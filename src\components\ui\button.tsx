
/**
 * Enhanced <PERSON><PERSON> Component
 *
 * Updated to use the new design system while maintaining backward compatibility.
 * Integrates with the unified design tokens and variant system.
 */

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  [
    // Base styles using design system patterns
    "inline-flex items-center justify-center gap-2 whitespace-nowrap",
    "font-medium transition-all duration-normal",
    "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",
    "disabled:pointer-events-none disabled:opacity-50",
    "select-none",
    "[&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  ],
  {
    variants: {
      variant: {
        // Primary variant with enhanced hover effects
        default: [
          "bg-primary text-primary-foreground rounded-md",
          "hover:bg-primary-hover hover:shadow-md hover:-translate-y-0.5",
          "active:translate-y-0 active:shadow-sm",
          "focus-visible:ring-primary",
        ],
        // Updated to use design system colors
        primary: [
          "bg-primary text-primary-foreground rounded-md",
          "hover:bg-primary-hover hover:shadow-md hover:-translate-y-0.5",
          "active:translate-y-0 active:shadow-sm",
          "focus-visible:ring-primary",
        ],
        secondary: [
          "bg-secondary text-secondary-foreground border border-border rounded-md",
          "hover:bg-secondary/80 hover:border-border/80",
          "focus-visible:ring-secondary",
        ],
        destructive: [
          "bg-destructive text-destructive-foreground rounded-md",
          "hover:bg-destructive/90 hover:shadow-md",
          "focus-visible:ring-destructive",
        ],
        success: [
          "bg-success text-success-foreground rounded-md",
          "hover:bg-success/90 hover:shadow-md",
          "focus-visible:ring-success",
        ],
        warning: [
          "bg-warning text-warning-foreground rounded-md",
          "hover:bg-warning/90 hover:shadow-md",
          "focus-visible:ring-warning",
        ],
        outline: [
          "border border-border bg-background text-foreground rounded-md",
          "hover:bg-accent hover:text-accent-foreground",
          "focus-visible:ring-ring",
        ],
        ghost: [
          "text-foreground rounded-md",
          "hover:bg-accent hover:text-accent-foreground",
          "focus-visible:ring-ring",
        ],
        link: [
          "text-primary underline-offset-4",
          "hover:underline",
          "focus-visible:ring-primary",
        ],
      },
      size: {
        xs: "h-6 px-2 text-xs rounded-sm",
        sm: "h-8 px-3 text-sm rounded-md",
        default: "h-10 px-4 text-sm",
        md: "h-10 px-4 text-sm",
        lg: "h-12 px-6 text-base rounded-lg",
        xl: "h-14 px-8 text-lg rounded-lg",
        icon: "h-10 w-10",
      },
      fullWidth: {
        true: "w-full",
        false: "w-auto",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      fullWidth: false,
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      fullWidth,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      asChild = false,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button"
    const isDisabled = disabled || loading

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, fullWidth }), className)}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        {!loading && leftIcon && (
          <span className="mr-2 flex items-center">{leftIcon}</span>
        )}
        {loading && loadingText ? loadingText : children}
        {!loading && rightIcon && (
          <span className="ml-2 flex items-center">{rightIcon}</span>
        )}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
