import { useState, useCallback, useEffect } from 'react';
import { LangChainIntegration } from '@/services/langchain/langchainIntegration';
import { LangFlowIntegration, LangFlowWorkflow, LangFlowExecutionResult } from '@/services/langflow/langflowIntegration';
import { AnalysisResult } from '@/types/conversation';
import { AnalysisCluster, ChatSimulation } from '@/components/visual-analysis/types';

/**
 * Integration status
 */
export type IntegrationStatus = 'idle' | 'loading' | 'success' | 'error';

/**
 * Hook for LangChain and LangFlow integration with Living Data Canvas
 */
export const useLangChainIntegration = () => {
  const [langChain] = useState(() => new LangChainIntegration());
  const [langFlow] = useState(() => new LangFlowIntegration());
  
  const [status, setStatus] = useState<IntegrationStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [isLangFlowAvailable, setIsLangFlowAvailable] = useState(false);

  // Check LangFlow availability on mount
  useEffect(() => {
    const checkLangFlow = async () => {
      const available = await langFlow.checkStatus();
      setIsLangFlowAvailable(available);
    };
    
    checkLangFlow();
  }, [langFlow]);

  /**
   * Generate insights for analysis results using LangChain
   */
  const generateAnalysisInsights = useCallback(async (analysisResults: AnalysisResult[]) => {
    setStatus('loading');
    setError(null);

    try {
      const insights = await langChain.generateAnalysisInsights(analysisResults);
      setStatus('success');
      return insights;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate insights';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langChain]);

  /**
   * Generate cluster insights using LangChain
   */
  const generateClusterInsights = useCallback(async (
    cluster: AnalysisCluster, 
    analysisResults: AnalysisResult[]
  ) => {
    setStatus('loading');
    setError(null);

    try {
      const insights = await langChain.generateClusterInsights(cluster, analysisResults);
      setStatus('success');
      return insights;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate cluster insights';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langChain]);

  /**
   * Generate simulation prompts using LangChain
   */
  const generateSimulationPrompts = useCallback(async (
    sourceAnalyses: AnalysisResult[], 
    simulationType: string = 'exploration'
  ) => {
    setStatus('loading');
    setError(null);

    try {
      const prompts = await langChain.generateSimulationPrompts(sourceAnalyses, simulationType);
      setStatus('success');
      return prompts;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate simulation prompts';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langChain]);

  /**
   * Analyze connections between analysis records
   */
  const analyzeConnections = useCallback(async (
    sourceAnalysis: AnalysisResult, 
    targetAnalysis: AnalysisResult
  ) => {
    setStatus('loading');
    setError(null);

    try {
      const connectionAnalysis = await langChain.analyzeConnections(sourceAnalysis, targetAnalysis);
      setStatus('success');
      return connectionAnalysis;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to analyze connections';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langChain]);

  /**
   * Generate follow-up questions using LangChain
   */
  const generateFollowUpQuestions = useCallback(async (analysisResult: AnalysisResult) => {
    setStatus('loading');
    setError(null);

    try {
      const questions = await langChain.generateFollowUpQuestions(analysisResult);
      setStatus('success');
      return questions;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate follow-up questions';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langChain]);

  /**
   * Summarize simulation results using LangChain
   */
  const summarizeSimulationResults = useCallback(async (simulation: ChatSimulation) => {
    setStatus('loading');
    setError(null);

    try {
      const summary = await langChain.summarizeSimulationResults(simulation);
      setStatus('success');
      return summary;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to summarize simulation results';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langChain]);

  /**
   * Generate comprehensive canvas insights
   */
  const generateCanvasInsights = useCallback(async (
    analysisResults: AnalysisResult[],
    clusters: AnalysisCluster[],
    simulations: ChatSimulation[]
  ) => {
    setStatus('loading');
    setError(null);

    try {
      const insights = await langChain.generateCanvasInsights(analysisResults, clusters, simulations);
      setStatus('success');
      return insights;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate canvas insights';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langChain]);

  /**
   * Create and execute LangFlow workflow for analysis
   */
  const executeAnalysisWorkflow = useCallback(async (analysisResults: AnalysisResult[]) => {
    if (!isLangFlowAvailable) {
      throw new Error('LangFlow is not available');
    }

    setStatus('loading');
    setError(null);

    try {
      const workflow = langFlow.createAnalysisWorkflow(analysisResults);
      const result = await langFlow.executeWorkflow(workflow);
      
      if (!result.success) {
        throw new Error(result.error || 'Workflow execution failed');
      }

      setStatus('success');
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to execute analysis workflow';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langFlow, isLangFlowAvailable]);

  /**
   * Create and execute LangFlow workflow for cluster analysis
   */
  const executeClusterWorkflow = useCallback(async (
    cluster: AnalysisCluster, 
    analysisResults: AnalysisResult[]
  ) => {
    if (!isLangFlowAvailable) {
      throw new Error('LangFlow is not available');
    }

    setStatus('loading');
    setError(null);

    try {
      const workflow = langFlow.createClusterWorkflow(cluster, analysisResults);
      const result = await langFlow.executeWorkflow(workflow);
      
      if (!result.success) {
        throw new Error(result.error || 'Cluster workflow execution failed');
      }

      setStatus('success');
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to execute cluster workflow';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langFlow, isLangFlowAvailable]);

  /**
   * Create and execute LangFlow workflow for simulation
   */
  const executeSimulationWorkflow = useCallback(async (simulation: ChatSimulation) => {
    if (!isLangFlowAvailable) {
      throw new Error('LangFlow is not available');
    }

    setStatus('loading');
    setError(null);

    try {
      const workflow = langFlow.createSimulationWorkflow(simulation);
      const result = await langFlow.executeWorkflow(workflow);
      
      if (!result.success) {
        throw new Error(result.error || 'Simulation workflow execution failed');
      }

      setStatus('success');
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to execute simulation workflow';
      setError(errorMessage);
      setStatus('error');
      throw err;
    }
  }, [langFlow, isLangFlowAvailable]);

  /**
   * Save workflow to LangFlow
   */
  const saveWorkflow = useCallback(async (workflow: LangFlowWorkflow) => {
    if (!isLangFlowAvailable) {
      throw new Error('LangFlow is not available');
    }

    try {
      const success = await langFlow.saveWorkflow(workflow);
      if (!success) {
        throw new Error('Failed to save workflow');
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save workflow';
      setError(errorMessage);
      throw err;
    }
  }, [langFlow, isLangFlowAvailable]);

  /**
   * Load workflow from LangFlow
   */
  const loadWorkflow = useCallback(async (workflowId: string) => {
    if (!isLangFlowAvailable) {
      throw new Error('LangFlow is not available');
    }

    try {
      const workflow = await langFlow.loadWorkflow(workflowId);
      if (!workflow) {
        throw new Error('Workflow not found');
      }
      return workflow;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load workflow';
      setError(errorMessage);
      throw err;
    }
  }, [langFlow, isLangFlowAvailable]);

  /**
   * Update LangChain model configuration
   */
  const updateLangChainModel = useCallback((model: string, temperature: number = 0.7) => {
    langChain.updateModel(model, temperature);
  }, [langChain]);

  /**
   * Update LangFlow configuration
   */
  const updateLangFlowConfig = useCallback(async (baseUrl: string, apiKey?: string) => {
    langFlow.updateConfig(baseUrl, apiKey);
    const available = await langFlow.checkStatus();
    setIsLangFlowAvailable(available);
  }, [langFlow]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
    setStatus('idle');
  }, []);

  return {
    // Status
    status,
    error,
    isLangFlowAvailable,
    isLoading: status === 'loading',
    
    // LangChain functions
    generateAnalysisInsights,
    generateClusterInsights,
    generateSimulationPrompts,
    analyzeConnections,
    generateFollowUpQuestions,
    summarizeSimulationResults,
    generateCanvasInsights,
    
    // LangFlow functions
    executeAnalysisWorkflow,
    executeClusterWorkflow,
    executeSimulationWorkflow,
    saveWorkflow,
    loadWorkflow,
    
    // Configuration
    updateLangChainModel,
    updateLangFlowConfig,
    
    // Utilities
    clearError
  };
};
