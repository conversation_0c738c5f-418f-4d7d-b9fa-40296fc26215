const fs = require('fs');
const path = require('path');

/**
 * Bundle Analysis Script
 * Analyzes the build output and provides insights on chunk sizes and optimization
 */

const DIST_DIR = path.join(__dirname, '..', 'dist', 'assets');
const MAX_CHUNK_SIZE = 500 * 1024; // 500KB
const GZIP_RATIO_THRESHOLD = 0.4; // Good gzip ratio

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundle() {
  if (!fs.existsSync(DIST_DIR)) {
    console.log('❌ Build directory not found. Run "npm run build" first.');
    return;
  }

  const files = fs.readdirSync(DIST_DIR)
    .filter(file => file.endsWith('.js'))
    .map(file => {
      const filePath = path.join(DIST_DIR, file);
      const stats = fs.statSync(filePath);
      const size = stats.size;
      
      // Extract chunk type from filename
      let chunkType = 'unknown';
      if (file.includes('vendor-')) {
        chunkType = 'vendor';
      } else if (file.includes('conversation-planner')) {
        chunkType = 'feature';
      } else if (file.includes('historical-analysis') || file.includes('canvas-visualization') || file.includes('chatbot-features')) {
        chunkType = 'feature';
      } else if (file.includes('index-') || file.includes('Index-')) {
        chunkType = 'entry';
      } else {
        chunkType = 'other';
      }

      return {
        name: file,
        size,
        chunkType,
        isLarge: size > MAX_CHUNK_SIZE
      };
    })
    .sort((a, b) => b.size - a.size);

  console.log('📊 Bundle Analysis Report');
  console.log('========================\n');

  // Overall stats
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  const largeChunks = files.filter(file => file.isLarge);

  console.log(`📦 Total Bundle Size: ${formatBytes(totalSize)}`);
  console.log(`📁 Total Chunks: ${files.length}`);
  console.log(`⚠️  Large Chunks (>${formatBytes(MAX_CHUNK_SIZE)}): ${largeChunks.length}\n`);

  // Chunk breakdown by type
  const chunksByType = files.reduce((acc, file) => {
    if (!acc[file.chunkType]) {
      acc[file.chunkType] = { count: 0, totalSize: 0, files: [] };
    }
    acc[file.chunkType].count++;
    acc[file.chunkType].totalSize += file.size;
    acc[file.chunkType].files.push(file);
    return acc;
  }, {});

  console.log('📋 Chunks by Type:');
  Object.entries(chunksByType).forEach(([type, data]) => {
    console.log(`  ${type.toUpperCase()}: ${data.count} chunks, ${formatBytes(data.totalSize)}`);
  });
  console.log();

  // Top 10 largest chunks
  console.log('🔝 Top 10 Largest Chunks:');
  files.slice(0, 10).forEach((file, index) => {
    const status = file.isLarge ? '⚠️ ' : '✅';
    console.log(`  ${index + 1}. ${status} ${file.name} - ${formatBytes(file.size)}`);
  });
  console.log();

  // Recommendations
  console.log('💡 Recommendations:');
  
  if (largeChunks.length === 0) {
    console.log('  ✅ All chunks are optimally sized!');
  } else {
    console.log(`  ⚠️  ${largeChunks.length} chunks exceed ${formatBytes(MAX_CHUNK_SIZE)}:`);
    largeChunks.forEach(chunk => {
      console.log(`     - ${chunk.name}: Consider further splitting or lazy loading`);
    });
  }

  // Check for vendor chunk optimization
  const vendorChunks = files.filter(f => f.chunkType === 'vendor');
  if (vendorChunks.length > 0) {
    console.log(`  📚 Vendor chunks: ${vendorChunks.length} chunks, good for caching`);
  }

  // Check for feature chunk balance
  const featureChunks = files.filter(f => f.chunkType === 'feature');
  if (featureChunks.length > 0) {
    const avgFeatureSize = featureChunks.reduce((sum, f) => sum + f.size, 0) / featureChunks.length;
    console.log(`  🧩 Feature chunks: ${featureChunks.length} chunks, avg ${formatBytes(avgFeatureSize)}`);
  }

  console.log('\n🚀 Performance Tips:');
  console.log('  - Vendor chunks are cached separately for better performance');
  console.log('  - Feature chunks load on-demand with lazy loading');
  console.log('  - Consider preloading critical chunks for better UX');
  console.log('  - Monitor bundle size when adding new dependencies');
}

if (require.main === module) {
  analyzeBundle();
}

module.exports = { analyzeBundle };
