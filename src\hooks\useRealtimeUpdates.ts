import { useEffect, useCallback, useState } from 'react';
import { 
  websocketService, 
  RealtimeEvent, 
  RealtimeEventType, 
  ConnectionStatus,
  RealtimeEventEmitters
} from '@/services/realtime/websocketService';
import { AnalysisResult } from '@/types/conversation';
import { AnalysisCluster, ChatSimulation } from '@/components/visual-analysis/types';

/**
 * Real-time update handlers
 */
export interface RealtimeUpdateHandlers {
  onAnalysisCreated?: (analysis: AnalysisResult) => void;
  onAnalysisUpdated?: (analysis: AnalysisResult) => void;
  onAnalysisDeleted?: (analysisId: string) => void;
  onClusterCreated?: (cluster: AnalysisCluster) => void;
  onClusterUpdated?: (cluster: AnalysisCluster) => void;
  onClusterDeleted?: (clusterId: string) => void;
  onSimulationCreated?: (simulation: ChatSimulation) => void;
  onSimulationUpdated?: (simulation: ChatSimulation) => void;
  onSimulationProgress?: (progress: {
    simulationId: string;
    currentPrompt: number;
    totalPrompts: number;
    percentage: number;
    currentResult?: any;
  }) => void;
  onSimulationCompleted?: (simulation: ChatSimulation) => void;
  onCanvasStateUpdated?: (canvasState: any) => void;
  onUserJoined?: (user: { id: string; name: string }) => void;
  onUserLeft?: (user: { id: string; name: string }) => void;
}

/**
 * Hook for managing real-time updates
 */
export const useRealtimeUpdates = (handlers: RealtimeUpdateHandlers = {}) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastEvent, setLastEvent] = useState<RealtimeEvent | null>(null);
  const [connectedUsers, setConnectedUsers] = useState<{ id: string; name: string }[]>([]);

  /**
   * Connect to real-time service
   */
  const connect = useCallback(async () => {
    if (isConnecting || connectionStatus === 'connected') return;
    
    setIsConnecting(true);
    try {
      await websocketService.connect();
    } catch (error) {
      console.error('Failed to connect to real-time service:', error);
    } finally {
      setIsConnecting(false);
    }
  }, [isConnecting, connectionStatus]);

  /**
   * Disconnect from real-time service
   */
  const disconnect = useCallback(() => {
    websocketService.disconnect();
  }, []);

  /**
   * Send real-time event
   */
  const sendEvent = useCallback((type: RealtimeEventType, data: any) => {
    websocketService.send({ type, data });
  }, []);

  /**
   * Event emitters for convenience
   */
  const emitters = {
    emitAnalysisCreated: RealtimeEventEmitters.emitAnalysisCreated,
    emitAnalysisUpdated: RealtimeEventEmitters.emitAnalysisUpdated,
    emitAnalysisDeleted: RealtimeEventEmitters.emitAnalysisDeleted,
    emitClusterCreated: RealtimeEventEmitters.emitClusterCreated,
    emitClusterUpdated: RealtimeEventEmitters.emitClusterUpdated,
    emitClusterDeleted: RealtimeEventEmitters.emitClusterDeleted,
    emitSimulationProgress: RealtimeEventEmitters.emitSimulationProgress,
    emitSimulationCompleted: RealtimeEventEmitters.emitSimulationCompleted,
  };

  // Set up event listeners
  useEffect(() => {
    const unsubscribers: (() => void)[] = [];

    // Connection status listener
    const statusUnsubscriber = websocketService.onConnectionStatus(setConnectionStatus);
    unsubscribers.push(statusUnsubscriber);

    // Analysis events
    if (handlers.onAnalysisCreated) {
      const unsubscriber = websocketService.on('analysis_created', (event) => {
        handlers.onAnalysisCreated!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    if (handlers.onAnalysisUpdated) {
      const unsubscriber = websocketService.on('analysis_updated', (event) => {
        handlers.onAnalysisUpdated!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    if (handlers.onAnalysisDeleted) {
      const unsubscriber = websocketService.on('analysis_deleted', (event) => {
        handlers.onAnalysisDeleted!(event.data.id);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    // Cluster events
    if (handlers.onClusterCreated) {
      const unsubscriber = websocketService.on('cluster_created', (event) => {
        handlers.onClusterCreated!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    if (handlers.onClusterUpdated) {
      const unsubscriber = websocketService.on('cluster_updated', (event) => {
        handlers.onClusterUpdated!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    if (handlers.onClusterDeleted) {
      const unsubscriber = websocketService.on('cluster_deleted', (event) => {
        handlers.onClusterDeleted!(event.data.id);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    // Simulation events
    if (handlers.onSimulationCreated) {
      const unsubscriber = websocketService.on('simulation_created', (event) => {
        handlers.onSimulationCreated!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    if (handlers.onSimulationUpdated) {
      const unsubscriber = websocketService.on('simulation_updated', (event) => {
        handlers.onSimulationUpdated!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    if (handlers.onSimulationProgress) {
      const unsubscriber = websocketService.on('simulation_progress', (event) => {
        handlers.onSimulationProgress!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    if (handlers.onSimulationCompleted) {
      const unsubscriber = websocketService.on('simulation_completed', (event) => {
        handlers.onSimulationCompleted!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    // Canvas state events
    if (handlers.onCanvasStateUpdated) {
      const unsubscriber = websocketService.on('canvas_state_updated', (event) => {
        handlers.onCanvasStateUpdated!(event.data);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    // User presence events
    if (handlers.onUserJoined) {
      const unsubscriber = websocketService.on('user_joined', (event) => {
        handlers.onUserJoined!(event.data);
        setConnectedUsers(prev => [...prev, event.data]);
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    if (handlers.onUserLeft) {
      const unsubscriber = websocketService.on('user_left', (event) => {
        handlers.onUserLeft!(event.data);
        setConnectedUsers(prev => prev.filter(user => user.id !== event.data.id));
        setLastEvent(event);
      });
      unsubscribers.push(unsubscriber);
    }

    // Cleanup function
    return () => {
      unsubscribers.forEach(unsubscriber => unsubscriber());
    };
  }, [handlers]);

  // Auto-connect on mount
  useEffect(() => {
    connect();
    
    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    connectionStatus,
    isConnecting,
    lastEvent,
    connectedUsers,
    connect,
    disconnect,
    sendEvent,
    emitters,
    isConnected: connectionStatus === 'connected',
    isDisconnected: connectionStatus === 'disconnected',
    isReconnecting: connectionStatus === 'reconnecting',
    hasError: connectionStatus === 'error'
  };
};

/**
 * Hook specifically for canvas real-time updates
 */
export const useCanvasRealtimeUpdates = () => {
  const [realtimeUpdates, setRealtimeUpdates] = useState<{
    analysisResults: AnalysisResult[];
    clusters: AnalysisCluster[];
    simulations: ChatSimulation[];
  }>({
    analysisResults: [],
    clusters: [],
    simulations: []
  });

  const handlers: RealtimeUpdateHandlers = {
    onAnalysisCreated: (analysis) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        analysisResults: [...prev.analysisResults, analysis]
      }));
    },
    onAnalysisUpdated: (analysis) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        analysisResults: prev.analysisResults.map(a => 
          a.id === analysis.id ? analysis : a
        )
      }));
    },
    onAnalysisDeleted: (analysisId) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        analysisResults: prev.analysisResults.filter(a => a.id !== analysisId)
      }));
    },
    onClusterCreated: (cluster) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        clusters: [...prev.clusters, cluster]
      }));
    },
    onClusterUpdated: (cluster) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        clusters: prev.clusters.map(c => 
          c.id === cluster.id ? cluster : c
        )
      }));
    },
    onClusterDeleted: (clusterId) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        clusters: prev.clusters.filter(c => c.id !== clusterId)
      }));
    },
    onSimulationCreated: (simulation) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        simulations: [...prev.simulations, simulation]
      }));
    },
    onSimulationUpdated: (simulation) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        simulations: prev.simulations.map(s => 
          s.id === simulation.id ? simulation : s
        )
      }));
    },
    onSimulationCompleted: (simulation) => {
      setRealtimeUpdates(prev => ({
        ...prev,
        simulations: prev.simulations.map(s => 
          s.id === simulation.id ? simulation : s
        )
      }));
    }
  };

  const realtimeService = useRealtimeUpdates(handlers);

  return {
    ...realtimeService,
    realtimeUpdates,
    clearUpdates: () => setRealtimeUpdates({
      analysisResults: [],
      clusters: [],
      simulations: []
    })
  };
};
