import React, { useState, useEffect } from 'react';
import { LivingDataCanvasContainer } from './LivingDataCanvas';
import LivingDataCanvasSafe from './LivingDataCanvasSafe';

interface Props {
  // No props needed since both containers use sample data
}

interface ErrorInfo {
  componentStack: string;
  errorBoundary: string;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  usesSafeMode: boolean;
}

const LivingDataCanvasWithFallback: React.FC<Props> = () => {
  const [state, setState] = useState<State>({
    hasError: false,
    usesSafeMode: false
  });

  // Add global error listener to catch runtime errors
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      if (event.error && (
        event.error.message?.includes('count') ||
        event.error.message?.includes('LineMaterial') ||
        event.error.message?.includes('Line2') ||
        event.error.message?.includes('LineGeometry')
      )) {
        console.warn('Line material error detected, switching to safe mode:', event.error);
        setState(prev => ({
          ...prev,
          hasError: true,
          error: event.error,
          usesSafeMode: true
        }));
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason && typeof event.reason === 'object' && 
          (event.reason.message?.includes('count') || 
           event.reason.message?.includes('LineMaterial'))) {
        console.warn('Promise rejection with line material error, switching to safe mode:', event.reason);
        setState(prev => ({
          ...prev,
          hasError: true,
          error: event.reason,
          usesSafeMode: true
        }));
      }
    };

    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  const handleError = (error: Error, errorInfo?: ErrorInfo) => {
    console.warn('Canvas error detected, switching to safe mode:', error);
    setState({
      hasError: true,
      error,
      errorInfo,
      usesSafeMode: true
    });
  };

  const resetToEnhanced = () => {
    setState({
      hasError: false,
      usesSafeMode: false
    });
  };

  if (state.hasError || state.usesSafeMode) {
    return (
      <div>
        {state.hasError && (
          <div style={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            background: 'rgba(255, 165, 0, 0.9)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '4px',
            fontSize: '14px',
            zIndex: 1000,
            maxWidth: '400px'
          }}>
            ⚠️ Advanced rendering failed. Using safe mode.
            <button 
              onClick={resetToEnhanced}
              style={{
                marginLeft: '8px',
                background: 'transparent',
                border: '1px solid white',
                color: 'white',
                padding: '2px 8px',
                borderRadius: '2px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Retry Enhanced
            </button>
          </div>
        )}        <CanvasErrorBoundary onError={handleError}>
          <LivingDataCanvasSafe />
        </CanvasErrorBoundary>
      </div>
    );
  }
  return (
    <CanvasErrorBoundary onError={handleError}>
      <LivingDataCanvasContainer />
    </CanvasErrorBoundary>
  );
};

interface CanvasErrorBoundaryProps {
  children: React.ReactNode;
  onError: (error: Error, errorInfo?: ErrorInfo) => void;
}

class CanvasErrorBoundary extends React.Component<
  CanvasErrorBoundaryProps,
  { hasError: boolean }
> {
  constructor(props: CanvasErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.props.onError(error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: 'CanvasErrorBoundary'
    });
  }

  render() {
    if (this.state.hasError) {
      return null; // Let parent component handle the fallback
    }

    return this.props.children;
  }
}

export default LivingDataCanvasWithFallback;
