
import React from 'react';
import { motion } from 'framer-motion';
import { ParsedCharacterAnalysis } from '@/services/openRouter/types/parsedOutput';
import { FormattedContentDisplay } from './ContentFormatter';
import { ActionableListSection } from './ActionableListSection';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  MessageSquare,
  Swords,
  Search,
  User,
  ArrowRight,
  Quote
} from 'lucide-react';

interface CharacterAnalysisDisplayProps {
  analysis: ParsedCharacterAnalysis;
  onFollowUpQuestion: (question: string) => void;
  characterName?: string;
  onItemSelectForChat?: (item: string) => void;
  selectedItems?: string[];
  onItemToggleSelect?: (item: string) => void;
  visualMode?: boolean;
  characterAvatar?: string;
}

export const CharacterAnalysisDisplay: React.FC<CharacterAnalysisDisplayProps> = ({
  analysis,
  onFollowUpQuestion,
  characterName,
  onItemSelectForChat,
  selectedItems,
  onItemToggleSelect,
  visualMode = false,
  characterAvatar
}) => {
  const retortTitle = characterName ? `Possible Retorts from ${characterName}` : 'Possible Retorts';
  const inquiryTitle = characterName ? `Further Inquiries from ${characterName}` : 'Further Inquiries';
  const handleItemSelect = onItemSelectForChat ?? onFollowUpQuestion;

  if (visualMode) {
    return (
      <div className="space-y-6">
        {/* Character Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-4">
                <Avatar className="w-12 h-12 border-2 border-blue-300">
                  <AvatarFallback className="bg-blue-100 text-blue-700 font-semibold">
                    {characterName ? characterName.charAt(0).toUpperCase() : <User className="w-6 h-6" />}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-xl font-bold text-blue-800">
                    {characterName ? `${characterName}'s Response` : 'Character Response'}
                  </h3>
                  <Badge variant="secondary" className="mt-1 bg-blue-100 text-blue-700">
                    Character Analysis
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <Quote className="absolute -top-2 -left-2 w-6 h-6 text-blue-300" />
                <div className="pl-6">
                  <FormattedContentDisplay content={analysis.response} />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Flow Indicator */}
        <div className="flex justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="flex items-center gap-2 text-gray-500"
          >
            <ArrowRight className="w-5 h-5" />
            <span className="text-sm font-medium">Potential Reactions</span>
            <ArrowRight className="w-5 h-5" />
          </motion.div>
        </div>

        {/* Interactive Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="border-orange-200 bg-orange-50/50 h-full">
              <CardHeader className="bg-orange-100 pb-3">
                <CardTitle className="flex items-center gap-2 text-orange-800">
                  <Swords className="w-5 h-5" />
                  {retortTitle}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <p className="text-sm text-orange-700 mb-4">
                  How they might counter or react to your line of questioning.
                </p>
                <div className="space-y-2">
                  {(analysis.possibleResponses || []).map((response, index) => (
                    <motion.button
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 + index * 0.1 }}
                      onClick={() => handleItemSelect(response)}
                      className={`w-full text-left p-3 rounded-lg border transition-all duration-200 text-sm ${
                        selectedItems?.includes(response)
                          ? 'border-orange-300 bg-orange-100 shadow-sm'
                          : 'border-orange-200 bg-white hover:border-orange-300 hover:bg-orange-50'
                      }`}
                    >
                      {response}
                    </motion.button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="border-green-200 bg-green-50/50 h-full">
              <CardHeader className="bg-green-100 pb-3">
                <CardTitle className="flex items-center gap-2 text-green-800">
                  <Search className="w-5 h-5" />
                  {inquiryTitle}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <p className="text-sm text-green-700 mb-4">
                  Questions they might ask to gather more information or challenge you.
                </p>
                <div className="space-y-2">
                  {(analysis.followUpQuestions || []).map((question, index) => (
                    <motion.button
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      onClick={() => handleItemSelect(question)}
                      className={`w-full text-left p-3 rounded-lg border transition-all duration-200 text-sm ${
                        selectedItems?.includes(question)
                          ? 'border-green-300 bg-green-100 shadow-sm'
                          : 'border-green-200 bg-white hover:border-green-300 hover:bg-green-50'
                      }`}
                    >
                      {question}
                    </motion.button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  // Traditional layout
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2 flex items-center text-foreground">
          <MessageSquare className="h-5 w-5 mr-3 text-primary" />
          Response {characterName && `from ${characterName}`}
        </h3>
        <FormattedContentDisplay content={analysis.response} />
      </div>

      <ActionableListSection
        title={retortTitle}
        description="How they might counter or react to your line of questioning."
        items={analysis.possibleResponses || []}
        onItemSelect={handleItemSelect}
        IconComponent={Swords}
        isItemSelected={(item) => selectedItems?.includes(item) ?? false}
        onItemToggleSelect={onItemToggleSelect}
      />

      <ActionableListSection
        title={inquiryTitle}
        description="Questions they might ask to gather more information or challenge you."
        items={analysis.followUpQuestions || []}
        onItemSelect={handleItemSelect}
        IconComponent={Search}
        isItemSelected={(item) => selectedItems?.includes(item) ?? false}
        onItemToggleSelect={onItemToggleSelect}
      />
    </div>
  );
};
