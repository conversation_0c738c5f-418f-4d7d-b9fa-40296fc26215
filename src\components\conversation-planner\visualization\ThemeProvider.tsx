import React, { createContext, useContext, useEffect, useState } from 'react';
import { ThemeConfig, getTheme, applyThemeToElement } from './themes';

interface ThemeContextType {
  currentTheme: ThemeConfig;
  themeName: string;
  setTheme: (themeName: string) => void;
  availableThemes: string[];
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'default'
}) => {
  const [themeName, setThemeName] = useState(defaultTheme);
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>(getTheme(defaultTheme));

  const availableThemes = ['default', 'dark', 'colorful', 'minimal'];

  useEffect(() => {
    // Load saved theme from localStorage
    const savedTheme = localStorage.getItem('visualization-theme');
    if (savedTheme && availableThemes.includes(savedTheme)) {
      setThemeName(savedTheme);
      setCurrentTheme(getTheme(savedTheme));
    }
  }, []);

  useEffect(() => {
    // Apply theme to document root
    const root = document.documentElement;
    applyThemeToElement(root, currentTheme);
    
    // Save theme to localStorage
    localStorage.setItem('visualization-theme', themeName);
  }, [currentTheme, themeName]);

  const setTheme = (newThemeName: string) => {
    if (availableThemes.includes(newThemeName)) {
      setThemeName(newThemeName);
      setCurrentTheme(getTheme(newThemeName));
    }
  };

  const contextValue: ThemeContextType = {
    currentTheme,
    themeName,
    setTheme,
    availableThemes
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Higher-order component for theme-aware components
export const withTheme = <P extends object>(
  Component: React.ComponentType<P & { theme: ThemeConfig }>
) => {
  return (props: P) => {
    const { currentTheme } = useTheme();
    return <Component {...props} theme={currentTheme} />;
  };
};

// Hook for theme-aware styling
export const useThemedStyles = () => {
  const { currentTheme } = useTheme();
  
  return {
    getCardStyle: (variant: 'primary' | 'secondary' | 'accent' = 'primary') => ({
      backgroundColor: variant === 'primary' ? currentTheme.colors.surface : 
                      variant === 'secondary' ? currentTheme.colors.secondary : 
                      currentTheme.colors.accent,
      borderColor: currentTheme.colors.border,
      color: currentTheme.colors.text,
      boxShadow: currentTheme.shadows.md,
      borderRadius: currentTheme.borderRadius.md
    }),
    
    getButtonStyle: (variant: 'primary' | 'secondary' | 'accent' = 'primary') => ({
      backgroundColor: variant === 'primary' ? currentTheme.colors.primary : 
                      variant === 'secondary' ? currentTheme.colors.secondary : 
                      currentTheme.colors.accent,
      color: variant === 'secondary' ? currentTheme.colors.text : '#ffffff',
      borderRadius: currentTheme.borderRadius.sm,
      boxShadow: currentTheme.shadows.sm
    }),
    
    getTextStyle: (variant: 'primary' | 'secondary' = 'primary') => ({
      color: variant === 'primary' ? currentTheme.colors.text : currentTheme.colors.textSecondary
    }),
    
    getBorderStyle: () => ({
      borderColor: currentTheme.colors.border
    }),
    
    getGradientStyle: (variant: 'primary' | 'secondary' | 'accent' = 'primary') => ({
      background: variant === 'primary' ? currentTheme.gradients.primary : 
                  variant === 'secondary' ? currentTheme.gradients.secondary : 
                  currentTheme.gradients.accent
    })
  };
};

// Theme-aware CSS class generator
export const useThemedClasses = () => {
  const { currentTheme } = useTheme();
  
  return {
    card: `bg-[${currentTheme.colors.surface}] border-[${currentTheme.colors.border}] text-[${currentTheme.colors.text}] rounded-[${currentTheme.borderRadius.md}]`,
    button: {
      primary: `bg-[${currentTheme.colors.primary}] text-white rounded-[${currentTheme.borderRadius.sm}]`,
      secondary: `bg-[${currentTheme.colors.secondary}] text-[${currentTheme.colors.text}] rounded-[${currentTheme.borderRadius.sm}]`,
      accent: `bg-[${currentTheme.colors.accent}] text-white rounded-[${currentTheme.borderRadius.sm}]`
    },
    text: {
      primary: `text-[${currentTheme.colors.text}]`,
      secondary: `text-[${currentTheme.colors.textSecondary}]`
    },
    border: `border-[${currentTheme.colors.border}]`,
    background: `bg-[${currentTheme.colors.background}]`,
    surface: `bg-[${currentTheme.colors.surface}]`
  };
};
