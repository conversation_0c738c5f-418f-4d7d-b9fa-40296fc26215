// Enterprise-grade chart utilities and data processing

export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    notation: 'compact',
    maximumFractionDigits: 1
  }).format(value);
};

export const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    notation: 'compact',
    maximumFractionDigits: 1
  }).format(value);
};

export const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1
  }).format(value / 100);
};

export interface DataPoint {
  x: number;
  y: number;
  value: number;
  date: string;
  tooltip?: string;
}

export interface MetricCard {
  id: number;
  value: string;
  label: string;
  change: string;
  type: 'positive' | 'negative' | 'neutral';
  icon: string;
}

export interface TimelineItem {
  date: string;
  title: string;
  content: string;
  important?: boolean;
}

export const generateDataPoints = (count: number, type: 'linear' | 'exponential' | 'sine' = 'linear'): DataPoint[] => {
  const points: DataPoint[] = [];
  const width = 680; // Chart width minus padding
  const height = 250; // Chart height
  
  for (let i = 0; i < count; i++) {
    const x = 60 + (i * width / (count - 1));
    let y: number;
    
    switch (type) {
      case 'exponential':
        y = height - (Math.pow(i / count, 2) * height * 0.8);
        break;
      case 'sine':
        y = height / 2 + Math.sin(i * 0.5) * height * 0.3;
        break;
      default:
        y = height - (i / count * height * 0.8) + Math.random() * 40;
    }
    
    const value = Math.floor(Math.random() * 1000) + 500;
    const date = new Date(2024, i, 1);
    
    points.push({
      x,
      y: Math.max(50, Math.min(300, y)),
      value,
      date: date.toLocaleDateString('en-US', { month: 'short' }),
      tooltip: `${date.toLocaleDateString('en-US', { month: 'long' })}: ${value} users`
    });
  }
  
  return points;
};

export const calculateTrend = (data: DataPoint[]): number => {
  if (data.length < 2) return 0;
  
  const firstValue = data[0].value;
  const lastValue = data[data.length - 1].value;
  
  return ((lastValue - firstValue) / firstValue) * 100;
};

export const smoothData = (data: DataPoint[], factor: number = 0.3): DataPoint[] => {
  const smoothed = [...data];
  
  for (let i = 1; i < smoothed.length - 1; i++) {
    const prev = data[i - 1].value;
    const current = data[i].value;
    const next = data[i + 1].value;
    
    smoothed[i] = {
      ...data[i],
      value: current * (1 - factor) + (prev + next) / 2 * factor
    };
  }
  
  return smoothed;
};

export const generateSampleMetrics = (): MetricCard[] => {
  return [
    { 
      id: 1, 
      value: '2.4M', 
      label: 'Total Users', 
      change: '+12%', 
      type: 'positive', 
      icon: '👥' 
    },
    { 
      id: 2, 
      value: '99.9%', 
      label: 'Uptime', 
      change: '+0.1%', 
      type: 'positive', 
      icon: '🔄' 
    },
    { 
      id: 3, 
      value: '$5.2M', 
      label: 'Revenue', 
      change: '+24%', 
      type: 'positive', 
      icon: '💰' 
    },
    { 
      id: 4, 
      value: '342ms', 
      label: 'Avg Response', 
      change: '-15%', 
      type: 'positive', 
      icon: '⚡' 
    }
  ];
};

export const generateSampleTimeline = (): TimelineItem[] => {
  return [
    { 
      date: '2024-06-15', 
      title: 'System Upgrade', 
      content: 'Major performance improvements deployed', 
      important: true 
    },
    { 
      date: '2024-06-10', 
      title: 'New Feature Launch', 
      content: 'Advanced analytics dashboard released' 
    },
    { 
      date: '2024-06-05', 
      title: 'Security Update', 
      content: 'Enhanced authentication protocols implemented' 
    },
    { 
      date: '2024-06-01', 
      title: 'Monthly Review', 
      content: 'Performance metrics exceeded targets by 15%' 
    }
  ];
};
