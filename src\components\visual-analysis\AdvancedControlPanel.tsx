import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Slider } from '../ui/slider';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import {
  Settings,
  Eye,
  EyeOff,
  Zap,
  Layout,
  Palette,
  HelpCircle,
  RotateCcw,
  Download,
  Upload,
  Maximize2,
  Minimize2,
  Play,
  Pause,
  SkipForward,
  Volume2,
  VolumeX,
  Monitor,
  Smartphone,
  Tablet,
  Gamepad2,
  Accessibility,
  Languages,
  Moon,
  Sun,
  Contrast,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Grid3X3,
  Layers,
  Filter,
  Search,
  Bookmark,
  Share2,
  Copy,
  ExternalLink
} from 'lucide-react';

interface AdvancedControlPanelProps {
  isVisible: boolean;
  onToggle: () => void;
  onLayoutChange: (algorithm: string, parameters: any) => void;
  onVisibilityToggle: (element: string, visible: boolean) => void;
  onThemeChange: (theme: string) => void;
  onExport: (format: string) => void;
  onImport: (file: File) => void;
  onReset: () => void;
  onAnimationToggle?: (enabled: boolean) => void;
  onSoundToggle?: (enabled: boolean) => void;
  onAccessibilityChange?: (setting: string, value: any) => void;
  onViewportChange?: (viewport: string) => void;
  onZoomChange?: (zoom: number) => void;
  onFilterChange?: (filters: Record<string, any>) => void;
  onBookmark?: () => void;
  onShare?: () => void;
  currentSettings: {
    layout: { algorithm: string; parameters: any };
    visibility: Record<string, boolean>;
    theme: string;
    performance: any;
    animation?: { enabled: boolean; speed: number };
    sound?: { enabled: boolean; volume: number };
    accessibility?: { highContrast: boolean; fontSize: number; reducedMotion: boolean };
    viewport?: string;
    zoom?: number;
    filters?: Record<string, any>;
  };
}

export const AdvancedControlPanel: React.FC<AdvancedControlPanelProps> = ({
  isVisible,
  onToggle,
  onLayoutChange,
  onVisibilityToggle,
  onThemeChange,
  onExport,
  onImport,
  onReset,
  onAnimationToggle,
  onSoundToggle,
  onAccessibilityChange,
  onViewportChange,
  onZoomChange,
  onFilterChange,
  onBookmark,
  onShare,
  currentSettings
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('layout');
  const [searchQuery, setSearchQuery] = useState('');

  const layoutAlgorithms = [
    { id: 'force-directed', name: 'Force Directed', description: 'Physics-based layout' },
    { id: 'grid', name: 'Grid', description: 'Organized grid layout' },
    { id: 'circular', name: 'Circular', description: 'Circular arrangement' },
    { id: 'hierarchical', name: 'Hierarchical', description: 'Tree-like structure' },
    { id: 'cluster', name: 'Cluster', description: 'Group-based layout' }
  ];

  const themes = [
    { id: 'dark', name: 'Dark', color: '#1a1a2e' },
    { id: 'light', name: 'Light', color: '#ffffff' },
    { id: 'blue', name: 'Ocean', color: '#0ea5e9' },
    { id: 'purple', name: 'Galaxy', color: '#8b5cf6' },
    { id: 'green', name: 'Forest', color: '#10b981' }
  ];

  const visibilityElements = [
    { id: 'nodes', name: 'Nodes', description: 'Show/hide all nodes', icon: <Eye className="w-3 h-3" /> },
    { id: 'connections', name: 'Connections', description: 'Show/hide connections', icon: <Zap className="w-3 h-3" /> },
    { id: 'clusters', name: 'Clusters', description: 'Show/hide cluster boundaries', icon: <Layers className="w-3 h-3" /> },
    { id: 'labels', name: 'Labels', description: 'Show/hide node labels', icon: <Languages className="w-3 h-3" /> },
    { id: 'background', name: 'Background Effects', description: 'Particle effects and gradients', icon: <Palette className="w-3 h-3" /> },
    { id: 'ui', name: 'UI Elements', description: 'Control panels and overlays', icon: <Monitor className="w-3 h-3" /> },
    { id: 'grid', name: 'Grid Lines', description: 'Show/hide alignment grid', icon: <Grid3X3 className="w-3 h-3" /> },
    { id: 'animations', name: 'Animations', description: 'Enable/disable animations', icon: <Play className="w-3 h-3" /> }
  ];

  const viewportOptions = [
    { id: 'desktop', name: 'Desktop', icon: <Monitor className="w-3 h-3" /> },
    { id: 'tablet', name: 'Tablet', icon: <Tablet className="w-3 h-3" /> },
    { id: 'mobile', name: 'Mobile', icon: <Smartphone className="w-3 h-3" /> }
  ];

  const filterOptions = [
    { id: 'analysisType', name: 'Analysis Type', options: ['all', 'multiple', 'deep', 'character', 'pros-cons'] },
    { id: 'rating', name: 'Rating', options: ['all', '5-star', '4-star', '3-star', '2-star', '1-star'] },
    { id: 'dateRange', name: 'Date Range', options: ['all', 'today', 'week', 'month', 'year'] },
    { id: 'model', name: 'AI Model', options: ['all', 'gpt-4', 'gpt-3.5-turbo', 'claude'] }
  ];

  const handleLayoutParameterChange = useCallback((parameter: string, value: number) => {
    const newParameters = {
      ...currentSettings.layout.parameters,
      [parameter]: value
    };
    onLayoutChange(currentSettings.layout.algorithm, newParameters);
  }, [currentSettings.layout, onLayoutChange]);

  const handleFileImport = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onImport(file);
    }
  }, [onImport]);

  if (!isVisible) {
    return (
      <Button
        onClick={onToggle}
        className="fixed top-4 right-4 z-20 bg-white/10 hover:bg-white/20 backdrop-blur-md border border-white/20"
        size="sm"
      >
        <Settings className="w-4 h-4" />
      </Button>
    );
  }

  return (
    <Card className="fixed top-4 right-4 z-20 w-80 bg-black/80 backdrop-blur-md border-white/20 text-white">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-base flex items-center">
            <Settings className="w-4 h-4 mr-2" />
            Advanced Controls
          </CardTitle>
          <div className="flex gap-1">
            <Button
              onClick={() => setIsExpanded(!isExpanded)}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/10"
            >
              {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </Button>
            <Button
              onClick={onToggle}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/10"
            >
              ×
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className={`${isExpanded ? 'max-h-96 overflow-y-auto' : 'max-h-64 overflow-y-auto'}`}>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-6 bg-white/10">
            <TabsTrigger value="layout" className="text-xs">Layout</TabsTrigger>
            <TabsTrigger value="visual" className="text-xs">Visual</TabsTrigger>
            <TabsTrigger value="filters" className="text-xs">Filters</TabsTrigger>
            <TabsTrigger value="accessibility" className="text-xs">A11y</TabsTrigger>
            <TabsTrigger value="data" className="text-xs">Data</TabsTrigger>
            <TabsTrigger value="help" className="text-xs">Help</TabsTrigger>
          </TabsList>

          <TabsContent value="layout" className="space-y-4 mt-4">
            <div>
              <Label className="text-sm font-medium">Layout Algorithm</Label>
              <div className="grid grid-cols-1 gap-2 mt-2">
                {layoutAlgorithms.map((algo) => (
                  <Button
                    key={algo.id}
                    onClick={() => onLayoutChange(algo.id, {})}
                    variant={currentSettings.layout.algorithm === algo.id ? "default" : "outline"}
                    className="justify-start text-left h-auto p-2"
                  >
                    <div>
                      <div className="font-medium text-xs">{algo.name}</div>
                      <div className="text-xs opacity-70">{algo.description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {currentSettings.layout.algorithm === 'force-directed' && (
              <div className="space-y-3">
                <div>
                  <Label className="text-xs">Strength: {currentSettings.layout.parameters?.strength || 0.5}</Label>
                  <Slider
                    value={[currentSettings.layout.parameters?.strength || 0.5]}
                    onValueChange={(value) => handleLayoutParameterChange('strength', value[0])}
                    min={0.1}
                    max={2.0}
                    step={0.1}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Distance: {currentSettings.layout.parameters?.distance || 100}</Label>
                  <Slider
                    value={[currentSettings.layout.parameters?.distance || 100]}
                    onValueChange={(value) => handleLayoutParameterChange('distance', value[0])}
                    min={50}
                    max={300}
                    step={10}
                    className="mt-1"
                  />
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="visual" className="space-y-4 mt-4">
            <div>
              <Label className="text-sm font-medium">Visibility Controls</Label>
              <div className="space-y-2 mt-2">
                {visibilityElements.map((element) => (
                  <div key={element.id} className="flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      {element.icon}
                      <div className="ml-2">
                        <div className="text-xs font-medium">{element.name}</div>
                        <div className="text-xs opacity-70">{element.description}</div>
                      </div>
                    </div>
                    <Switch
                      checked={currentSettings.visibility[element.id] !== false}
                      onCheckedChange={(checked) => onVisibilityToggle(element.id, checked)}
                    />
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Theme & Appearance</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {themes.map((theme) => (
                  <Button
                    key={theme.id}
                    onClick={() => onThemeChange(theme.id)}
                    variant={currentSettings.theme === theme.id ? "default" : "outline"}
                    className="justify-start h-8 text-xs"
                  >
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: theme.color }}
                    />
                    {theme.name}
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Viewport & Zoom</Label>
              <div className="space-y-3 mt-2">
                <div className="flex gap-1">
                  {viewportOptions.map((viewport) => (
                    <Button
                      key={viewport.id}
                      onClick={() => onViewportChange?.(viewport.id)}
                      variant={currentSettings.viewport === viewport.id ? "default" : "outline"}
                      size="sm"
                      className="flex-1 text-xs"
                    >
                      {viewport.icon}
                      <span className="ml-1">{viewport.name}</span>
                    </Button>
                  ))}
                </div>

                <div>
                  <Label className="text-xs">Zoom: {Math.round((currentSettings.zoom || 1) * 100)}%</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Button
                      onClick={() => onZoomChange?.((currentSettings.zoom || 1) * 0.9)}
                      size="sm"
                      variant="outline"
                      className="p-1"
                    >
                      <ZoomOut className="w-3 h-3" />
                    </Button>
                    <Slider
                      value={[(currentSettings.zoom || 1) * 100]}
                      onValueChange={(value) => onZoomChange?.(value[0] / 100)}
                      min={25}
                      max={300}
                      step={5}
                      className="flex-1"
                    />
                    <Button
                      onClick={() => onZoomChange?.((currentSettings.zoom || 1) * 1.1)}
                      size="sm"
                      variant="outline"
                      className="p-1"
                    >
                      <ZoomIn className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Animation & Effects</Label>
              <div className="space-y-3 mt-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Play className="w-3 h-3 mr-2" />
                    <span className="text-xs">Enable Animations</span>
                  </div>
                  <Switch
                    checked={currentSettings.animation?.enabled !== false}
                    onCheckedChange={(checked) => onAnimationToggle?.(checked)}
                  />
                </div>

                {currentSettings.animation?.enabled && (
                  <div>
                    <Label className="text-xs">Animation Speed: {currentSettings.animation?.speed || 1}x</Label>
                    <Slider
                      value={[currentSettings.animation?.speed || 1]}
                      onValueChange={(value) => onAnimationToggle?.(true)}
                      min={0.1}
                      max={3.0}
                      step={0.1}
                      className="mt-1"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Volume2 className="w-3 h-3 mr-2" />
                    <span className="text-xs">Sound Effects</span>
                  </div>
                  <Switch
                    checked={currentSettings.sound?.enabled !== false}
                    onCheckedChange={(checked) => onSoundToggle?.(checked)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="filters" className="space-y-4 mt-4">
            <div>
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-medium">Search & Filter</Label>
                <Button
                  onClick={() => setSearchQuery('')}
                  size="sm"
                  variant="ghost"
                  className="text-xs"
                >
                  Clear
                </Button>
              </div>

              <div className="relative mb-3">
                <Search className="w-3 h-3 absolute left-2 top-2.5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search nodes..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-7 pr-3 py-2 text-xs bg-white/10 border border-white/20 rounded focus:outline-none focus:border-white/40"
                />
              </div>

              {filterOptions.map((filter) => (
                <div key={filter.id} className="space-y-2">
                  <Label className="text-xs font-medium">{filter.name}</Label>
                  <div className="grid grid-cols-2 gap-1">
                    {filter.options.map((option) => (
                      <Button
                        key={option}
                        onClick={() => onFilterChange?.({ [filter.id]: option })}
                        variant={currentSettings.filters?.[filter.id] === option ? "default" : "outline"}
                        size="sm"
                        className="text-xs h-7"
                      >
                        {option}
                      </Button>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <div>
              <Label className="text-sm font-medium">Quick Actions</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <Button
                  onClick={() => onBookmark?.()}
                  size="sm"
                  variant="outline"
                  className="text-xs"
                >
                  <Bookmark className="w-3 h-3 mr-1" />
                  Bookmark View
                </Button>
                <Button
                  onClick={() => onShare?.()}
                  size="sm"
                  variant="outline"
                  className="text-xs"
                >
                  <Share2 className="w-3 h-3 mr-1" />
                  Share Canvas
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="accessibility" className="space-y-4 mt-4">
            <div>
              <Label className="text-sm font-medium">Accessibility Options</Label>
              <div className="space-y-3 mt-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Contrast className="w-3 h-3 mr-2" />
                    <span className="text-xs">High Contrast</span>
                  </div>
                  <Switch
                    checked={currentSettings.accessibility?.highContrast || false}
                    onCheckedChange={(checked) => onAccessibilityChange?.('highContrast', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Accessibility className="w-3 h-3 mr-2" />
                    <span className="text-xs">Reduced Motion</span>
                  </div>
                  <Switch
                    checked={currentSettings.accessibility?.reducedMotion || false}
                    onCheckedChange={(checked) => onAccessibilityChange?.('reducedMotion', checked)}
                  />
                </div>

                <div>
                  <Label className="text-xs">Font Size: {currentSettings.accessibility?.fontSize || 14}px</Label>
                  <Slider
                    value={[currentSettings.accessibility?.fontSize || 14]}
                    onValueChange={(value) => onAccessibilityChange?.('fontSize', value[0])}
                    min={10}
                    max={24}
                    step={1}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label className="text-xs">Color Blind Support</Label>
                  <div className="grid grid-cols-2 gap-1 mt-1">
                    {['none', 'protanopia', 'deuteranopia', 'tritanopia'].map((type) => (
                      <Button
                        key={type}
                        onClick={() => onAccessibilityChange?.('colorBlindSupport', type)}
                        variant={currentSettings.accessibility?.colorBlindSupport === type ? "default" : "outline"}
                        size="sm"
                        className="text-xs h-7"
                      >
                        {type === 'none' ? 'Normal' : type}
                      </Button>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-xs">Keyboard Navigation</Label>
                  <div className="text-xs opacity-70 mt-1 space-y-1">
                    <div>• Tab: Navigate elements</div>
                    <div>• Space: Activate buttons</div>
                    <div>• Arrow keys: Move focus</div>
                    <div>• Esc: Close dialogs</div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="data" className="space-y-4 mt-4">
            <div>
              <Label className="text-sm font-medium">Export Options</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <Button
                  onClick={() => onExport('json')}
                  className="justify-start text-xs h-8"
                  variant="outline"
                >
                  <Download className="w-3 h-3 mr-2" />
                  JSON
                </Button>
                <Button
                  onClick={() => onExport('csv')}
                  className="justify-start text-xs h-8"
                  variant="outline"
                >
                  <Download className="w-3 h-3 mr-2" />
                  CSV
                </Button>
                <Button
                  onClick={() => onExport('png')}
                  className="justify-start text-xs h-8"
                  variant="outline"
                >
                  <Download className="w-3 h-3 mr-2" />
                  PNG
                </Button>
                <Button
                  onClick={() => onExport('svg')}
                  className="justify-start text-xs h-8"
                  variant="outline"
                >
                  <Download className="w-3 h-3 mr-2" />
                  SVG
                </Button>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Import Options</Label>
              <div className="space-y-2 mt-2">
                <input
                  type="file"
                  accept=".json,.csv"
                  onChange={handleFileImport}
                  className="hidden"
                  id="import-file"
                />
                <Button
                  onClick={() => document.getElementById('import-file')?.click()}
                  className="w-full justify-start text-xs h-8"
                  variant="outline"
                >
                  <Upload className="w-3 h-3 mr-2" />
                  Import Canvas Data
                </Button>

                <Button
                  onClick={() => navigator.clipboard.readText().then(text => {
                    try {
                      const data = JSON.parse(text);
                      console.log('Importing from clipboard:', data);
                    } catch (e) {
                      console.error('Invalid JSON in clipboard');
                    }
                  })}
                  className="w-full justify-start text-xs h-8"
                  variant="outline"
                >
                  <Copy className="w-3 h-3 mr-2" />
                  Import from Clipboard
                </Button>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Sharing & Collaboration</Label>
              <div className="space-y-2 mt-2">
                <Button
                  onClick={() => onShare?.()}
                  className="w-full justify-start text-xs h-8"
                  variant="outline"
                >
                  <Share2 className="w-3 h-3 mr-2" />
                  Generate Share Link
                </Button>

                <Button
                  onClick={() => {
                    const url = window.location.href;
                    navigator.clipboard.writeText(url);
                  }}
                  className="w-full justify-start text-xs h-8"
                  variant="outline"
                >
                  <ExternalLink className="w-3 h-3 mr-2" />
                  Copy Current URL
                </Button>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Canvas Management</Label>
              <div className="space-y-2 mt-2">
                <Button
                  onClick={onReset}
                  className="w-full justify-start text-xs h-8"
                  variant="destructive"
                >
                  <RotateCcw className="w-3 h-3 mr-2" />
                  Reset to Default
                </Button>

                <Button
                  onClick={() => {
                    if (confirm('This will clear all data. Are you sure?')) {
                      onReset();
                    }
                  }}
                  className="w-full justify-start text-xs h-8"
                  variant="destructive"
                >
                  <RotateCcw className="w-3 h-3 mr-2" />
                  Clear All Data
                </Button>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Performance</Label>
              <div className="space-y-1 mt-2">
                <div className="flex justify-between text-xs">
                  <span>FPS:</span>
                  <Badge variant="secondary">{currentSettings.performance?.fps || 60}</Badge>
                </div>
                <div className="flex justify-between text-xs">
                  <span>Nodes:</span>
                  <Badge variant="secondary">{currentSettings.performance?.nodeCount || 0}</Badge>
                </div>
                <div className="flex justify-between text-xs">
                  <span>Connections:</span>
                  <Badge variant="secondary">{currentSettings.performance?.connectionCount || 0}</Badge>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="help" className="space-y-4 mt-4">
            <div className="space-y-3 text-xs">
              <div>
                <div className="font-medium flex items-center">
                  <HelpCircle className="w-3 h-3 mr-1" />
                  Keyboard Shortcuts
                </div>
                <div className="mt-1 space-y-1 opacity-70">
                  <div>• Ctrl+Click: Multi-select nodes</div>
                  <div>• Ctrl+Shift+Click: Start connection</div>
                  <div>• C: Create cluster from selection</div>
                  <div>• S: Create simulation from selection</div>
                  <div>• Esc: Cancel operations</div>
                </div>
              </div>

              <div>
                <div className="font-medium">Mouse Controls</div>
                <div className="mt-1 space-y-1 opacity-70">
                  <div>• Left Click: Select node</div>
                  <div>• Right Click: Context menu</div>
                  <div>• Drag: Pan canvas</div>
                  <div>• Scroll: Zoom in/out</div>
                </div>
              </div>

              <div>
                <div className="font-medium">Tips</div>
                <div className="mt-1 space-y-1 opacity-70">
                  <div>• Use clusters to organize related analyses</div>
                  <div>• Create simulations to test scenarios</div>
                  <div>• Export data for backup or sharing</div>
                  <div>• Adjust layout for better visualization</div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
