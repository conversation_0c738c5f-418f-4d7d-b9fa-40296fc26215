import React, { useState, useMemo } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, EyeOff, Settings, Download, RefreshCcw } from "lucide-react";
import { CanvasToolbar } from './CanvasToolbar';
import { AICanvasControls } from './AICanvasControls';
import { FabricCanvas } from './FabricCanvas';
import { EmptyCanvasState } from './EmptyCanvasState';
import { AIConnectionSuggestions } from './AIConnectionSuggestions';
import { SmartClusterOverlay } from './SmartClusterOverlay';
import { PatternOverlayVisualization } from './PatternOverlayVisualization';
import { AIInsightsSummary } from './AIInsightsSummary';
import { AICanvasExport } from './AICanvasExport';
import { CanvasActionMenu } from './CanvasActionMenu';
import { useVisualCanvas } from './hooks/useVisualCanvas';
import { useCanvasStore } from '@/stores/useCanvasStore';

export const VisualHistoricalAnalysisCanvas: React.FC = () => {
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  
  const {
    notes,
    connections,
    searchQuery,
    setSearchQuery,
    analysisTypeFilter,
    setAnalysisTypeFilter,
    tagFilter,
    setTagFilter,
    allTags,
    nodePositions,
    connectingNodeId,
    snapToGrid,
    setSnapToGrid,
    contextMenu,
    closeContextMenu,
    handleNodeDoubleClick,
    handleViewDetails,
    handleConnectEnd,
    handleConnectCancel,
    handleContextMenu,
    
    // AI state
    isAnalyzing,
    connectionSuggestions,
    smartClusters,
    patternOverlays,
    insightSummary,
    showSuggestions,
    showClusters,
    showPatterns,
    showInsights,
    showExport,
    isAIEnabled,
    
    // AI actions
    runAIAnalysis,
    acceptSuggestion,
    rejectSuggestion,
    clearAIData,
    toggleSuggestions,
    toggleClusters,
    togglePatterns,
    toggleInsights,
    toggleExport,
    
    // Canvas interactions
    handleConnectNodes,
    handleToggleConnection,
    handleDeleteNote,
    getNodePosition,
  } = useVisualCanvas();

  // Get the setNodePositions function from the store for node movement
  const setNodePositions = useCanvasStore((state) => state.setNodePositions);

  const filteredNotes = useMemo(() => {
    return notes.filter(note => {
      const searchMatch = !searchQuery || 
        note.noteText.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const typeMatch = analysisTypeFilter.length === 0 || 
        (note.analysisType && analysisTypeFilter.includes(note.analysisType));
      
      const tagMatch = tagFilter.length === 0 || 
        note.tags?.some(tag => tagFilter.includes(tag));
      
      return searchMatch && typeMatch && tagMatch;
    });
  }, [notes, searchQuery, analysisTypeFilter, tagFilter]);

  const filteredConnections = useMemo(() => {
    const visibleNodeIds = new Set(filteredNotes.map(note => note.id));
    return connections.filter(conn => 
      visibleNodeIds.has(conn.fromId) && visibleNodeIds.has(conn.toId)
    );
  }, [connections, filteredNotes]);

  const areSelectedNodesConnected = useMemo(() => {
    if (selectedNodes.length !== 2) return false;
    const [node1, node2] = selectedNodes;
    return filteredConnections.some(conn => 
      (conn.fromId === node1 && conn.toId === node2) ||
      (conn.fromId === node2 && conn.toId === node1)
    );
  }, [selectedNodes, filteredConnections]);

  const handleZoomIn = () => setZoom(prev => Math.min(prev * 1.2, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev / 1.2, 0.1));
  const handleResetView = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  const handleNodeMove = (updates: { id: string; x: number; y: number }[]) => {
    setNodePositions(currentPositions => {
      const positionMap = new Map(currentPositions.map(p => [p.id, p]));
      
      updates.forEach(update => {
        const existingPosition = positionMap.get(update.id);
        if (existingPosition) {
          existingPosition.x = update.x;
          existingPosition.y = update.y;
        } else {
          positionMap.set(update.id, update);
        }
      });
      
      return Array.from(positionMap.values());
    });
  };

  const handleSelectionChange = (selectedIds: string[], isMultiSelect: boolean) => {
    setSelectedNodes(selectedIds);
  };

  const handleCanvasClick = () => {
    setSelectedNodes([]);
    closeContextMenu();
  };

  const handleConnectStart = (nodeId: string) => {
    // This is handled by the canvas state
  };

  const handleRunAIAnalysis = () => {
    const nodePositionsForAnalysis = nodePositions.map(pos => ({
      id: pos.id,
      x: pos.x,
      y: pos.y
    }));
    runAIAnalysis(filteredNotes, filteredConnections, nodePositionsForAnalysis);
  };

  // Show empty state if no notes
  if (filteredNotes.length === 0 && notes.length === 0) {
    return <EmptyCanvasState />;
  }

  return (
    <div className="h-[800px] flex flex-col bg-slate-50 rounded-lg border border-slate-200">
      {/* Canvas Toolbar */}
      <CanvasToolbar
        zoom={zoom}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetView={handleResetView}
        selectedNodesCount={selectedNodes.length}
        onConnectNodes={handleToggleConnection}
        snapToGrid={snapToGrid}
        onToggleSnapToGrid={() => setSnapToGrid(!snapToGrid)}
        searchQuery={searchQuery}
        onSearchQueryChange={setSearchQuery}
        analysisTypeFilter={analysisTypeFilter}
        onAnalysisTypeFilterChange={setAnalysisTypeFilter}
        allTags={allTags}
        tagFilter={tagFilter}
        onTagFilterChange={setTagFilter}
        areSelectedNodesConnected={areSelectedNodesConnected}
      />

      {/* AI Canvas Controls */}
      <div className="border-b border-slate-200 p-2">
        <div id="ai-canvas-controls">
          <AICanvasControls
            isAIEnabled={isAIEnabled}
            showSuggestions={showSuggestions}
            showClusters={showClusters}
            showPatterns={showPatterns}
            showInsights={showInsights}
            showExport={showExport}
            isAnalyzing={isAnalyzing}
            onToggleSuggestions={toggleSuggestions}
            onToggleClusters={toggleClusters}
            onTogglePatterns={togglePatterns}
            onToggleInsights={toggleInsights}
            onToggleExport={toggleExport}
            onRunAnalysis={handleRunAIAnalysis}
            onClearAIData={clearAIData}
          />
        </div>
      </div>

      {/* Main Canvas Area */}
      <div className="flex-1 relative overflow-hidden">
        {filteredNotes.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-slate-500">
              <p className="text-lg font-medium">No notes match your current filters</p>
              <p className="text-sm">Try adjusting your search or filter criteria</p>
            </div>
          </div>
        ) : (
          <>
            {/* Fabric Canvas for rendering nodes and connections */}
            <FabricCanvas
              notes={filteredNotes}
              connections={filteredConnections}
              nodePositions={nodePositions}
              selectedNodes={selectedNodes}
              connectingNodeId={connectingNodeId}
              onNodeMove={handleNodeMove}
              onSelectionChange={handleSelectionChange}
              onNodeDoubleClick={handleNodeDoubleClick}
              onCanvasClick={handleCanvasClick}
              onConnectStart={handleConnectStart}
              onConnectEnd={handleConnectEnd}
              onConnectCancel={handleConnectCancel}
              onContextMenu={handleContextMenu}
            />

            {/* AI Overlays */}
            {showClusters && (
              <SmartClusterOverlay
                clusters={smartClusters}
                zoom={zoom}
                pan={pan}
                isVisible={showClusters}
              />
            )}

            {showPatterns && (
              <PatternOverlayVisualization
                patterns={patternOverlays}
                zoom={zoom}
                pan={pan}
                isVisible={showPatterns}
              />
            )}            {/* Context Menu */}
            {contextMenu.visible && (
              <CanvasActionMenu
                x={contextMenu.x}
                y={contextMenu.y}
                targetId={contextMenu.targetId}
                selectedNodes={selectedNodes}
                areSelectedNodesConnected={areSelectedNodesConnected}
                onClose={closeContextMenu}
                onViewDetails={handleViewDetails}
                onDelete={(nodeIds) => {
                  nodeIds.forEach(nodeId => handleDeleteNote(nodeId));
                  closeContextMenu();
                }}
                onToggleConnection={handleToggleConnection}
                onConnectStart={(nodeId) => {
                  // Handle connect start
                }}
              />
            )}
          </>
        )}
      </div>      {/* AI Sidebars and Modals */}
      {showSuggestions && connectionSuggestions.length > 0 && (
        <div className="absolute top-20 right-4 w-80 max-h-96 overflow-y-auto">
          <AIConnectionSuggestions
            suggestions={connectionSuggestions}
            onAcceptSuggestion={(suggestion) => {
              const result = acceptSuggestion(suggestion);
              if (result) {
                handleConnectNodes(result.fromId, result.toId);
              }
            }}            onRejectSuggestion={(suggestionId) => {
              rejectSuggestion(suggestionId);
            }}
            isVisible={showSuggestions}
          />
        </div>
      )}

      {showInsights && insightSummary && (
        <div className="absolute top-20 left-4 w-80 max-h-96 overflow-y-auto">
          <AIInsightsSummary
            notes={filteredNotes}
            insights={insightSummary}
            isVisible={showInsights}
            onClose={toggleInsights}
          />
        </div>
      )}

      {showExport && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <AICanvasExport
              notes={filteredNotes}
              connections={filteredConnections}
              clusters={smartClusters}
              patterns={patternOverlays}
              insights={insightSummary}
              isVisible={showExport}
              onClose={toggleExport}
            />
          </div>
        </div>
      )}

      {/* Status Bar */}
      <div className="border-t border-slate-200 px-4 py-2 bg-white text-sm text-slate-600 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <span>{filteredNotes.length} notes</span>
          <span>{filteredConnections.length} connections</span>
          {selectedNodes.length > 0 && (
            <Badge variant="secondary">
              {selectedNodes.length} selected
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          {isAnalyzing && (
            <div className="flex items-center gap-2">
              <RefreshCcw className="h-3 w-3 animate-spin" />
              <span className="text-xs">Analyzing...</span>
            </div>
          )}
          <span className="text-xs">Zoom: {Math.round(zoom * 100)}%</span>
        </div>
      </div>
    </div>
  );
};
