/**
 * Animation Keyframes
 * 
 * Comprehensive animation keyframes for smooth, performant animations.
 * Designed to work with the utility classes and design system.
 */

@layer base {
  /* Fade Animations */
  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInDown {
    0% {
      opacity: 0;
      transform: translateY(-20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInLeft {
    0% {
      opacity: 0;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes fadeInRight {
    0% {
      opacity: 0;
      transform: translateX(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes fadeOut {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }

  /* Scale Animations */
  @keyframes scaleIn {
    0% {
      opacity: 0;
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes scaleOut {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(0.9);
    }
  }
  
  @keyframes scaleInBounce {
    0% {
      opacity: 0;
      transform: scale(0.3);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Slide Animations */
  @keyframes slideUp {
    0% {
      opacity: 0;
      transform: translateY(100%);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slideDown {
    0% {
      opacity: 0;
      transform: translateY(-100%);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slideLeft {
    0% {
      opacity: 0;
      transform: translateX(100%);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes slideRight {
    0% {
      opacity: 0;
      transform: translateX(-100%);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Bounce Animations */
  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes bounceOut {
    20% {
      transform: scale(0.9);
    }
    50%, 55% {
      opacity: 1;
      transform: scale(1.1);
    }
    100% {
      opacity: 0;
      transform: scale(0.3);
    }
  }
  
  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
      transform: translate3d(0, 0, 0);
    }
    40%, 43% {
      animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
      transform: translate3d(0, -30px, 0);
    }
    70% {
      animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
      transform: translate3d(0, -15px, 0);
    }
    90% {
      transform: translate3d(0, -4px, 0);
    }
  }

  /* Rotation Animations */
  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes rotateIn {
    0% {
      opacity: 0;
      transform: rotate(-200deg);
    }
    100% {
      opacity: 1;
      transform: rotate(0deg);
    }
  }
  
  @keyframes rotateOut {
    0% {
      opacity: 1;
      transform: rotate(0deg);
    }
    100% {
      opacity: 0;
      transform: rotate(200deg);
    }
  }

  /* Flip Animations */
  @keyframes flipInX {
    0% {
      opacity: 0;
      transform: perspective(400px) rotateX(90deg);
    }
    40% {
      transform: perspective(400px) rotateX(-20deg);
    }
    60% {
      transform: perspective(400px) rotateX(10deg);
    }
    80% {
      transform: perspective(400px) rotateX(-5deg);
    }
    100% {
      opacity: 1;
      transform: perspective(400px) rotateX(0deg);
    }
  }
  
  @keyframes flipInY {
    0% {
      opacity: 0;
      transform: perspective(400px) rotateY(90deg);
    }
    40% {
      transform: perspective(400px) rotateY(-20deg);
    }
    60% {
      transform: perspective(400px) rotateY(10deg);
    }
    80% {
      transform: perspective(400px) rotateY(-5deg);
    }
    100% {
      opacity: 1;
      transform: perspective(400px) rotateY(0deg);
    }
  }

  /* Shake and Wobble */
  @keyframes shake {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
      transform: translateX(10px);
    }
  }
  
  @keyframes wobble {
    0% {
      transform: translateX(0%);
    }
    15% {
      transform: translateX(-25%) rotate(-5deg);
    }
    30% {
      transform: translateX(20%) rotate(3deg);
    }
    45% {
      transform: translateX(-15%) rotate(-3deg);
    }
    60% {
      transform: translateX(10%) rotate(2deg);
    }
    75% {
      transform: translateX(-5%) rotate(-1deg);
    }
    100% {
      transform: translateX(0%);
    }
  }

  /* Pulse and Glow */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
  
  @keyframes pulseGlow {
    0%, 100% {
      opacity: 0.7;
      box-shadow: 0 0 20px rgba(var(--primary), 0.3);
    }
    50% {
      opacity: 1;
      box-shadow: 0 0 30px rgba(var(--primary), 0.6);
    }
  }
  
  @keyframes heartbeat {
    0% {
      transform: scale(1);
    }
    14% {
      transform: scale(1.3);
    }
    28% {
      transform: scale(1);
    }
    42% {
      transform: scale(1.3);
    }
    70% {
      transform: scale(1);
    }
  }

  /* Float and Hover */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  @keyframes floatSlow {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-5px);
    }
  }
  
  @keyframes hoverFloat {
    0%, 100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-5px) scale(1.02);
    }
  }

  /* Loading Animations */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  
  @keyframes dots {
    0%, 20% {
      content: '';
    }
    40% {
      content: '.';
    }
    60% {
      content: '..';
    }
    80%, 100% {
      content: '...';
    }
  }
  
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes spinSlow {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Border Animations */
  @keyframes dash {
    0% {
      stroke-dashoffset: 0;
    }
    100% {
      stroke-dashoffset: 20;
    }
  }
  
  @keyframes borderGlow {
    0%, 100% {
      border-color: rgba(var(--primary), 0.3);
      box-shadow: 0 0 5px rgba(var(--primary), 0.3);
    }
    50% {
      border-color: rgba(var(--primary), 0.8);
      box-shadow: 0 0 20px rgba(var(--primary), 0.6);
    }
  }

  /* Typewriter Effect */
  @keyframes typewriter {
    0% {
      width: 0;
    }
    100% {
      width: 100%;
    }
  }
  
  @keyframes blinkCursor {
    0%, 50% {
      border-color: transparent;
    }
    51%, 100% {
      border-color: currentColor;
    }
  }

  /* Morphing Animations */
  @keyframes morphCircleToSquare {
    0% {
      border-radius: 50%;
    }
    100% {
      border-radius: 0%;
    }
  }
  
  @keyframes morphSquareToCircle {
    0% {
      border-radius: 0%;
    }
    100% {
      border-radius: 50%;
    }
  }

  /* Attention Seekers */
  @keyframes flash {
    0%, 50%, 100% {
      opacity: 1;
    }
    25%, 75% {
      opacity: 0;
    }
  }
  
  @keyframes rubberBand {
    0% {
      transform: scale(1);
    }
    30% {
      transform: scaleX(1.25) scaleY(0.75);
    }
    40% {
      transform: scaleX(0.75) scaleY(1.25);
    }
    50% {
      transform: scaleX(1.15) scaleY(0.85);
    }
    65% {
      transform: scaleX(0.95) scaleY(1.05);
    }
    75% {
      transform: scaleX(1.05) scaleY(0.95);
    }
    100% {
      transform: scale(1);
    }
  }
  
  @keyframes jello {
    0%, 11.1%, 100% {
      transform: translate3d(0, 0, 0);
    }
    22.2% {
      transform: skewX(-12.5deg) skewY(-12.5deg);
    }
    33.3% {
      transform: skewX(6.25deg) skewY(6.25deg);
    }
    44.4% {
      transform: skewX(-3.125deg) skewY(-3.125deg);
    }
    55.5% {
      transform: skewX(1.5625deg) skewY(1.5625deg);
    }
    66.6% {
      transform: skewX(-0.78125deg) skewY(-0.78125deg);
    }
    77.7% {
      transform: skewX(0.390625deg) skewY(0.390625deg);
    }
    88.8% {
      transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    }
  }
}
