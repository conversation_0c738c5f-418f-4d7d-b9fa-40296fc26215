
import { useState, useMemo } from 'react';
import { TPointerEventInfo } from 'fabric';
import { UserNote } from '@/types/conversation';
import { useCanvasState } from './useCanvasState';
import { useAICanvasState } from './useAICanvasState';
import { useCanvasEventHandlers } from './useCanvasEventHandlers';
import { useCanvasStore } from '@/stores/useCanvasStore';
import { useUIStore } from '@/stores/useUIStore';
import { useLibraryStore } from '@/stores/useLibraryStore';
import { calculateBounds, calculateCenter } from '../utils/canvasUtils';
import { CustomFabricObject } from '../fabric-helpers';

export const useVisualCanvas = () => {
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    targetId: string | null;
  }>({ visible: false, x: 0, y: 0, targetId: null });
  
  const canvasState = useCanvasState({ snapToGrid });
  const {
    notes,
    connections,
    getNodePosition,
    handleConnectNodes,
    setConnectingNodeId,
    handleDeleteNote,
    handleToggleConnection,
    connectingNodeId,
  } = canvasState;

  const aiState = useAICanvasState();
  const nodePositions = useCanvasStore((state) => state.nodePositions);
  const openModal = useUIStore((state) => state.openModal);
  const { savedAnalyses } = useLibraryStore();

  const eventHandlers = useCanvasEventHandlers({
    notes,
    connections,
    getNodePosition,
    runAIAnalysis: aiState.runAIAnalysis,
    acceptSuggestion: aiState.acceptSuggestion,
    handleConnectNodes,
    handleToggleConnection,
    handleDeleteNote,
  });
  const smartClusters = useMemo(() => {
    try {
      if (!aiState.smartClusters || aiState.smartClusters.length === 0 || nodePositions.length === 0) return [];
      return aiState.smartClusters.map(cluster => ({
        ...cluster,
        center: calculateCenter(cluster.noteIds, nodePositions),
      }));
    } catch (error) {
      console.warn('Failed to calculate smart clusters:', error);
      return [];
    }
  }, [aiState.smartClusters, nodePositions]);

  const patternOverlays = useMemo(() => {
    try {
      if (!aiState.patternOverlays || aiState.patternOverlays.length === 0 || nodePositions.length === 0) return [];
      return aiState.patternOverlays.map(pattern => ({
        ...pattern,
        coordinates: calculateBounds(pattern.nodeIds, nodePositions),
      }));
    } catch (error) {
      console.warn('Failed to calculate pattern overlays:', error);
      return [];
    }
  }, [aiState.patternOverlays, nodePositions]);

  const closeContextMenu = () => {
    setContextMenu((prev) => ({ ...prev, visible: false }));
  };

  const handleNodeDoubleClick = (note: UserNote) => {
    const parentAnalysis = savedAnalyses.find(sa => 
      sa.results.some(r => r.id === note.questionId)
    );
    if (parentAnalysis) {
      openModal("SAVED_ANALYSIS_VIEWER", { analysis: parentAnalysis });
    } else {
      openModal('NOTE_EDITOR', { note, isViewing: true });
    }
  };

  const handleViewDetails = (noteId: string) => {
    const note = notes.find(n => n.id === noteId);
    if (note) {
      handleNodeDoubleClick(note);
    }
  };

  const handleConnectEnd = (fromId: string, toId: string) => {
    handleConnectNodes(fromId, toId);
    setConnectingNodeId(null);
  };
  
  const handleConnectCancel = () => {
    setConnectingNodeId(null);
  };

  const handleContextMenu = (opt: TPointerEventInfo) => {
    const target = opt.target as CustomFabricObject | undefined;
    const targetId = target?.data?.id || null;

    if (!targetId) {
      closeContextMenu();
      return;
    }
    
    if (!('clientX' in opt.e) || !('clientY' in opt.e)) {
      return;
    }

    const { clientX, clientY } = opt.e;
    setContextMenu({
      visible: true,
      x: clientX,
      y: clientY,
      targetId: targetId,
    });
  };

  return {
    ...canvasState,
    ...aiState,
    ...eventHandlers,
    connectingNodeId,
    nodePositions,
    snapToGrid,
    setSnapToGrid,
    contextMenu,
    closeContextMenu,
    handleNodeDoubleClick,
    handleViewDetails,
    handleConnectEnd,
    handleConnectCancel,
    handleContextMenu,
    smartClusters,
    patternOverlays,
  };
};
