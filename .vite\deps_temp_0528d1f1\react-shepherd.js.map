{"version": 3, "sources": ["../../node_modules/shepherd.js/dist/js/shepherd.esm.js", "../../node_modules/react-shepherd/src/index.tsx"], "sourcesContent": ["/*! shepherd.js 11.0.1 */\n\nvar isMergeableObject = function isMergeableObject(value) {\n  return isNonNullObject(value) && !isSpecial(value);\n};\nfunction isNonNullObject(value) {\n  return !!value && typeof value === 'object';\n}\nfunction isSpecial(value) {\n  var stringValue = Object.prototype.toString.call(value);\n  return stringValue === '[object RegExp]' || stringValue === '[object Date]' || isReactElement(value);\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\nfunction isReactElement(value) {\n  return value.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction emptyTarget(val) {\n  return Array.isArray(val) ? [] : {};\n}\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n  return options.clone !== false && options.isMergeableObject(value) ? deepmerge(emptyTarget(value), value, options) : value;\n}\nfunction defaultArrayMerge(target, source, options) {\n  return target.concat(source).map(function (element) {\n    return cloneUnlessOtherwiseSpecified(element, options);\n  });\n}\nfunction getMergeFunction(key, options) {\n  if (!options.customMerge) {\n    return deepmerge;\n  }\n  var customMerge = options.customMerge(key);\n  return typeof customMerge === 'function' ? customMerge : deepmerge;\n}\nfunction getEnumerableOwnPropertySymbols(target) {\n  return Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(target).filter(function (symbol) {\n    return target.propertyIsEnumerable(symbol);\n  }) : [];\n}\nfunction getKeys(target) {\n  return Object.keys(target).concat(getEnumerableOwnPropertySymbols(target));\n}\nfunction propertyIsOnObject(object, property) {\n  try {\n    return property in object;\n  } catch (_) {\n    return false;\n  }\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n  return propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n  && !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n  && Object.propertyIsEnumerable.call(target, key)); // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n  var destination = {};\n  if (options.isMergeableObject(target)) {\n    getKeys(target).forEach(function (key) {\n      destination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n    });\n  }\n  getKeys(source).forEach(function (key) {\n    if (propertyIsUnsafe(target, key)) {\n      return;\n    }\n    if (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n      destination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n    } else {\n      destination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n    }\n  });\n  return destination;\n}\nfunction deepmerge(target, source, options) {\n  options = options || {};\n  options.arrayMerge = options.arrayMerge || defaultArrayMerge;\n  options.isMergeableObject = options.isMergeableObject || isMergeableObject;\n  // cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n  // implementations can use it. The caller may not replace it.\n  options.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n  var sourceIsArray = Array.isArray(source);\n  var targetIsArray = Array.isArray(target);\n  var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n  if (!sourceAndTargetTypesMatch) {\n    return cloneUnlessOtherwiseSpecified(source, options);\n  } else if (sourceIsArray) {\n    return options.arrayMerge(target, source, options);\n  } else {\n    return mergeObject(target, source, options);\n  }\n}\ndeepmerge.all = function deepmergeAll(array, options) {\n  if (!Array.isArray(array)) {\n    throw new Error('first argument should be an array');\n  }\n  return array.reduce(function (prev, next) {\n    return deepmerge(prev, next, options);\n  }, {});\n};\nvar deepmerge_1 = deepmerge;\nvar cjs = deepmerge_1;\n\n/**\n * Checks if `value` is classified as an `Element`.\n * @param {*} value The param to check if it is an Element\n */\nfunction isElement$1(value) {\n  return value instanceof Element;\n}\n\n/**\n * Checks if `value` is classified as an `HTMLElement`.\n * @param {*} value The param to check if it is an HTMLElement\n */\nfunction isHTMLElement$1(value) {\n  return value instanceof HTMLElement;\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n * @param {*} value The param to check if it is a function\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n\n/**\n * Checks if `value` is classified as a `String` object.\n * @param {*} value The param to check if it is a string\n */\nfunction isString(value) {\n  return typeof value === 'string';\n}\n\n/**\n * Checks if `value` is undefined.\n * @param {*} value The param to check if it is undefined\n */\nfunction isUndefined(value) {\n  return value === undefined;\n}\n\nclass Evented {\n  on(event, handler, ctx, once) {\n    if (once === void 0) {\n      once = false;\n    }\n    if (isUndefined(this.bindings)) {\n      this.bindings = {};\n    }\n    if (isUndefined(this.bindings[event])) {\n      this.bindings[event] = [];\n    }\n    this.bindings[event].push({\n      handler,\n      ctx,\n      once\n    });\n    return this;\n  }\n  once(event, handler, ctx) {\n    return this.on(event, handler, ctx, true);\n  }\n  off(event, handler) {\n    if (isUndefined(this.bindings) || isUndefined(this.bindings[event])) {\n      return this;\n    }\n    if (isUndefined(handler)) {\n      delete this.bindings[event];\n    } else {\n      this.bindings[event].forEach((binding, index) => {\n        if (binding.handler === handler) {\n          this.bindings[event].splice(index, 1);\n        }\n      });\n    }\n    return this;\n  }\n  trigger(event) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (!isUndefined(this.bindings) && this.bindings[event]) {\n      this.bindings[event].forEach((binding, index) => {\n        const {\n          ctx,\n          handler,\n          once\n        } = binding;\n        const context = ctx || this;\n        handler.apply(context, args);\n        if (once) {\n          this.bindings[event].splice(index, 1);\n        }\n      });\n    }\n    return this;\n  }\n}\n\n/**\n * Binds all the methods on a JS Class to the `this` context of the class.\n * Adapted from https://github.com/sindresorhus/auto-bind\n * @param {object} self The `this` context of the class\n * @return {object} The `this` context of the class\n */\nfunction autoBind(self) {\n  const keys = Object.getOwnPropertyNames(self.constructor.prototype);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const val = self[key];\n    if (key !== 'constructor' && typeof val === 'function') {\n      self[key] = val.bind(self);\n    }\n  }\n  return self;\n}\n\n/**\n * Sets up the handler to determine if we should advance the tour\n * @param {string} selector\n * @param {Step} step The step instance\n * @return {Function}\n * @private\n */\nfunction _setupAdvanceOnHandler(selector, step) {\n  return event => {\n    if (step.isOpen()) {\n      const targetIsEl = step.el && event.currentTarget === step.el;\n      const targetIsSelector = !isUndefined(selector) && event.currentTarget.matches(selector);\n      if (targetIsSelector || targetIsEl) {\n        step.tour.next();\n      }\n    }\n  };\n}\n\n/**\n * Bind the event handler for advanceOn\n * @param {Step} step The step instance\n */\nfunction bindAdvance(step) {\n  // An empty selector matches the step element\n  const {\n    event,\n    selector\n  } = step.options.advanceOn || {};\n  if (event) {\n    const handler = _setupAdvanceOnHandler(selector, step);\n\n    // TODO: this should also bind/unbind on show/hide\n    let el;\n    try {\n      el = document.querySelector(selector);\n    } catch (e) {\n      // TODO\n    }\n    if (!isUndefined(selector) && !el) {\n      return console.error(`No element was found for the selector supplied to advanceOn: ${selector}`);\n    } else if (el) {\n      el.addEventListener(event, handler);\n      step.on('destroy', () => {\n        return el.removeEventListener(event, handler);\n      });\n    } else {\n      document.body.addEventListener(event, handler, true);\n      step.on('destroy', () => {\n        return document.body.removeEventListener(event, handler, true);\n      });\n    }\n  } else {\n    return console.error('advanceOn was defined, but no event name was passed.');\n  }\n}\n\n/**\n * Ensure class prefix ends in `-`\n * @param {string} prefix The prefix to prepend to the class names generated by nano-css\n * @return {string} The prefix ending in `-`\n */\nfunction normalizePrefix(prefix) {\n  if (!isString(prefix) || prefix === '') {\n    return '';\n  }\n  return prefix.charAt(prefix.length - 1) !== '-' ? `${prefix}-` : prefix;\n}\n\n/**\n * Resolves attachTo options, converting element option value to a qualified HTMLElement.\n * @param {Step} step The step instance\n * @returns {{}|{element, on}}\n * `element` is a qualified HTML Element\n * `on` is a string position value\n */\nfunction parseAttachTo(step) {\n  const options = step.options.attachTo || {};\n  const returnOpts = Object.assign({}, options);\n  if (isFunction(returnOpts.element)) {\n    // Bind the callback to step so that it has access to the object, to enable running additional logic\n    returnOpts.element = returnOpts.element.call(step);\n  }\n  if (isString(returnOpts.element)) {\n    // Can't override the element in user opts reference because we can't\n    // guarantee that the element will exist in the future.\n    try {\n      returnOpts.element = document.querySelector(returnOpts.element);\n    } catch (e) {\n      // TODO\n    }\n    if (!returnOpts.element) {\n      console.error(`The element for this Shepherd step was not found ${options.element}`);\n    }\n  }\n  return returnOpts;\n}\n\n/**\n * Checks if the step should be centered or not. Does not trigger attachTo.element evaluation, making it a pure\n * alternative for the deprecated step.isCentered() method.\n * @param resolvedAttachToOptions\n * @returns {boolean}\n */\nfunction shouldCenterStep(resolvedAttachToOptions) {\n  if (resolvedAttachToOptions === undefined || resolvedAttachToOptions === null) {\n    return true;\n  }\n  return !resolvedAttachToOptions.element || !resolvedAttachToOptions.on;\n}\n\n/**\n * Create a unique id for steps, tours, modals, etc\n * @return {string}\n */\nfunction uuid() {\n  let d = Date.now();\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (d + Math.random() * 16) % 16 | 0;\n    d = Math.floor(d / 16);\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n}\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\nconst _excluded2 = [\"mainAxis\", \"crossAxis\", \"fallbackPlacements\", \"fallbackStrategy\", \"flipAlignment\"],\n  _excluded4 = [\"mainAxis\", \"crossAxis\", \"limiter\"];\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'x' : 'y';\n}\nfunction getLengthFromAxis(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const mainAxis = getMainAxisFromPlacement(placement);\n  const length = getLengthFromAxis(mainAxis);\n  const commonAlign = reference[length] / 2 - floating[length] / 2;\n  const side = getSide(placement);\n  const isVertical = mainAxis === 'x';\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[mainAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[mainAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a reference element when it is given a certain positioning strategy.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\n\nconst computePosition$1 = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = _extends({}, middlewareData, {\n      [name]: _extends({}, middlewareData[name], data)\n    });\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n      continue;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\nfunction expandPaddingObject(padding) {\n  return _extends({\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, padding);\n}\nfunction getSideObjectFromPadding(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  return _extends({}, rect, {\n    top: rect.y,\n    left: rect.x,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(middlewareArguments, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = middlewareArguments;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = options;\n  const paddingObject = getSideObjectFromPadding(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? _extends({}, rects.floating, {\n    x,\n    y\n  }) : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\nconst min$1 = Math.min;\nconst max$1 = Math.max;\nfunction within(min$1$1, value, max$1$1) {\n  return max$1(min$1$1, min$1(value, max$1$1));\n}\n\n/**\n * Positions an inner element of the floating element such that it is centered\n * to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(middlewareArguments) {\n    // Since `element` is required, we don't Partial<> the type\n    const {\n      element,\n      padding = 0\n    } = options != null ? options : {};\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform\n    } = middlewareArguments;\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getSideObjectFromPadding(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getMainAxisFromPlacement(placement);\n    const alignment = getAlignment(placement);\n    const length = getLengthFromAxis(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const minProp = axis === 'y' ? 'top' : 'left';\n    const maxProp = axis === 'y' ? 'bottom' : 'right';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n    if (clientSize === 0) {\n      clientSize = rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds\n\n    const min = paddingObject[minProp];\n    const max = clientSize - arrowDimensions[length] - paddingObject[maxProp];\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = within(min, center, max); // Make sure that arrow points at the reference\n\n    const alignmentPadding = alignment === 'start' ? paddingObject[minProp] : paddingObject[maxProp];\n    const shouldAddOffset = alignmentPadding > 0 && center !== offset && rects.reference[length] <= rects.floating[length];\n    const alignmentOffset = shouldAddOffset ? center < min ? min - center : max - center : 0;\n    return {\n      [axis]: coords[axis] - alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset\n      }\n    };\n  }\n});\nconst hash$1 = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, matched => hash$1[matched]);\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const mainAxis = getMainAxisFromPlacement(placement);\n  const length = getLengthFromAxis(mainAxis);\n  let mainAlignmentSide = mainAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return {\n    main: mainAlignmentSide,\n    cross: getOppositePlacement(mainAlignmentSide)\n  };\n}\nconst hash = {\n  start: 'end',\n  end: 'start'\n};\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, matched => hash[matched]);\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\n\n/**\n * Changes the placement of the floating element to one that will fit if the\n * initially specified `placement` does not.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function flip(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(middlewareArguments) {\n      var _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = middlewareArguments;\n      const {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = true,\n          fallbackPlacements: specifiedFallbackPlacements,\n          fallbackStrategy = 'bestFit',\n          flipAlignment = true\n        } = options,\n        detectOverflowOptions = _objectWithoutPropertiesLoose(options, _excluded2);\n      const side = getSide(placement);\n      const isBasePlacement = side === initialPlacement;\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(middlewareArguments, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const {\n          main,\n          cross\n        } = getAlignmentSides(placement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n        overflows.push(overflow[main], overflow[cross]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }]; // One or more sides is overflowing\n\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip$, _middlewareData$flip2;\n        const nextIndex = ((_middlewareData$flip$ = (_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) != null ? _middlewareData$flip$ : 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n        let resetPlacement = 'bottom';\n        switch (fallbackStrategy) {\n          case 'bestFit':\n            {\n              var _overflowsData$map$so;\n              const placement = (_overflowsData$map$so = overflowsData.map(d => [d, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$map$so[0].placement;\n              if (placement) {\n                resetPlacement = placement;\n              }\n              break;\n            }\n          case 'initialPlacement':\n            resetPlacement = initialPlacement;\n            break;\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\nfunction getCrossAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\n\n/**\n * Shifts the floating element in order to keep it in view when it will overflow\n * a clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function shift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(middlewareArguments) {\n      const {\n        x,\n        y,\n        placement\n      } = middlewareArguments;\n      const {\n          mainAxis: checkMainAxis = true,\n          crossAxis: checkCrossAxis = false,\n          limiter = {\n            fn: _ref => {\n              let {\n                x,\n                y\n              } = _ref;\n              return {\n                x,\n                y\n              };\n            }\n          }\n        } = options,\n        detectOverflowOptions = _objectWithoutPropertiesLoose(options, _excluded4);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(middlewareArguments, detectOverflowOptions);\n      const mainAxis = getMainAxisFromPlacement(getSide(placement));\n      const crossAxis = getCrossAxis(mainAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = within(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = within(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn(_extends({}, middlewareArguments, {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      }));\n      return _extends({}, limitedCoords, {\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y\n        }\n      });\n    }\n  };\n};\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function limitShift(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(middlewareArguments) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = middlewareArguments;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = options;\n      const coords = {\n        x,\n        y\n      };\n      const mainAxis = getMainAxisFromPlacement(placement);\n      const crossAxis = getCrossAxis(mainAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = typeof offset === 'function' ? offset(middlewareArguments) : offset;\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : _extends({\n        mainAxis: 0,\n        crossAxis: 0\n      }, rawOffset);\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2, _middlewareData$offse3, _middlewareData$offse4;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? (_middlewareData$offse = (_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) != null ? _middlewareData$offse : 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : (_middlewareData$offse3 = (_middlewareData$offse4 = middlewareData.offset) == null ? void 0 : _middlewareData$offse4[crossAxis]) != null ? _middlewareData$offse3 : 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return ((_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeName(node) {\n  return isNode(node) ? (node.nodeName || '').toLowerCase() : '';\n}\nlet uaString;\nfunction getUAString() {\n  if (uaString) {\n    return uaString;\n  }\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    uaString = uaData.brands.map(item => item.brand + \"/\" + item.version).join(' ');\n    return uaString;\n  }\n  return navigator.userAgent;\n}\nfunction isHTMLElement(value) {\n  return value instanceof getWindow(value).HTMLElement;\n}\nfunction isElement(value) {\n  return value instanceof getWindow(value).Element;\n}\nfunction isNode(value) {\n  return value instanceof getWindow(value).Node;\n}\nfunction isShadowRoot(node) {\n  // Browsers without `ShadowRoot` support\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  const OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isContainingBlock(element) {\n  // TODO: Try and use feature detection here instead\n  const isFirefox = /firefox/i.test(getUAString());\n  const css = getComputedStyle(element);\n  const backdropFilter = css.backdropFilter || css.WebkitBackdropFilter; // This is non-exhaustive but covers the most common CSS properties that\n  // create a containing block.\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n  return css.transform !== 'none' || css.perspective !== 'none' || (backdropFilter ? backdropFilter !== 'none' : false) || isFirefox && css.willChange === 'filter' || isFirefox && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective'].some(value => css.willChange.includes(value)) || ['paint', 'layout', 'strict', 'content'].some(\n  // TS 4.1 compat\n  value => {\n    const contain = css.contain;\n    return contain != null ? contain.includes(value) : false;\n  });\n}\nfunction isLayoutViewport() {\n  // Not Safari\n  return !/^((?!chrome|android).)*safari/i.test(getUAString()); // Feature detection for this fails in various ways\n  // • Always-visible scrollbar or not\n  // • Width of <html>, etc.\n  // const vV = win.visualViewport;\n  // return vV ? Math.abs(win.innerWidth / vV.scale - vV.width) < 0.5 : true;\n}\n\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nconst FALLBACK_SCALE = {\n  x: 1,\n  y: 1\n};\nfunction getScale(element) {\n  const domElement = !isElement(element) && element.contextElement ? element.contextElement : isElement(element) ? element : null;\n  if (!domElement) {\n    return FALLBACK_SCALE;\n  }\n  const rect = domElement.getBoundingClientRect();\n  const css = getComputedStyle(domElement);\n  let x = rect.width / parseFloat(css.width);\n  let y = rect.height / parseFloat(css.height); // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  var _win$visualViewport$o, _win$visualViewport, _win$visualViewport$o2, _win$visualViewport2;\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  let scale = FALLBACK_SCALE;\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const win = isElement(element) ? getWindow(element) : window;\n  const addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  const x = (clientRect.left + (addVisualOffsets ? (_win$visualViewport$o = (_win$visualViewport = win.visualViewport) == null ? void 0 : _win$visualViewport.offsetLeft) != null ? _win$visualViewport$o : 0 : 0)) / scale.x;\n  const y = (clientRect.top + (addVisualOffsets ? (_win$visualViewport$o2 = (_win$visualViewport2 = win.visualViewport) == null ? void 0 : _win$visualViewport2.offsetTop) != null ? _win$visualViewport$o2 : 0 : 0)) / scale.y;\n  const width = clientRect.width / scale.x;\n  const height = clientRect.height / scale.y;\n  return {\n    width,\n    height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x,\n    y\n  };\n}\nfunction getDocumentElement(node) {\n  return ((isNode(node) ? node.ownerDocument : node.document) || window.document).documentElement;\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.pageXOffset,\n    scrollTop: element.pageYOffset\n  };\n}\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const rect = getBoundingClientRect(element, true, strategy === 'fixed', offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = {\n    x: 0,\n    y: 0\n  };\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent, true);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node\n  node.assignedSlot ||\n  // DOM Element detected\n  node.parentNode || (\n  // ShadowRoot detected\n  isShadowRoot(node) ? node.host : null) ||\n  // Fallback\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  return element.offsetParent;\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else {\n      currentNode = getParentNode(currentNode);\n    }\n  }\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\nfunction getOffsetParent(element) {\n  const window = getWindow(element);\n  let offsetParent = getTrueOffsetParent(element);\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static' && !isContainingBlock(offsetParent))) {\n    return window;\n  }\n  return offsetParent || getContainingBlock(element) || window;\n}\nfunction getDimensions(element) {\n  if (isHTMLElement(element)) {\n    return {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    };\n  }\n  const rect = getBoundingClientRect(element);\n  return {\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  if (offsetParent === documentElement) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = {\n    x: 1,\n    y: 1\n  };\n  const offsets = {\n    x: 0,\n    y: 0\n  };\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && strategy !== 'fixed') {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } // This doesn't appear to need to be negated.\n    // else if (documentElement) {\n    //   offsets.x = getWindowScrollBarX(documentElement);\n    // }\n  }\n\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const layoutViewport = isLayoutViewport();\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nconst min = Math.min;\nconst max = Math.max;\n\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nfunction getDocumentRect(element) {\n  var _element$ownerDocumen;\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  const width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  const height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    // @ts-ignore assume body is always available\n    return node.ownerDocument.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list) {\n  var _node$ownerDocument;\n  if (list === void 0) {\n    list = [];\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor));\n}\n\n// Returns the inner client rect, subtracting scrollbars if present\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : {\n    x: 1,\n    y: 1\n  };\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y,\n    width,\n    height\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  if (clippingAncestor === 'viewport') {\n    return rectToClientRect(getViewportRect(element, strategy));\n  }\n  if (isElement(clippingAncestor)) {\n    return getInnerBoundingClientRect(clippingAncestor, strategy);\n  }\n  return rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\n\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element; // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const containingBlock = isContainingBlock(currentNode);\n    const shouldDropCurrentNode = elementIsFixed ? !containingBlock && !currentContainingBlockComputedStyle : !containingBlock && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors\n\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nconst platform = {\n  getClippingRect,\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  isElement,\n  getDimensions,\n  getOffsetParent,\n  getDocumentElement,\n  getScale,\n  async getElementRects(_ref) {\n    let {\n      reference,\n      floating,\n      strategy\n    } = _ref;\n    const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n    const getDimensionsFn = this.getDimensions;\n    return {\n      reference: getRectRelativeToOffsetParent(reference, await getOffsetParentFn(floating), strategy),\n      floating: _extends({\n        x: 0,\n        y: 0\n      }, await getDimensionsFn(floating))\n    };\n  },\n  getClientRects: element => Array.from(element.getClientRects()),\n  isRTL: element => getComputedStyle(element).direction === 'rtl'\n};\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll: _ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = true,\n    animationFrame = false\n  } = options;\n  const ancestorScroll = _ancestorScroll && !animationFrame;\n  const ancestors = ancestorScroll || ancestorResize ? [...(isElement(reference) ? getOverflowAncestors(reference) : reference.contextElement ? getOverflowAncestors(reference.contextElement) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  let observer = null;\n  if (elementResize) {\n    let initialUpdate = true;\n    observer = new ResizeObserver(() => {\n      if (!initialUpdate) {\n        update();\n      }\n      initialUpdate = false;\n    });\n    isElement(reference) && !animationFrame && observer.observe(reference);\n    if (!isElement(reference) && reference.contextElement && !animationFrame) {\n      observer.observe(reference.contextElement);\n    }\n    observer.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _observer;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    (_observer = observer) == null ? void 0 : _observer.disconnect();\n    observer = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a reference element when it is given a certain CSS positioning\n * strategy.\n */\n\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = _extends({\n    platform\n  }, options);\n  const platformWithCache = _extends({}, mergedOptions.platform, {\n    _c: cache\n  });\n  return computePosition$1(reference, floating, _extends({}, mergedOptions, {\n    platform: platformWithCache\n  }));\n};\n\n/**\n * Floating UI Options\n *\n * @typedef {object} FloatingUIOptions\n */\n\n/**\n * Determines options for the tooltip and initializes event listeners.\n *\n * @param {Step} step The step instance\n *\n * @return {FloatingUIOptions}\n */\nfunction setupTooltip(step) {\n  if (step.cleanup) {\n    step.cleanup();\n  }\n  const attachToOptions = step._getResolvedAttachToOptions();\n  let target = attachToOptions.element;\n  const floatingUIOptions = getFloatingUIOptions(attachToOptions, step);\n  const shouldCenter = shouldCenterStep(attachToOptions);\n  if (shouldCenter) {\n    target = document.body;\n    const content = step.shepherdElementComponent.getElement();\n    content.classList.add('shepherd-centered');\n  }\n  step.cleanup = autoUpdate(target, step.el, () => {\n    // The element might have already been removed by the end of the tour.\n    if (!step.el) {\n      step.cleanup();\n      return;\n    }\n    setPosition(target, step, floatingUIOptions, shouldCenter);\n  });\n  step.target = attachToOptions.element;\n  return floatingUIOptions;\n}\n\n/**\n * Merge tooltip options handling nested keys.\n *\n * @param tourOptions - The default tour options.\n * @param options - Step specific options.\n *\n * @return {floatingUIOptions: FloatingUIOptions}\n */\nfunction mergeTooltipConfig(tourOptions, options) {\n  return {\n    floatingUIOptions: cjs(tourOptions.floatingUIOptions || {}, options.floatingUIOptions || {})\n  };\n}\n\n/**\n * Cleanup function called when the step is closed/destroyed.\n *\n * @param {Step} step\n */\nfunction destroyTooltip(step) {\n  if (step.cleanup) {\n    step.cleanup();\n  }\n  step.cleanup = null;\n}\n\n/**\n *\n * @return {Promise<*>}\n */\nfunction setPosition(target, step, floatingUIOptions, shouldCenter) {\n  return computePosition(target, step.el, floatingUIOptions).then(floatingUIposition(step, shouldCenter))\n  // Wait before forcing focus.\n  .then(step => new Promise(resolve => {\n    setTimeout(() => resolve(step), 300);\n  }))\n  // Replaces focusAfterRender modifier.\n  .then(step => {\n    if (step && step.el) {\n      step.el.focus({\n        preventScroll: true\n      });\n    }\n  });\n}\n\n/**\n *\n * @param step\n * @param shouldCenter\n * @return {function({x: *, y: *, placement: *, middlewareData: *}): Promise<unknown>}\n */\nfunction floatingUIposition(step, shouldCenter) {\n  return _ref => {\n    let {\n      x,\n      y,\n      placement,\n      middlewareData\n    } = _ref;\n    if (!step.el) {\n      return step;\n    }\n    if (shouldCenter) {\n      Object.assign(step.el.style, {\n        position: 'fixed',\n        left: '50%',\n        top: '50%',\n        transform: 'translate(-50%, -50%)'\n      });\n    } else {\n      Object.assign(step.el.style, {\n        position: 'absolute',\n        left: `${x}px`,\n        top: `${y}px`\n      });\n    }\n    step.el.dataset.popperPlacement = placement;\n    placeArrow(step.el, middlewareData);\n    return step;\n  };\n}\n\n/**\n *\n * @param el\n * @param middlewareData\n */\nfunction placeArrow(el, middlewareData) {\n  const arrowEl = el.querySelector('.shepherd-arrow');\n  if (arrowEl) {\n    let left, top, right, bottom;\n    if (middlewareData.arrow) {\n      const {\n        x: arrowX,\n        y: arrowY\n      } = middlewareData.arrow;\n      left = arrowX != null ? `${arrowX}px` : '';\n      top = arrowY != null ? `${arrowY}px` : '';\n    }\n    Object.assign(arrowEl.style, {\n      left,\n      top,\n      right,\n      bottom\n    });\n  }\n}\n\n/**\n * Gets the `Floating UI` options from a set of base `attachTo` options\n * @param attachToOptions\n * @param {Step} step The step instance\n * @return {Object}\n * @private\n */\nfunction getFloatingUIOptions(attachToOptions, step) {\n  const options = {\n    strategy: 'absolute',\n    middleware: []\n  };\n  const arrowEl = addArrow(step);\n  const shouldCenter = shouldCenterStep(attachToOptions);\n  if (!shouldCenter) {\n    options.middleware.push(flip(),\n    // Replicate PopperJS default behavior.\n    shift({\n      limiter: limitShift(),\n      crossAxis: true\n    }));\n    if (arrowEl) {\n      options.middleware.push(arrow({\n        element: arrowEl\n      }));\n    }\n    options.placement = attachToOptions.on;\n  }\n  return cjs(step.options.floatingUIOptions || {}, options);\n}\n\n/**\n * @param {Step} step\n * @return {HTMLElement|false|null}\n */\nfunction addArrow(step) {\n  if (step.options.arrow && step.el) {\n    return step.el.querySelector('.shepherd-arrow');\n  }\n  return false;\n}\n\nfunction noop() {}\nfunction assign(tar, src) {\n  // @ts-ignore\n  for (const k in src) tar[k] = src[k];\n  return tar;\n}\nfunction run(fn) {\n  return fn();\n}\nfunction blank_object() {\n  return Object.create(null);\n}\nfunction run_all(fns) {\n  fns.forEach(run);\n}\nfunction is_function(thing) {\n  return typeof thing === 'function';\n}\nfunction safe_not_equal(a, b) {\n  return a != a ? b == b : a !== b || a && typeof a === 'object' || typeof a === 'function';\n}\nfunction is_empty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction append(target, node) {\n  target.appendChild(node);\n}\nfunction insert(target, node, anchor) {\n  target.insertBefore(node, anchor || null);\n}\nfunction detach(node) {\n  if (node.parentNode) {\n    node.parentNode.removeChild(node);\n  }\n}\nfunction destroy_each(iterations, detaching) {\n  for (let i = 0; i < iterations.length; i += 1) {\n    if (iterations[i]) iterations[i].d(detaching);\n  }\n}\nfunction element(name) {\n  return document.createElement(name);\n}\nfunction svg_element(name) {\n  return document.createElementNS('http://www.w3.org/2000/svg', name);\n}\nfunction text(data) {\n  return document.createTextNode(data);\n}\nfunction space() {\n  return text(' ');\n}\nfunction empty() {\n  return text('');\n}\nfunction listen(node, event, handler, options) {\n  node.addEventListener(event, handler, options);\n  return () => node.removeEventListener(event, handler, options);\n}\nfunction attr(node, attribute, value) {\n  if (value == null) node.removeAttribute(attribute);else if (node.getAttribute(attribute) !== value) node.setAttribute(attribute, value);\n}\nfunction set_attributes(node, attributes) {\n  // @ts-ignore\n  const descriptors = Object.getOwnPropertyDescriptors(node.__proto__);\n  for (const key in attributes) {\n    if (attributes[key] == null) {\n      node.removeAttribute(key);\n    } else if (key === 'style') {\n      node.style.cssText = attributes[key];\n    } else if (key === '__value') {\n      node.value = node[key] = attributes[key];\n    } else if (descriptors[key] && descriptors[key].set) {\n      node[key] = attributes[key];\n    } else {\n      attr(node, key, attributes[key]);\n    }\n  }\n}\nfunction children(element) {\n  return Array.from(element.childNodes);\n}\nfunction toggle_class(element, name, toggle) {\n  element.classList[toggle ? 'add' : 'remove'](name);\n}\nlet current_component;\nfunction set_current_component(component) {\n  current_component = component;\n}\nfunction get_current_component() {\n  if (!current_component) throw new Error('Function called outside component initialization');\n  return current_component;\n}\n/**\n * The `onMount` function schedules a callback to run as soon as the component has been mounted to the DOM.\n * It must be called during the component's initialisation (but doesn't need to live *inside* the component;\n * it can be called from an external module).\n *\n * `onMount` does not run inside a [server-side component](/docs#run-time-server-side-component-api).\n *\n * https://svelte.dev/docs#run-time-svelte-onmount\n */\nfunction onMount(fn) {\n  get_current_component().$$.on_mount.push(fn);\n}\n/**\n * Schedules a callback to run immediately after the component has been updated.\n *\n * The first time the callback runs will be after the initial `onMount`\n */\nfunction afterUpdate(fn) {\n  get_current_component().$$.after_update.push(fn);\n}\nconst dirty_components = [];\nconst binding_callbacks = [];\nconst render_callbacks = [];\nconst flush_callbacks = [];\nconst resolved_promise = Promise.resolve();\nlet update_scheduled = false;\nfunction schedule_update() {\n  if (!update_scheduled) {\n    update_scheduled = true;\n    resolved_promise.then(flush);\n  }\n}\nfunction add_render_callback(fn) {\n  render_callbacks.push(fn);\n}\n// flush() calls callbacks in this order:\n// 1. All beforeUpdate callbacks, in order: parents before children\n// 2. All bind:this callbacks, in reverse order: children before parents.\n// 3. All afterUpdate callbacks, in order: parents before children. EXCEPT\n//    for afterUpdates called during the initial onMount, which are called in\n//    reverse order: children before parents.\n// Since callbacks might update component values, which could trigger another\n// call to flush(), the following steps guard against this:\n// 1. During beforeUpdate, any updated components will be added to the\n//    dirty_components array and will cause a reentrant call to flush(). Because\n//    the flush index is kept outside the function, the reentrant call will pick\n//    up where the earlier call left off and go through all dirty components. The\n//    current_component value is saved and restored so that the reentrant call will\n//    not interfere with the \"parent\" flush() call.\n// 2. bind:this callbacks cannot trigger new flush() calls.\n// 3. During afterUpdate, any updated components will NOT have their afterUpdate\n//    callback called a second time; the seen_callbacks set, outside the flush()\n//    function, guarantees this behavior.\nconst seen_callbacks = new Set();\nlet flushidx = 0; // Do *not* move this inside the flush() function\nfunction flush() {\n  const saved_component = current_component;\n  do {\n    // first, call beforeUpdate functions\n    // and update components\n    while (flushidx < dirty_components.length) {\n      const component = dirty_components[flushidx];\n      flushidx++;\n      set_current_component(component);\n      update(component.$$);\n    }\n    set_current_component(null);\n    dirty_components.length = 0;\n    flushidx = 0;\n    while (binding_callbacks.length) binding_callbacks.pop()();\n    // then, once components are updated, call\n    // afterUpdate functions. This may cause\n    // subsequent updates...\n    for (let i = 0; i < render_callbacks.length; i += 1) {\n      const callback = render_callbacks[i];\n      if (!seen_callbacks.has(callback)) {\n        // ...so guard against infinite loops\n        seen_callbacks.add(callback);\n        callback();\n      }\n    }\n    render_callbacks.length = 0;\n  } while (dirty_components.length);\n  while (flush_callbacks.length) {\n    flush_callbacks.pop()();\n  }\n  update_scheduled = false;\n  seen_callbacks.clear();\n  set_current_component(saved_component);\n}\nfunction update($$) {\n  if ($$.fragment !== null) {\n    $$.update();\n    run_all($$.before_update);\n    const dirty = $$.dirty;\n    $$.dirty = [-1];\n    $$.fragment && $$.fragment.p($$.ctx, dirty);\n    $$.after_update.forEach(add_render_callback);\n  }\n}\nconst outroing = new Set();\nlet outros;\nfunction group_outros() {\n  outros = {\n    r: 0,\n    c: [],\n    p: outros // parent group\n  };\n}\n\nfunction check_outros() {\n  if (!outros.r) {\n    run_all(outros.c);\n  }\n  outros = outros.p;\n}\nfunction transition_in(block, local) {\n  if (block && block.i) {\n    outroing.delete(block);\n    block.i(local);\n  }\n}\nfunction transition_out(block, local, detach, callback) {\n  if (block && block.o) {\n    if (outroing.has(block)) return;\n    outroing.add(block);\n    outros.c.push(() => {\n      outroing.delete(block);\n      if (callback) {\n        if (detach) block.d(1);\n        callback();\n      }\n    });\n    block.o(local);\n  } else if (callback) {\n    callback();\n  }\n}\nfunction get_spread_update(levels, updates) {\n  const update = {};\n  const to_null_out = {};\n  const accounted_for = {\n    $$scope: 1\n  };\n  let i = levels.length;\n  while (i--) {\n    const o = levels[i];\n    const n = updates[i];\n    if (n) {\n      for (const key in o) {\n        if (!(key in n)) to_null_out[key] = 1;\n      }\n      for (const key in n) {\n        if (!accounted_for[key]) {\n          update[key] = n[key];\n          accounted_for[key] = 1;\n        }\n      }\n      levels[i] = n;\n    } else {\n      for (const key in o) {\n        accounted_for[key] = 1;\n      }\n    }\n  }\n  for (const key in to_null_out) {\n    if (!(key in update)) update[key] = undefined;\n  }\n  return update;\n}\nfunction create_component(block) {\n  block && block.c();\n}\nfunction mount_component(component, target, anchor, customElement) {\n  const {\n    fragment,\n    after_update\n  } = component.$$;\n  fragment && fragment.m(target, anchor);\n  if (!customElement) {\n    // onMount happens before the initial afterUpdate\n    add_render_callback(() => {\n      const new_on_destroy = component.$$.on_mount.map(run).filter(is_function);\n      // if the component was destroyed immediately\n      // it will update the `$$.on_destroy` reference to `null`.\n      // the destructured on_destroy may still reference to the old array\n      if (component.$$.on_destroy) {\n        component.$$.on_destroy.push(...new_on_destroy);\n      } else {\n        // Edge case - component was destroyed immediately,\n        // most likely as a result of a binding initialising\n        run_all(new_on_destroy);\n      }\n      component.$$.on_mount = [];\n    });\n  }\n  after_update.forEach(add_render_callback);\n}\nfunction destroy_component(component, detaching) {\n  const $$ = component.$$;\n  if ($$.fragment !== null) {\n    run_all($$.on_destroy);\n    $$.fragment && $$.fragment.d(detaching);\n    // TODO null out other refs, including component.$$ (but need to\n    // preserve final state?)\n    $$.on_destroy = $$.fragment = null;\n    $$.ctx = [];\n  }\n}\nfunction make_dirty(component, i) {\n  if (component.$$.dirty[0] === -1) {\n    dirty_components.push(component);\n    schedule_update();\n    component.$$.dirty.fill(0);\n  }\n  component.$$.dirty[i / 31 | 0] |= 1 << i % 31;\n}\nfunction init(component, options, instance, create_fragment, not_equal, props, append_styles, dirty) {\n  if (dirty === void 0) {\n    dirty = [-1];\n  }\n  const parent_component = current_component;\n  set_current_component(component);\n  const $$ = component.$$ = {\n    fragment: null,\n    ctx: [],\n    // state\n    props,\n    update: noop,\n    not_equal,\n    bound: blank_object(),\n    // lifecycle\n    on_mount: [],\n    on_destroy: [],\n    on_disconnect: [],\n    before_update: [],\n    after_update: [],\n    context: new Map(options.context || (parent_component ? parent_component.$$.context : [])),\n    // everything else\n    callbacks: blank_object(),\n    dirty,\n    skip_bound: false,\n    root: options.target || parent_component.$$.root\n  };\n  append_styles && append_styles($$.root);\n  let ready = false;\n  $$.ctx = instance ? instance(component, options.props || {}, function (i, ret) {\n    const value = (arguments.length <= 2 ? 0 : arguments.length - 2) ? arguments.length <= 2 ? undefined : arguments[2] : ret;\n    if ($$.ctx && not_equal($$.ctx[i], $$.ctx[i] = value)) {\n      if (!$$.skip_bound && $$.bound[i]) $$.bound[i](value);\n      if (ready) make_dirty(component, i);\n    }\n    return ret;\n  }) : [];\n  $$.update();\n  ready = true;\n  run_all($$.before_update);\n  // `false` as a special case of no DOM component\n  $$.fragment = create_fragment ? create_fragment($$.ctx) : false;\n  if (options.target) {\n    if (options.hydrate) {\n      const nodes = children(options.target);\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      $$.fragment && $$.fragment.l(nodes);\n      nodes.forEach(detach);\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      $$.fragment && $$.fragment.c();\n    }\n    if (options.intro) transition_in(component.$$.fragment);\n    mount_component(component, options.target, options.anchor, options.customElement);\n    flush();\n  }\n  set_current_component(parent_component);\n}\n/**\n * Base class for Svelte components. Used when dev=false.\n */\nclass SvelteComponent {\n  $destroy() {\n    destroy_component(this, 1);\n    this.$destroy = noop;\n  }\n  $on(type, callback) {\n    if (!is_function(callback)) {\n      return noop;\n    }\n    const callbacks = this.$$.callbacks[type] || (this.$$.callbacks[type] = []);\n    callbacks.push(callback);\n    return () => {\n      const index = callbacks.indexOf(callback);\n      if (index !== -1) callbacks.splice(index, 1);\n    };\n  }\n  $set($$props) {\n    if (this.$$set && !is_empty($$props)) {\n      this.$$.skip_bound = true;\n      this.$$set($$props);\n      this.$$.skip_bound = false;\n    }\n  }\n}\n\n/* src/js/components/shepherd-button.svelte generated by Svelte v3.54.0 */\nfunction create_fragment$8(ctx) {\n  let button;\n  let button_aria_label_value;\n  let button_class_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      button = element(\"button\");\n      attr(button, \"aria-label\", button_aria_label_value = /*label*/ctx[3] ? /*label*/ctx[3] : null);\n      attr(button, \"class\", button_class_value = `${/*classes*/ctx[1] || ''} shepherd-button ${/*secondary*/ctx[4] ? 'shepherd-button-secondary' : ''}`);\n      button.disabled = /*disabled*/ctx[2];\n      attr(button, \"tabindex\", \"0\");\n    },\n    m(target, anchor) {\n      insert(target, button, anchor);\n      button.innerHTML = /*text*/ctx[5];\n      if (!mounted) {\n        dispose = listen(button, \"click\", function () {\n          if (is_function( /*action*/ctx[0])) /*action*/ctx[0].apply(this, arguments);\n        });\n        mounted = true;\n      }\n    },\n    p(new_ctx, _ref) {\n      let [dirty] = _ref;\n      ctx = new_ctx;\n      if (dirty & /*text*/32) button.innerHTML = /*text*/ctx[5];\n      if (dirty & /*label*/8 && button_aria_label_value !== (button_aria_label_value = /*label*/ctx[3] ? /*label*/ctx[3] : null)) {\n        attr(button, \"aria-label\", button_aria_label_value);\n      }\n      if (dirty & /*classes, secondary*/18 && button_class_value !== (button_class_value = `${/*classes*/ctx[1] || ''} shepherd-button ${/*secondary*/ctx[4] ? 'shepherd-button-secondary' : ''}`)) {\n        attr(button, \"class\", button_class_value);\n      }\n      if (dirty & /*disabled*/4) {\n        button.disabled = /*disabled*/ctx[2];\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(button);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction instance$8($$self, $$props, $$invalidate) {\n  let {\n    config,\n    step\n  } = $$props;\n  let action, classes, disabled, label, secondary, text;\n  function getConfigOption(option) {\n    if (isFunction(option)) {\n      return option = option.call(step);\n    }\n    return option;\n  }\n  $$self.$$set = $$props => {\n    if ('config' in $$props) $$invalidate(6, config = $$props.config);\n    if ('step' in $$props) $$invalidate(7, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*config, step*/192) {\n      {\n        $$invalidate(0, action = config.action ? config.action.bind(step.tour) : null);\n        $$invalidate(1, classes = config.classes);\n        $$invalidate(2, disabled = config.disabled ? getConfigOption(config.disabled) : false);\n        $$invalidate(3, label = config.label ? getConfigOption(config.label) : null);\n        $$invalidate(4, secondary = config.secondary);\n        $$invalidate(5, text = config.text ? getConfigOption(config.text) : null);\n      }\n    }\n  };\n  return [action, classes, disabled, label, secondary, text, config, step];\n}\nclass Shepherd_button extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$8, create_fragment$8, safe_not_equal, {\n      config: 6,\n      step: 7\n    });\n  }\n}\n\n/* src/js/components/shepherd-footer.svelte generated by Svelte v3.54.0 */\nfunction get_each_context(ctx, list, i) {\n  const child_ctx = ctx.slice();\n  child_ctx[2] = list[i];\n  return child_ctx;\n}\n\n// (24:4) {#if buttons}\nfunction create_if_block$3(ctx) {\n  let each_1_anchor;\n  let current;\n  let each_value = /*buttons*/ctx[1];\n  let each_blocks = [];\n  for (let i = 0; i < each_value.length; i += 1) {\n    each_blocks[i] = create_each_block(get_each_context(ctx, each_value, i));\n  }\n  const out = i => transition_out(each_blocks[i], 1, 1, () => {\n    each_blocks[i] = null;\n  });\n  return {\n    c() {\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        each_blocks[i].c();\n      }\n      each_1_anchor = empty();\n    },\n    m(target, anchor) {\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        each_blocks[i].m(target, anchor);\n      }\n      insert(target, each_1_anchor, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      if (dirty & /*buttons, step*/3) {\n        each_value = /*buttons*/ctx[1];\n        let i;\n        for (i = 0; i < each_value.length; i += 1) {\n          const child_ctx = get_each_context(ctx, each_value, i);\n          if (each_blocks[i]) {\n            each_blocks[i].p(child_ctx, dirty);\n            transition_in(each_blocks[i], 1);\n          } else {\n            each_blocks[i] = create_each_block(child_ctx);\n            each_blocks[i].c();\n            transition_in(each_blocks[i], 1);\n            each_blocks[i].m(each_1_anchor.parentNode, each_1_anchor);\n          }\n        }\n        group_outros();\n        for (i = each_value.length; i < each_blocks.length; i += 1) {\n          out(i);\n        }\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      for (let i = 0; i < each_value.length; i += 1) {\n        transition_in(each_blocks[i]);\n      }\n      current = true;\n    },\n    o(local) {\n      each_blocks = each_blocks.filter(Boolean);\n      for (let i = 0; i < each_blocks.length; i += 1) {\n        transition_out(each_blocks[i]);\n      }\n      current = false;\n    },\n    d(detaching) {\n      destroy_each(each_blocks, detaching);\n      if (detaching) detach(each_1_anchor);\n    }\n  };\n}\n\n// (25:8) {#each buttons as config}\nfunction create_each_block(ctx) {\n  let shepherdbutton;\n  let current;\n  shepherdbutton = new Shepherd_button({\n    props: {\n      config: /*config*/ctx[2],\n      step: /*step*/ctx[0]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdbutton.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdbutton, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdbutton_changes = {};\n      if (dirty & /*buttons*/2) shepherdbutton_changes.config = /*config*/ctx[2];\n      if (dirty & /*step*/1) shepherdbutton_changes.step = /*step*/ctx[0];\n      shepherdbutton.$set(shepherdbutton_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdbutton.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdbutton.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdbutton, detaching);\n    }\n  };\n}\nfunction create_fragment$7(ctx) {\n  let footer;\n  let current;\n  let if_block = /*buttons*/ctx[1] && create_if_block$3(ctx);\n  return {\n    c() {\n      footer = element(\"footer\");\n      if (if_block) if_block.c();\n      attr(footer, \"class\", \"shepherd-footer\");\n    },\n    m(target, anchor) {\n      insert(target, footer, anchor);\n      if (if_block) if_block.m(footer, null);\n      current = true;\n    },\n    p(ctx, _ref) {\n      let [dirty] = _ref;\n      if ( /*buttons*/ctx[1]) {\n        if (if_block) {\n          if_block.p(ctx, dirty);\n          if (dirty & /*buttons*/2) {\n            transition_in(if_block, 1);\n          }\n        } else {\n          if_block = create_if_block$3(ctx);\n          if_block.c();\n          transition_in(if_block, 1);\n          if_block.m(footer, null);\n        }\n      } else if (if_block) {\n        group_outros();\n        transition_out(if_block, 1, 1, () => {\n          if_block = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(footer);\n      if (if_block) if_block.d();\n    }\n  };\n}\nfunction instance$7($$self, $$props, $$invalidate) {\n  let buttons;\n  let {\n    step\n  } = $$props;\n  $$self.$$set = $$props => {\n    if ('step' in $$props) $$invalidate(0, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/1) {\n      $$invalidate(1, buttons = step.options.buttons);\n    }\n  };\n  return [step, buttons];\n}\nclass Shepherd_footer extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$7, create_fragment$7, safe_not_equal, {\n      step: 0\n    });\n  }\n}\n\n/* src/js/components/shepherd-cancel-icon.svelte generated by Svelte v3.54.0 */\nfunction create_fragment$6(ctx) {\n  let button;\n  let span;\n  let button_aria_label_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      button = element(\"button\");\n      span = element(\"span\");\n      span.textContent = \"×\";\n      attr(span, \"aria-hidden\", \"true\");\n      attr(button, \"aria-label\", button_aria_label_value = /*cancelIcon*/ctx[0].label ? /*cancelIcon*/ctx[0].label : 'Close Tour');\n      attr(button, \"class\", \"shepherd-cancel-icon\");\n      attr(button, \"type\", \"button\");\n    },\n    m(target, anchor) {\n      insert(target, button, anchor);\n      append(button, span);\n      if (!mounted) {\n        dispose = listen(button, \"click\", /*handleCancelClick*/ctx[1]);\n        mounted = true;\n      }\n    },\n    p(ctx, _ref) {\n      let [dirty] = _ref;\n      if (dirty & /*cancelIcon*/1 && button_aria_label_value !== (button_aria_label_value = /*cancelIcon*/ctx[0].label ? /*cancelIcon*/ctx[0].label : 'Close Tour')) {\n        attr(button, \"aria-label\", button_aria_label_value);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(button);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction instance$6($$self, $$props, $$invalidate) {\n  let {\n    cancelIcon,\n    step\n  } = $$props;\n\n  /**\n  * Add a click listener to the cancel link that cancels the tour\n  */\n  const handleCancelClick = e => {\n    e.preventDefault();\n    step.cancel();\n  };\n  $$self.$$set = $$props => {\n    if ('cancelIcon' in $$props) $$invalidate(0, cancelIcon = $$props.cancelIcon);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [cancelIcon, handleCancelClick, step];\n}\nclass Shepherd_cancel_icon extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$6, create_fragment$6, safe_not_equal, {\n      cancelIcon: 0,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-title.svelte generated by Svelte v3.54.0 */\nfunction create_fragment$5(ctx) {\n  let h3;\n  return {\n    c() {\n      h3 = element(\"h3\");\n      attr(h3, \"id\", /*labelId*/ctx[1]);\n      attr(h3, \"class\", \"shepherd-title\");\n    },\n    m(target, anchor) {\n      insert(target, h3, anchor);\n      /*h3_binding*/\n      ctx[3](h3);\n    },\n    p(ctx, _ref) {\n      let [dirty] = _ref;\n      if (dirty & /*labelId*/2) {\n        attr(h3, \"id\", /*labelId*/ctx[1]);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(h3);\n      /*h3_binding*/\n      ctx[3](null);\n    }\n  };\n}\nfunction instance$5($$self, $$props, $$invalidate) {\n  let {\n    labelId,\n    element,\n    title\n  } = $$props;\n  afterUpdate(() => {\n    if (isFunction(title)) {\n      $$invalidate(2, title = title());\n    }\n    $$invalidate(0, element.innerHTML = title, element);\n  });\n  function h3_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('labelId' in $$props) $$invalidate(1, labelId = $$props.labelId);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('title' in $$props) $$invalidate(2, title = $$props.title);\n  };\n  return [element, labelId, title, h3_binding];\n}\nclass Shepherd_title extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$5, create_fragment$5, safe_not_equal, {\n      labelId: 1,\n      element: 0,\n      title: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-header.svelte generated by Svelte v3.54.0 */\nfunction create_if_block_1$1(ctx) {\n  let shepherdtitle;\n  let current;\n  shepherdtitle = new Shepherd_title({\n    props: {\n      labelId: /*labelId*/ctx[0],\n      title: /*title*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdtitle.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdtitle, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdtitle_changes = {};\n      if (dirty & /*labelId*/1) shepherdtitle_changes.labelId = /*labelId*/ctx[0];\n      if (dirty & /*title*/4) shepherdtitle_changes.title = /*title*/ctx[2];\n      shepherdtitle.$set(shepherdtitle_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdtitle.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdtitle.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdtitle, detaching);\n    }\n  };\n}\n\n// (39:4) {#if cancelIcon && cancelIcon.enabled}\nfunction create_if_block$2(ctx) {\n  let shepherdcancelicon;\n  let current;\n  shepherdcancelicon = new Shepherd_cancel_icon({\n    props: {\n      cancelIcon: /*cancelIcon*/ctx[3],\n      step: /*step*/ctx[1]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdcancelicon.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdcancelicon, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdcancelicon_changes = {};\n      if (dirty & /*cancelIcon*/8) shepherdcancelicon_changes.cancelIcon = /*cancelIcon*/ctx[3];\n      if (dirty & /*step*/2) shepherdcancelicon_changes.step = /*step*/ctx[1];\n      shepherdcancelicon.$set(shepherdcancelicon_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdcancelicon.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdcancelicon.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdcancelicon, detaching);\n    }\n  };\n}\nfunction create_fragment$4(ctx) {\n  let header;\n  let t;\n  let current;\n  let if_block0 = /*title*/ctx[2] && create_if_block_1$1(ctx);\n  let if_block1 = /*cancelIcon*/ctx[3] && /*cancelIcon*/ctx[3].enabled && create_if_block$2(ctx);\n  return {\n    c() {\n      header = element(\"header\");\n      if (if_block0) if_block0.c();\n      t = space();\n      if (if_block1) if_block1.c();\n      attr(header, \"class\", \"shepherd-header\");\n    },\n    m(target, anchor) {\n      insert(target, header, anchor);\n      if (if_block0) if_block0.m(header, null);\n      append(header, t);\n      if (if_block1) if_block1.m(header, null);\n      current = true;\n    },\n    p(ctx, _ref) {\n      let [dirty] = _ref;\n      if ( /*title*/ctx[2]) {\n        if (if_block0) {\n          if_block0.p(ctx, dirty);\n          if (dirty & /*title*/4) {\n            transition_in(if_block0, 1);\n          }\n        } else {\n          if_block0 = create_if_block_1$1(ctx);\n          if_block0.c();\n          transition_in(if_block0, 1);\n          if_block0.m(header, t);\n        }\n      } else if (if_block0) {\n        group_outros();\n        transition_out(if_block0, 1, 1, () => {\n          if_block0 = null;\n        });\n        check_outros();\n      }\n      if ( /*cancelIcon*/ctx[3] && /*cancelIcon*/ctx[3].enabled) {\n        if (if_block1) {\n          if_block1.p(ctx, dirty);\n          if (dirty & /*cancelIcon*/8) {\n            transition_in(if_block1, 1);\n          }\n        } else {\n          if_block1 = create_if_block$2(ctx);\n          if_block1.c();\n          transition_in(if_block1, 1);\n          if_block1.m(header, null);\n        }\n      } else if (if_block1) {\n        group_outros();\n        transition_out(if_block1, 1, 1, () => {\n          if_block1 = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block0);\n      transition_in(if_block1);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block0);\n      transition_out(if_block1);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(header);\n      if (if_block0) if_block0.d();\n      if (if_block1) if_block1.d();\n    }\n  };\n}\nfunction instance$4($$self, $$props, $$invalidate) {\n  let {\n    labelId,\n    step\n  } = $$props;\n  let title, cancelIcon;\n  $$self.$$set = $$props => {\n    if ('labelId' in $$props) $$invalidate(0, labelId = $$props.labelId);\n    if ('step' in $$props) $$invalidate(1, step = $$props.step);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/2) {\n      {\n        $$invalidate(2, title = step.options.title);\n        $$invalidate(3, cancelIcon = step.options.cancelIcon);\n      }\n    }\n  };\n  return [labelId, step, title, cancelIcon];\n}\nclass Shepherd_header extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$4, create_fragment$4, safe_not_equal, {\n      labelId: 0,\n      step: 1\n    });\n  }\n}\n\n/* src/js/components/shepherd-text.svelte generated by Svelte v3.54.0 */\nfunction create_fragment$3(ctx) {\n  let div;\n  return {\n    c() {\n      div = element(\"div\");\n      attr(div, \"class\", \"shepherd-text\");\n      attr(div, \"id\", /*descriptionId*/ctx[1]);\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      /*div_binding*/\n      ctx[3](div);\n    },\n    p(ctx, _ref) {\n      let [dirty] = _ref;\n      if (dirty & /*descriptionId*/2) {\n        attr(div, \"id\", /*descriptionId*/ctx[1]);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(div);\n      /*div_binding*/\n      ctx[3](null);\n    }\n  };\n}\nfunction instance$3($$self, $$props, $$invalidate) {\n  let {\n    descriptionId,\n    element,\n    step\n  } = $$props;\n  afterUpdate(() => {\n    let {\n      text\n    } = step.options;\n    if (isFunction(text)) {\n      text = text.call(step);\n    }\n    if (isHTMLElement$1(text)) {\n      element.appendChild(text);\n    } else {\n      $$invalidate(0, element.innerHTML = text, element);\n    }\n  });\n  function div_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('descriptionId' in $$props) $$invalidate(1, descriptionId = $$props.descriptionId);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [element, descriptionId, step, div_binding];\n}\nclass Shepherd_text extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$3, create_fragment$3, safe_not_equal, {\n      descriptionId: 1,\n      element: 0,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-content.svelte generated by Svelte v3.54.0 */\nfunction create_if_block_2(ctx) {\n  let shepherdheader;\n  let current;\n  shepherdheader = new Shepherd_header({\n    props: {\n      labelId: /*labelId*/ctx[1],\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdheader.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdheader, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdheader_changes = {};\n      if (dirty & /*labelId*/2) shepherdheader_changes.labelId = /*labelId*/ctx[1];\n      if (dirty & /*step*/4) shepherdheader_changes.step = /*step*/ctx[2];\n      shepherdheader.$set(shepherdheader_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdheader.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdheader.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdheader, detaching);\n    }\n  };\n}\n\n// (28:2) {#if !isUndefined(step.options.text)}\nfunction create_if_block_1(ctx) {\n  let shepherdtext;\n  let current;\n  shepherdtext = new Shepherd_text({\n    props: {\n      descriptionId: /*descriptionId*/ctx[0],\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdtext.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdtext, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdtext_changes = {};\n      if (dirty & /*descriptionId*/1) shepherdtext_changes.descriptionId = /*descriptionId*/ctx[0];\n      if (dirty & /*step*/4) shepherdtext_changes.step = /*step*/ctx[2];\n      shepherdtext.$set(shepherdtext_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdtext.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdtext.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdtext, detaching);\n    }\n  };\n}\n\n// (35:2) {#if Array.isArray(step.options.buttons) && step.options.buttons.length}\nfunction create_if_block$1(ctx) {\n  let shepherdfooter;\n  let current;\n  shepherdfooter = new Shepherd_footer({\n    props: {\n      step: /*step*/ctx[2]\n    }\n  });\n  return {\n    c() {\n      create_component(shepherdfooter.$$.fragment);\n    },\n    m(target, anchor) {\n      mount_component(shepherdfooter, target, anchor);\n      current = true;\n    },\n    p(ctx, dirty) {\n      const shepherdfooter_changes = {};\n      if (dirty & /*step*/4) shepherdfooter_changes.step = /*step*/ctx[2];\n      shepherdfooter.$set(shepherdfooter_changes);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdfooter.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdfooter.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      destroy_component(shepherdfooter, detaching);\n    }\n  };\n}\nfunction create_fragment$2(ctx) {\n  let div;\n  let show_if_2 = !isUndefined( /*step*/ctx[2].options.title) || /*step*/ctx[2].options.cancelIcon && /*step*/ctx[2].options.cancelIcon.enabled;\n  let t0;\n  let show_if_1 = !isUndefined( /*step*/ctx[2].options.text);\n  let t1;\n  let show_if = Array.isArray( /*step*/ctx[2].options.buttons) && /*step*/ctx[2].options.buttons.length;\n  let current;\n  let if_block0 = show_if_2 && create_if_block_2(ctx);\n  let if_block1 = show_if_1 && create_if_block_1(ctx);\n  let if_block2 = show_if && create_if_block$1(ctx);\n  return {\n    c() {\n      div = element(\"div\");\n      if (if_block0) if_block0.c();\n      t0 = space();\n      if (if_block1) if_block1.c();\n      t1 = space();\n      if (if_block2) if_block2.c();\n      attr(div, \"class\", \"shepherd-content\");\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      if (if_block0) if_block0.m(div, null);\n      append(div, t0);\n      if (if_block1) if_block1.m(div, null);\n      append(div, t1);\n      if (if_block2) if_block2.m(div, null);\n      current = true;\n    },\n    p(ctx, _ref) {\n      let [dirty] = _ref;\n      if (dirty & /*step*/4) show_if_2 = !isUndefined( /*step*/ctx[2].options.title) || /*step*/ctx[2].options.cancelIcon && /*step*/ctx[2].options.cancelIcon.enabled;\n      if (show_if_2) {\n        if (if_block0) {\n          if_block0.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block0, 1);\n          }\n        } else {\n          if_block0 = create_if_block_2(ctx);\n          if_block0.c();\n          transition_in(if_block0, 1);\n          if_block0.m(div, t0);\n        }\n      } else if (if_block0) {\n        group_outros();\n        transition_out(if_block0, 1, 1, () => {\n          if_block0 = null;\n        });\n        check_outros();\n      }\n      if (dirty & /*step*/4) show_if_1 = !isUndefined( /*step*/ctx[2].options.text);\n      if (show_if_1) {\n        if (if_block1) {\n          if_block1.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block1, 1);\n          }\n        } else {\n          if_block1 = create_if_block_1(ctx);\n          if_block1.c();\n          transition_in(if_block1, 1);\n          if_block1.m(div, t1);\n        }\n      } else if (if_block1) {\n        group_outros();\n        transition_out(if_block1, 1, 1, () => {\n          if_block1 = null;\n        });\n        check_outros();\n      }\n      if (dirty & /*step*/4) show_if = Array.isArray( /*step*/ctx[2].options.buttons) && /*step*/ctx[2].options.buttons.length;\n      if (show_if) {\n        if (if_block2) {\n          if_block2.p(ctx, dirty);\n          if (dirty & /*step*/4) {\n            transition_in(if_block2, 1);\n          }\n        } else {\n          if_block2 = create_if_block$1(ctx);\n          if_block2.c();\n          transition_in(if_block2, 1);\n          if_block2.m(div, null);\n        }\n      } else if (if_block2) {\n        group_outros();\n        transition_out(if_block2, 1, 1, () => {\n          if_block2 = null;\n        });\n        check_outros();\n      }\n    },\n    i(local) {\n      if (current) return;\n      transition_in(if_block0);\n      transition_in(if_block1);\n      transition_in(if_block2);\n      current = true;\n    },\n    o(local) {\n      transition_out(if_block0);\n      transition_out(if_block1);\n      transition_out(if_block2);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n      if (if_block0) if_block0.d();\n      if (if_block1) if_block1.d();\n      if (if_block2) if_block2.d();\n    }\n  };\n}\nfunction instance$2($$self, $$props, $$invalidate) {\n  let {\n    descriptionId,\n    labelId,\n    step\n  } = $$props;\n  $$self.$$set = $$props => {\n    if ('descriptionId' in $$props) $$invalidate(0, descriptionId = $$props.descriptionId);\n    if ('labelId' in $$props) $$invalidate(1, labelId = $$props.labelId);\n    if ('step' in $$props) $$invalidate(2, step = $$props.step);\n  };\n  return [descriptionId, labelId, step];\n}\nclass Shepherd_content extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$2, create_fragment$2, safe_not_equal, {\n      descriptionId: 0,\n      labelId: 1,\n      step: 2\n    });\n  }\n}\n\n/* src/js/components/shepherd-element.svelte generated by Svelte v3.54.0 */\nfunction create_if_block(ctx) {\n  let div;\n  return {\n    c() {\n      div = element(\"div\");\n      attr(div, \"class\", \"shepherd-arrow\");\n      attr(div, \"data-popper-arrow\", \"\");\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n    }\n  };\n}\nfunction create_fragment$1(ctx) {\n  let div;\n  let t;\n  let shepherdcontent;\n  let div_aria_describedby_value;\n  let div_aria_labelledby_value;\n  let current;\n  let mounted;\n  let dispose;\n  let if_block = /*step*/ctx[4].options.arrow && /*step*/ctx[4].options.attachTo && /*step*/ctx[4].options.attachTo.element && /*step*/ctx[4].options.attachTo.on && create_if_block();\n  shepherdcontent = new Shepherd_content({\n    props: {\n      descriptionId: /*descriptionId*/ctx[2],\n      labelId: /*labelId*/ctx[3],\n      step: /*step*/ctx[4]\n    }\n  });\n  let div_levels = [{\n    \"aria-describedby\": div_aria_describedby_value = !isUndefined( /*step*/ctx[4].options.text) ? /*descriptionId*/ctx[2] : null\n  }, {\n    \"aria-labelledby\": div_aria_labelledby_value = /*step*/ctx[4].options.title ? /*labelId*/ctx[3] : null\n  }, /*dataStepId*/ctx[1], {\n    role: \"dialog\"\n  }, {\n    tabindex: \"0\"\n  }];\n  let div_data = {};\n  for (let i = 0; i < div_levels.length; i += 1) {\n    div_data = assign(div_data, div_levels[i]);\n  }\n  return {\n    c() {\n      div = element(\"div\");\n      if (if_block) if_block.c();\n      t = space();\n      create_component(shepherdcontent.$$.fragment);\n      set_attributes(div, div_data);\n      toggle_class(div, \"shepherd-has-cancel-icon\", /*hasCancelIcon*/ctx[5]);\n      toggle_class(div, \"shepherd-has-title\", /*hasTitle*/ctx[6]);\n      toggle_class(div, \"shepherd-element\", true);\n    },\n    m(target, anchor) {\n      insert(target, div, anchor);\n      if (if_block) if_block.m(div, null);\n      append(div, t);\n      mount_component(shepherdcontent, div, null);\n      /*div_binding*/\n      ctx[13](div);\n      current = true;\n      if (!mounted) {\n        dispose = listen(div, \"keydown\", /*handleKeyDown*/ctx[7]);\n        mounted = true;\n      }\n    },\n    p(ctx, _ref) {\n      let [dirty] = _ref;\n      if ( /*step*/ctx[4].options.arrow && /*step*/ctx[4].options.attachTo && /*step*/ctx[4].options.attachTo.element && /*step*/ctx[4].options.attachTo.on) {\n        if (if_block) ; else {\n          if_block = create_if_block();\n          if_block.c();\n          if_block.m(div, t);\n        }\n      } else if (if_block) {\n        if_block.d(1);\n        if_block = null;\n      }\n      const shepherdcontent_changes = {};\n      if (dirty & /*descriptionId*/4) shepherdcontent_changes.descriptionId = /*descriptionId*/ctx[2];\n      if (dirty & /*labelId*/8) shepherdcontent_changes.labelId = /*labelId*/ctx[3];\n      if (dirty & /*step*/16) shepherdcontent_changes.step = /*step*/ctx[4];\n      shepherdcontent.$set(shepherdcontent_changes);\n      set_attributes(div, div_data = get_spread_update(div_levels, [(!current || dirty & /*step, descriptionId*/20 && div_aria_describedby_value !== (div_aria_describedby_value = !isUndefined( /*step*/ctx[4].options.text) ? /*descriptionId*/ctx[2] : null)) && {\n        \"aria-describedby\": div_aria_describedby_value\n      }, (!current || dirty & /*step, labelId*/24 && div_aria_labelledby_value !== (div_aria_labelledby_value = /*step*/ctx[4].options.title ? /*labelId*/ctx[3] : null)) && {\n        \"aria-labelledby\": div_aria_labelledby_value\n      }, dirty & /*dataStepId*/2 && /*dataStepId*/ctx[1], {\n        role: \"dialog\"\n      }, {\n        tabindex: \"0\"\n      }]));\n      toggle_class(div, \"shepherd-has-cancel-icon\", /*hasCancelIcon*/ctx[5]);\n      toggle_class(div, \"shepherd-has-title\", /*hasTitle*/ctx[6]);\n      toggle_class(div, \"shepherd-element\", true);\n    },\n    i(local) {\n      if (current) return;\n      transition_in(shepherdcontent.$$.fragment, local);\n      current = true;\n    },\n    o(local) {\n      transition_out(shepherdcontent.$$.fragment, local);\n      current = false;\n    },\n    d(detaching) {\n      if (detaching) detach(div);\n      if (if_block) if_block.d();\n      destroy_component(shepherdcontent);\n      /*div_binding*/\n      ctx[13](null);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nconst KEY_TAB = 9;\nconst KEY_ESC = 27;\nconst LEFT_ARROW = 37;\nconst RIGHT_ARROW = 39;\nfunction getClassesArray(classes) {\n  return classes.split(' ').filter(className => !!className.length);\n}\nfunction instance$1($$self, $$props, $$invalidate) {\n  let {\n    classPrefix,\n    element,\n    descriptionId,\n    firstFocusableElement,\n    focusableElements,\n    labelId,\n    lastFocusableElement,\n    step,\n    dataStepId\n  } = $$props;\n  let hasCancelIcon, hasTitle, classes;\n  const getElement = () => element;\n  onMount(() => {\n    // Get all elements that are focusable\n    $$invalidate(1, dataStepId = {\n      [`data-${classPrefix}shepherd-step-id`]: step.id\n    });\n    $$invalidate(9, focusableElements = element.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex=\"0\"]'));\n    $$invalidate(8, firstFocusableElement = focusableElements[0]);\n    $$invalidate(10, lastFocusableElement = focusableElements[focusableElements.length - 1]);\n  });\n  afterUpdate(() => {\n    if (classes !== step.options.classes) {\n      updateDynamicClasses();\n    }\n  });\n  function updateDynamicClasses() {\n    removeClasses(classes);\n    classes = step.options.classes;\n    addClasses(classes);\n  }\n  function removeClasses(classes) {\n    if (isString(classes)) {\n      const oldClasses = getClassesArray(classes);\n      if (oldClasses.length) {\n        element.classList.remove(...oldClasses);\n      }\n    }\n  }\n  function addClasses(classes) {\n    if (isString(classes)) {\n      const newClasses = getClassesArray(classes);\n      if (newClasses.length) {\n        element.classList.add(...newClasses);\n      }\n    }\n  }\n\n  /**\n  * Setup keydown events to allow closing the modal with ESC\n  *\n  * Borrowed from this great post! https://bitsofco.de/accessible-modal-dialog/\n  *\n  * @private\n  */\n  const handleKeyDown = e => {\n    const {\n      tour\n    } = step;\n    switch (e.keyCode) {\n      case KEY_TAB:\n        if (focusableElements.length === 0) {\n          e.preventDefault();\n          break;\n        }\n        // Backward tab\n        if (e.shiftKey) {\n          if (document.activeElement === firstFocusableElement || document.activeElement.classList.contains('shepherd-element')) {\n            e.preventDefault();\n            lastFocusableElement.focus();\n          }\n        } else {\n          if (document.activeElement === lastFocusableElement) {\n            e.preventDefault();\n            firstFocusableElement.focus();\n          }\n        }\n        break;\n      case KEY_ESC:\n        if (tour.options.exitOnEsc) {\n          step.cancel();\n        }\n        break;\n      case LEFT_ARROW:\n        if (tour.options.keyboardNavigation) {\n          tour.back();\n        }\n        break;\n      case RIGHT_ARROW:\n        if (tour.options.keyboardNavigation) {\n          tour.next();\n        }\n        break;\n    }\n  };\n  function div_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('classPrefix' in $$props) $$invalidate(11, classPrefix = $$props.classPrefix);\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('descriptionId' in $$props) $$invalidate(2, descriptionId = $$props.descriptionId);\n    if ('firstFocusableElement' in $$props) $$invalidate(8, firstFocusableElement = $$props.firstFocusableElement);\n    if ('focusableElements' in $$props) $$invalidate(9, focusableElements = $$props.focusableElements);\n    if ('labelId' in $$props) $$invalidate(3, labelId = $$props.labelId);\n    if ('lastFocusableElement' in $$props) $$invalidate(10, lastFocusableElement = $$props.lastFocusableElement);\n    if ('step' in $$props) $$invalidate(4, step = $$props.step);\n    if ('dataStepId' in $$props) $$invalidate(1, dataStepId = $$props.dataStepId);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*step*/16) {\n      {\n        $$invalidate(5, hasCancelIcon = step.options && step.options.cancelIcon && step.options.cancelIcon.enabled);\n        $$invalidate(6, hasTitle = step.options && step.options.title);\n      }\n    }\n  };\n  return [element, dataStepId, descriptionId, labelId, step, hasCancelIcon, hasTitle, handleKeyDown, firstFocusableElement, focusableElements, lastFocusableElement, classPrefix, getElement, div_binding];\n}\nclass Shepherd_element extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance$1, create_fragment$1, safe_not_equal, {\n      classPrefix: 11,\n      element: 0,\n      descriptionId: 2,\n      firstFocusableElement: 8,\n      focusableElements: 9,\n      labelId: 3,\n      lastFocusableElement: 10,\n      step: 4,\n      dataStepId: 1,\n      getElement: 12\n    });\n  }\n  get getElement() {\n    return this.$$.ctx[12];\n  }\n}\n\n/**\n * A class representing steps to be added to a tour.\n * @extends {Evented}\n */\nclass Step extends Evented {\n  /**\n   * Create a step\n   * @param {Tour} tour The tour for the step\n   * @param {object} options The options for the step\n   * @param {boolean} options.arrow Whether to display the arrow for the tooltip or not. Defaults to `true`.\n   * @param {object} options.attachTo The element the step should be attached to on the page.\n   * An object with properties `element` and `on`.\n   *\n   * ```js\n   * const step = new Step(tour, {\n   *   attachTo: { element: '.some .selector-path', on: 'left' },\n   *   ...moreOptions\n   * });\n   * ```\n   *\n   * If you don’t specify an `attachTo` the element will appear in the middle of the screen. The same will happen if your `attachTo.element` callback returns `null`, `undefined`, or a selector that does not exist in the DOM.\n   * If you omit the `on` portion of `attachTo`, the element will still be highlighted, but the tooltip will appear\n   * in the middle of the screen, without an arrow pointing to the target.\n   * If the element to highlight does not yet exist while instantiating tour steps, you may use lazy evaluation by supplying a function to `attachTo.element`. The function will be called in the `before-show` phase.\n   * @param {string|HTMLElement|function} options.attachTo.element An element selector string, DOM element, or a function (returning a selector, a DOM element, `null` or `undefined`).\n   * @param {string} options.attachTo.on The optional direction to place the FloatingUI tooltip relative to the element.\n   *   - Possible string values: 'top', 'top-start', 'top-end', 'bottom', 'bottom-start', 'bottom-end', 'right', 'right-start', 'right-end', 'left', 'left-start', 'left-end'\n   * @param {Object} options.advanceOn An action on the page which should advance shepherd to the next step.\n   * It should be an object with a string `selector` and an `event` name\n   * ```js\n   * const step = new Step(tour, {\n   *   advanceOn: { selector: '.some .selector-path', event: 'click' },\n   *   ...moreOptions\n   * });\n   * ```\n   * `event` doesn’t have to be an event inside the tour, it can be any event fired on any element on the page.\n   * You can also always manually advance the Tour by calling `myTour.next()`.\n   * @param {function} options.beforeShowPromise A function that returns a promise.\n   * When the promise resolves, the rest of the `show` code for the step will execute.\n   * @param {Object[]} options.buttons An array of buttons to add to the step. These will be rendered in a\n   * footer below the main body text.\n   * @param {function} options.buttons.button.action A function executed when the button is clicked on.\n   * It is automatically bound to the `tour` the step is associated with, so things like `this.next` will\n   * work inside the action.\n   * You can use action to skip steps or navigate to specific steps, with something like:\n   * ```js\n   * action() {\n   *   return this.show('some_step_name');\n   * }\n   * ```\n   * @param {string} options.buttons.button.classes Extra classes to apply to the `<a>`\n   * @param {boolean} options.buttons.button.disabled Should the button be disabled?\n   * @param {string} options.buttons.button.label The aria-label text of the button\n   * @param {boolean} options.buttons.button.secondary If true, a shepherd-button-secondary class is applied to the button\n   * @param {string} options.buttons.button.text The HTML text of the button\n   * @param {boolean} options.canClickTarget A boolean, that when set to false, will set `pointer-events: none` on the target\n   * @param {object} options.cancelIcon Options for the cancel icon\n   * @param {boolean} options.cancelIcon.enabled Should a cancel “✕” be shown in the header of the step?\n   * @param {string} options.cancelIcon.label The label to add for `aria-label`\n   * @param {string} options.classes A string of extra classes to add to the step's content element.\n   * @param {string} options.highlightClass An extra class to apply to the `attachTo` element when it is\n   * highlighted (that is, when its step is active). You can then target that selector in your CSS.\n   * @param {string} options.id The string to use as the `id` for the step.\n   * @param {number} options.modalOverlayOpeningPadding An amount of padding to add around the modal overlay opening\n   * @param {number | { topLeft: number, bottomLeft: number, bottomRight: number, topRight: number }} options.modalOverlayOpeningRadius An amount of border radius to add around the modal overlay opening\n   * @param {object} options.floatingUIOptions Extra options to pass to FloatingUI\n   * @param {boolean|Object} options.scrollTo Should the element be scrolled to when this step is shown? If true, uses the default `scrollIntoView`,\n   * if an object, passes that object as the params to `scrollIntoView` i.e. `{behavior: 'smooth', block: 'center'}`\n   * @param {function} options.scrollToHandler A function that lets you override the default scrollTo behavior and\n   * define a custom action to do the scrolling, and possibly other logic.\n   * @param {function} options.showOn A function that, when it returns `true`, will show the step.\n   * If it returns false, the step will be skipped.\n   * @param {string} options.text The text in the body of the step. It can be one of three types:\n   * ```\n   * - HTML string\n   * - `HTMLElement` object\n   * - `Function` to be executed when the step is built. It must return one the two options above.\n   * ```\n   * @param {string} options.title The step's title. It becomes an `h3` at the top of the step. It can be one of two types:\n   * ```\n   * - HTML string\n   * - `Function` to be executed when the step is built. It must return HTML string.\n   * ```\n   * @param {object} options.when You can define `show`, `hide`, etc events inside `when`. For example:\n   * ```js\n   * when: {\n   *   show: function() {\n   *     window.scrollTo(0, 0);\n   *   }\n   * }\n   * ```\n   * @return {Step} The newly created Step instance\n   */\n  constructor(tour, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    super(tour, options);\n    this.tour = tour;\n    this.classPrefix = this.tour.options ? normalizePrefix(this.tour.options.classPrefix) : '';\n    this.styles = tour.styles;\n\n    /**\n     * Resolved attachTo options. Due to lazy evaluation, we only resolve the options during `before-show` phase.\n     * Do not use this directly, use the _getResolvedAttachToOptions method instead.\n     * @type {null|{}|{element, to}}\n     * @private\n     */\n    this._resolvedAttachTo = null;\n    autoBind(this);\n    this._setOptions(options);\n    return this;\n  }\n\n  /**\n   * Cancel the tour\n   * Triggers the `cancel` event\n   */\n  cancel() {\n    this.tour.cancel();\n    this.trigger('cancel');\n  }\n\n  /**\n   * Complete the tour\n   * Triggers the `complete` event\n   */\n  complete() {\n    this.tour.complete();\n    this.trigger('complete');\n  }\n\n  /**\n   * Remove the step, delete the step's element, and destroy the FloatingUI instance for the step.\n   * Triggers `destroy` event\n   */\n  destroy() {\n    destroyTooltip(this);\n    if (isHTMLElement$1(this.el)) {\n      this.el.remove();\n      this.el = null;\n    }\n    this._updateStepTargetOnHide();\n    this.trigger('destroy');\n  }\n\n  /**\n   * Returns the tour for the step\n   * @return {Tour} The tour instance\n   */\n  getTour() {\n    return this.tour;\n  }\n\n  /**\n   * Hide the step\n   */\n  hide() {\n    this.tour.modal.hide();\n    this.trigger('before-hide');\n    if (this.el) {\n      this.el.hidden = true;\n    }\n    this._updateStepTargetOnHide();\n    this.trigger('hide');\n  }\n\n  /**\n   * Resolves attachTo options.\n   * @returns {{}|{element, on}}\n   * @private\n   */\n  _resolveAttachToOptions() {\n    this._resolvedAttachTo = parseAttachTo(this);\n    return this._resolvedAttachTo;\n  }\n\n  /**\n   * A selector for resolved attachTo options.\n   * @returns {{}|{element, on}}\n   * @private\n   */\n  _getResolvedAttachToOptions() {\n    if (this._resolvedAttachTo === null) {\n      return this._resolveAttachToOptions();\n    }\n    return this._resolvedAttachTo;\n  }\n\n  /**\n   * Check if the step is open and visible\n   * @return {boolean} True if the step is open and visible\n   */\n  isOpen() {\n    return Boolean(this.el && !this.el.hidden);\n  }\n\n  /**\n   * Wraps `_show` and ensures `beforeShowPromise` resolves before calling show\n   * @return {*|Promise}\n   */\n  show() {\n    if (isFunction(this.options.beforeShowPromise)) {\n      return Promise.resolve(this.options.beforeShowPromise()).then(() => this._show());\n    }\n    return Promise.resolve(this._show());\n  }\n\n  /**\n   * Updates the options of the step.\n   *\n   * @param {Object} options The options for the step\n   */\n  updateStepOptions(options) {\n    Object.assign(this.options, options);\n    if (this.shepherdElementComponent) {\n      this.shepherdElementComponent.$set({\n        step: this\n      });\n    }\n  }\n\n  /**\n   * Returns the element for the step\n   * @return {HTMLElement|null|undefined} The element instance. undefined if it has never been shown, null if it has been destroyed\n   */\n  getElement() {\n    return this.el;\n  }\n\n  /**\n   * Returns the target for the step\n   * @return {HTMLElement|null|undefined} The element instance. undefined if it has never been shown, null if query string has not been found\n   */\n  getTarget() {\n    return this.target;\n  }\n\n  /**\n   * Creates Shepherd element for step based on options\n   *\n   * @return {Element} The DOM element for the step tooltip\n   * @private\n   */\n  _createTooltipContent() {\n    const descriptionId = `${this.id}-description`;\n    const labelId = `${this.id}-label`;\n    this.shepherdElementComponent = new Shepherd_element({\n      target: this.tour.options.stepsContainer || document.body,\n      props: {\n        classPrefix: this.classPrefix,\n        descriptionId,\n        labelId,\n        step: this,\n        styles: this.styles\n      }\n    });\n    return this.shepherdElementComponent.getElement();\n  }\n\n  /**\n   * If a custom scrollToHandler is defined, call that, otherwise do the generic\n   * scrollIntoView call.\n   *\n   * @param {boolean|Object} scrollToOptions If true, uses the default `scrollIntoView`,\n   * if an object, passes that object as the params to `scrollIntoView` i.e. `{ behavior: 'smooth', block: 'center' }`\n   * @private\n   */\n  _scrollTo(scrollToOptions) {\n    const {\n      element\n    } = this._getResolvedAttachToOptions();\n    if (isFunction(this.options.scrollToHandler)) {\n      this.options.scrollToHandler(element);\n    } else if (isElement$1(element) && typeof element.scrollIntoView === 'function') {\n      element.scrollIntoView(scrollToOptions);\n    }\n  }\n\n  /**\n   * _getClassOptions gets all possible classes for the step\n   * @param {Object} stepOptions The step specific options\n   * @returns {String} unique string from array of classes\n   * @private\n   */\n  _getClassOptions(stepOptions) {\n    const defaultStepOptions = this.tour && this.tour.options && this.tour.options.defaultStepOptions;\n    const stepClasses = stepOptions.classes ? stepOptions.classes : '';\n    const defaultStepOptionsClasses = defaultStepOptions && defaultStepOptions.classes ? defaultStepOptions.classes : '';\n    const allClasses = [...stepClasses.split(' '), ...defaultStepOptionsClasses.split(' ')];\n    const uniqClasses = new Set(allClasses);\n    return Array.from(uniqClasses).join(' ').trim();\n  }\n\n  /**\n   * Sets the options for the step, maps `when` to events, sets up buttons\n   * @param {Object} options The options for the step\n   * @private\n   */\n  _setOptions(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    let tourOptions = this.tour && this.tour.options && this.tour.options.defaultStepOptions;\n    tourOptions = cjs({}, tourOptions || {});\n    this.options = Object.assign({\n      arrow: true\n    }, tourOptions, options, mergeTooltipConfig(tourOptions, options));\n    const {\n      when\n    } = this.options;\n    this.options.classes = this._getClassOptions(options);\n    this.destroy();\n    this.id = this.options.id || `step-${uuid()}`;\n    if (when) {\n      Object.keys(when).forEach(event => {\n        this.on(event, when[event], this);\n      });\n    }\n  }\n\n  /**\n   * Create the element and set up the FloatingUI instance\n   * @private\n   */\n  _setupElements() {\n    if (!isUndefined(this.el)) {\n      this.destroy();\n    }\n    this.el = this._createTooltipContent();\n    if (this.options.advanceOn) {\n      bindAdvance(this);\n    }\n\n    // The tooltip implementation details are handled outside of the Step\n    // object.\n    setupTooltip(this);\n  }\n\n  /**\n   * Triggers `before-show`, generates the tooltip DOM content,\n   * sets up a FloatingUI instance for the tooltip, then triggers `show`.\n   * @private\n   */\n  _show() {\n    this.trigger('before-show');\n\n    // Force resolve to make sure the options are updated on subsequent shows.\n    this._resolveAttachToOptions();\n    this._setupElements();\n    if (!this.tour.modal) {\n      this.tour._setupModal();\n    }\n    this.tour.modal.setupForStep(this);\n    this._styleTargetElementForStep(this);\n    this.el.hidden = false;\n\n    // start scrolling to target before showing the step\n    if (this.options.scrollTo) {\n      setTimeout(() => {\n        this._scrollTo(this.options.scrollTo);\n      });\n    }\n    this.el.hidden = false;\n    const content = this.shepherdElementComponent.getElement();\n    const target = this.target || document.body;\n    target.classList.add(`${this.classPrefix}shepherd-enabled`);\n    target.classList.add(`${this.classPrefix}shepherd-target`);\n    content.classList.add('shepherd-enabled');\n    this.trigger('show');\n  }\n\n  /**\n   * Modulates the styles of the passed step's target element, based on the step's options and\n   * the tour's `modal` option, to visually emphasize the element\n   *\n   * @param step The step object that attaches to the element\n   * @private\n   */\n  _styleTargetElementForStep(step) {\n    const targetElement = step.target;\n    if (!targetElement) {\n      return;\n    }\n    if (step.options.highlightClass) {\n      targetElement.classList.add(step.options.highlightClass);\n    }\n    targetElement.classList.remove('shepherd-target-click-disabled');\n    if (step.options.canClickTarget === false) {\n      targetElement.classList.add('shepherd-target-click-disabled');\n    }\n  }\n\n  /**\n   * When a step is hidden, remove the highlightClass and 'shepherd-enabled'\n   * and 'shepherd-target' classes\n   * @private\n   */\n  _updateStepTargetOnHide() {\n    const target = this.target || document.body;\n    if (this.options.highlightClass) {\n      target.classList.remove(this.options.highlightClass);\n    }\n    target.classList.remove('shepherd-target-click-disabled', `${this.classPrefix}shepherd-enabled`, `${this.classPrefix}shepherd-target`);\n  }\n}\n\n/**\n * Cleanup the steps and set pointerEvents back to 'auto'\n * @param tour The tour object\n */\nfunction cleanupSteps(tour) {\n  if (tour) {\n    const {\n      steps\n    } = tour;\n    steps.forEach(step => {\n      if (step.options && step.options.canClickTarget === false && step.options.attachTo) {\n        if (step.target instanceof HTMLElement) {\n          step.target.classList.remove('shepherd-target-click-disabled');\n        }\n      }\n    });\n  }\n}\n\n/**\n * Generates the svg path data for a rounded rectangle overlay\n * @param {Object} dimension - Dimensions of rectangle.\n * @param {number} width - Width.\n * @param {number} height - Height.\n * @param {number} [x=0] - Offset from top left corner in x axis. default 0.\n * @param {number} [y=0] - Offset from top left corner in y axis. default 0.\n * @param {number | { topLeft: number, topRight: number, bottomRight: number, bottomLeft: number }} [r=0] - Corner Radius. Keep this smaller than half of width or height.\n * @returns {string} - Rounded rectangle overlay path data.\n */\nfunction makeOverlayPath(_ref) {\n  let {\n    width,\n    height,\n    x = 0,\n    y = 0,\n    r = 0\n  } = _ref;\n  const {\n    innerWidth: w,\n    innerHeight: h\n  } = window;\n  const {\n    topLeft = 0,\n    topRight = 0,\n    bottomRight = 0,\n    bottomLeft = 0\n  } = typeof r === 'number' ? {\n    topLeft: r,\n    topRight: r,\n    bottomRight: r,\n    bottomLeft: r\n  } : r;\n  return `M${w},${h}\\\nH0\\\nV0\\\nH${w}\\\nV${h}\\\nZ\\\nM${x + topLeft},${y}\\\na${topLeft},${topLeft},0,0,0-${topLeft},${topLeft}\\\nV${height + y - bottomLeft}\\\na${bottomLeft},${bottomLeft},0,0,0,${bottomLeft},${bottomLeft}\\\nH${width + x - bottomRight}\\\na${bottomRight},${bottomRight},0,0,0,${bottomRight}-${bottomRight}\\\nV${y + topRight}\\\na${topRight},${topRight},0,0,0-${topRight}-${topRight}\\\nZ`;\n}\n\n/* src/js/components/shepherd-modal.svelte generated by Svelte v3.54.0 */\nfunction create_fragment(ctx) {\n  let svg;\n  let path;\n  let svg_class_value;\n  let mounted;\n  let dispose;\n  return {\n    c() {\n      svg = svg_element(\"svg\");\n      path = svg_element(\"path\");\n      attr(path, \"d\", /*pathDefinition*/ctx[2]);\n      attr(svg, \"class\", svg_class_value = `${/*modalIsVisible*/ctx[1] ? 'shepherd-modal-is-visible' : ''} shepherd-modal-overlay-container`);\n    },\n    m(target, anchor) {\n      insert(target, svg, anchor);\n      append(svg, path);\n      /*svg_binding*/\n      ctx[11](svg);\n      if (!mounted) {\n        dispose = listen(svg, \"touchmove\", /*_preventModalOverlayTouch*/ctx[3]);\n        mounted = true;\n      }\n    },\n    p(ctx, _ref) {\n      let [dirty] = _ref;\n      if (dirty & /*pathDefinition*/4) {\n        attr(path, \"d\", /*pathDefinition*/ctx[2]);\n      }\n      if (dirty & /*modalIsVisible*/2 && svg_class_value !== (svg_class_value = `${/*modalIsVisible*/ctx[1] ? 'shepherd-modal-is-visible' : ''} shepherd-modal-overlay-container`)) {\n        attr(svg, \"class\", svg_class_value);\n      }\n    },\n    i: noop,\n    o: noop,\n    d(detaching) {\n      if (detaching) detach(svg);\n      /*svg_binding*/\n      ctx[11](null);\n      mounted = false;\n      dispose();\n    }\n  };\n}\nfunction _getScrollParent(element) {\n  if (!element) {\n    return null;\n  }\n  const isHtmlElement = element instanceof HTMLElement;\n  const overflowY = isHtmlElement && window.getComputedStyle(element).overflowY;\n  const isScrollable = overflowY !== 'hidden' && overflowY !== 'visible';\n  if (isScrollable && element.scrollHeight >= element.clientHeight) {\n    return element;\n  }\n  return _getScrollParent(element.parentElement);\n}\n\n/**\n * Get the visible height of the target element relative to its scrollParent.\n * If there is no scroll parent, the height of the element is returned.\n *\n * @param {HTMLElement} element The target element\n * @param {HTMLElement} [scrollParent] The scrollable parent element\n * @returns {{y: number, height: number}}\n * @private\n */\nfunction _getVisibleHeight(element, scrollParent) {\n  const elementRect = element.getBoundingClientRect();\n  let top = elementRect.y || elementRect.top;\n  let bottom = elementRect.bottom || top + elementRect.height;\n  if (scrollParent) {\n    const scrollRect = scrollParent.getBoundingClientRect();\n    const scrollTop = scrollRect.y || scrollRect.top;\n    const scrollBottom = scrollRect.bottom || scrollTop + scrollRect.height;\n    top = Math.max(top, scrollTop);\n    bottom = Math.min(bottom, scrollBottom);\n  }\n  const height = Math.max(bottom - top, 0); // Default to 0 if height is negative\n  return {\n    y: top,\n    height\n  };\n}\nfunction instance($$self, $$props, $$invalidate) {\n  let {\n    element,\n    openingProperties\n  } = $$props;\n  uuid();\n  let modalIsVisible = false;\n  let rafId = undefined;\n  let pathDefinition;\n  closeModalOpening();\n  const getElement = () => element;\n  function closeModalOpening() {\n    $$invalidate(4, openingProperties = {\n      width: 0,\n      height: 0,\n      x: 0,\n      y: 0,\n      r: 0\n    });\n  }\n  function hide() {\n    $$invalidate(1, modalIsVisible = false);\n\n    // Ensure we cleanup all event listeners when we hide the modal\n    _cleanupStepEventListeners();\n  }\n  function positionModal(modalOverlayOpeningPadding, modalOverlayOpeningRadius, scrollParent, targetElement) {\n    if (modalOverlayOpeningPadding === void 0) {\n      modalOverlayOpeningPadding = 0;\n    }\n    if (modalOverlayOpeningRadius === void 0) {\n      modalOverlayOpeningRadius = 0;\n    }\n    if (targetElement) {\n      const {\n        y,\n        height\n      } = _getVisibleHeight(targetElement, scrollParent);\n      const {\n        x,\n        width,\n        left\n      } = targetElement.getBoundingClientRect();\n\n      // getBoundingClientRect is not consistent. Some browsers use x and y, while others use left and top\n      $$invalidate(4, openingProperties = {\n        width: width + modalOverlayOpeningPadding * 2,\n        height: height + modalOverlayOpeningPadding * 2,\n        x: (x || left) - modalOverlayOpeningPadding,\n        y: y - modalOverlayOpeningPadding,\n        r: modalOverlayOpeningRadius\n      });\n    } else {\n      closeModalOpening();\n    }\n  }\n  function setupForStep(step) {\n    // Ensure we move listeners from the previous step, before we setup new ones\n    _cleanupStepEventListeners();\n    if (step.tour.options.useModalOverlay) {\n      _styleForStep(step);\n      show();\n    } else {\n      hide();\n    }\n  }\n  function show() {\n    $$invalidate(1, modalIsVisible = true);\n  }\n  const _preventModalBodyTouch = e => {\n    e.preventDefault();\n  };\n  const _preventModalOverlayTouch = e => {\n    e.stopPropagation();\n  };\n\n  /**\n  * Add touchmove event listener\n  * @private\n  */\n  function _addStepEventListeners() {\n    // Prevents window from moving on touch.\n    window.addEventListener('touchmove', _preventModalBodyTouch, {\n      passive: false\n    });\n  }\n\n  /**\n  * Cancel the requestAnimationFrame loop and remove touchmove event listeners\n  * @private\n  */\n  function _cleanupStepEventListeners() {\n    if (rafId) {\n      cancelAnimationFrame(rafId);\n      rafId = undefined;\n    }\n    window.removeEventListener('touchmove', _preventModalBodyTouch, {\n      passive: false\n    });\n  }\n\n  /**\n  * Style the modal for the step\n  * @param {Step} step The step to style the opening for\n  * @private\n  */\n  function _styleForStep(step) {\n    const {\n      modalOverlayOpeningPadding,\n      modalOverlayOpeningRadius\n    } = step.options;\n    const scrollParent = _getScrollParent(step.target);\n\n    // Setup recursive function to call requestAnimationFrame to update the modal opening position\n    const rafLoop = () => {\n      rafId = undefined;\n      positionModal(modalOverlayOpeningPadding, modalOverlayOpeningRadius, scrollParent, step.target);\n      rafId = requestAnimationFrame(rafLoop);\n    };\n    rafLoop();\n    _addStepEventListeners();\n  }\n  function svg_binding($$value) {\n    binding_callbacks[$$value ? 'unshift' : 'push'](() => {\n      element = $$value;\n      $$invalidate(0, element);\n    });\n  }\n  $$self.$$set = $$props => {\n    if ('element' in $$props) $$invalidate(0, element = $$props.element);\n    if ('openingProperties' in $$props) $$invalidate(4, openingProperties = $$props.openingProperties);\n  };\n  $$self.$$.update = () => {\n    if ($$self.$$.dirty & /*openingProperties*/16) {\n      $$invalidate(2, pathDefinition = makeOverlayPath(openingProperties));\n    }\n  };\n  return [element, modalIsVisible, pathDefinition, _preventModalOverlayTouch, openingProperties, getElement, closeModalOpening, hide, positionModal, setupForStep, show, svg_binding];\n}\nclass Shepherd_modal extends SvelteComponent {\n  constructor(options) {\n    super();\n    init(this, options, instance, create_fragment, safe_not_equal, {\n      element: 0,\n      openingProperties: 4,\n      getElement: 5,\n      closeModalOpening: 6,\n      hide: 7,\n      positionModal: 8,\n      setupForStep: 9,\n      show: 10\n    });\n  }\n  get getElement() {\n    return this.$$.ctx[5];\n  }\n  get closeModalOpening() {\n    return this.$$.ctx[6];\n  }\n  get hide() {\n    return this.$$.ctx[7];\n  }\n  get positionModal() {\n    return this.$$.ctx[8];\n  }\n  get setupForStep() {\n    return this.$$.ctx[9];\n  }\n  get show() {\n    return this.$$.ctx[10];\n  }\n}\n\nconst Shepherd = new Evented();\n\n/**\n * Class representing the site tour\n * @extends {Evented}\n */\nclass Tour extends Evented {\n  /**\n   * @param {Object} options The options for the tour\n   * @param {boolean} options.confirmCancel If true, will issue a `window.confirm` before cancelling\n   * @param {string} options.confirmCancelMessage The message to display in the confirm dialog\n   * @param {string} options.classPrefix The prefix to add to the `shepherd-enabled` and `shepherd-target` class names as well as the `data-shepherd-step-id`.\n   * @param {Object} options.defaultStepOptions Default options for Steps ({@link Step#constructor}), created through `addStep`\n   * @param {boolean} options.exitOnEsc Exiting the tour with the escape key will be enabled unless this is explicitly\n   * set to false.\n   * @param {boolean} options.keyboardNavigation Navigating the tour via left and right arrow keys will be enabled\n   * unless this is explicitly set to false.\n   * @param {HTMLElement} options.stepsContainer An optional container element for the steps.\n   * If not set, the steps will be appended to `document.body`.\n   * @param {HTMLElement} options.modalContainer An optional container element for the modal.\n   * If not set, the modal will be appended to `document.body`.\n   * @param {object[] | Step[]} options.steps An array of step options objects or Step instances to initialize the tour with\n   * @param {string} options.tourName An optional \"name\" for the tour. This will be appended to the the tour's\n   * dynamically generated `id` property.\n   * @param {boolean} options.useModalOverlay Whether or not steps should be placed above a darkened\n   * modal overlay. If true, the overlay will create an opening around the target element so that it\n   * can remain interactive\n   * @returns {Tour}\n   */\n  constructor(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    super(options);\n    autoBind(this);\n    const defaultTourOptions = {\n      exitOnEsc: true,\n      keyboardNavigation: true\n    };\n    this.options = Object.assign({}, defaultTourOptions, options);\n    this.classPrefix = normalizePrefix(this.options.classPrefix);\n    this.steps = [];\n    this.addSteps(this.options.steps);\n\n    // Pass these events onto the global Shepherd object\n    const events = ['active', 'cancel', 'complete', 'inactive', 'show', 'start'];\n    events.map(event => {\n      (e => {\n        this.on(e, opts => {\n          opts = opts || {};\n          opts.tour = this;\n          Shepherd.trigger(e, opts);\n        });\n      })(event);\n    });\n    this._setTourID();\n    return this;\n  }\n\n  /**\n   * Adds a new step to the tour\n   * @param {Object|Step} options An object containing step options or a Step instance\n   * @param {number} index The optional index to insert the step at. If undefined, the step\n   * is added to the end of the array.\n   * @return {Step} The newly added step\n   */\n  addStep(options, index) {\n    let step = options;\n    if (!(step instanceof Step)) {\n      step = new Step(this, step);\n    } else {\n      step.tour = this;\n    }\n    if (!isUndefined(index)) {\n      this.steps.splice(index, 0, step);\n    } else {\n      this.steps.push(step);\n    }\n    return step;\n  }\n\n  /**\n   * Add multiple steps to the tour\n   * @param {Array<object> | Array<Step>} steps The steps to add to the tour\n   */\n  addSteps(steps) {\n    if (Array.isArray(steps)) {\n      steps.forEach(step => {\n        this.addStep(step);\n      });\n    }\n    return this;\n  }\n\n  /**\n   * Go to the previous step in the tour\n   */\n  back() {\n    const index = this.steps.indexOf(this.currentStep);\n    this.show(index - 1, false);\n  }\n\n  /**\n   * Calls _done() triggering the 'cancel' event\n   * If `confirmCancel` is true, will show a window.confirm before cancelling\n   */\n  cancel() {\n    if (this.options.confirmCancel) {\n      const cancelMessage = this.options.confirmCancelMessage || 'Are you sure you want to stop the tour?';\n      const stopTour = window.confirm(cancelMessage);\n      if (stopTour) {\n        this._done('cancel');\n      }\n    } else {\n      this._done('cancel');\n    }\n  }\n\n  /**\n   * Calls _done() triggering the `complete` event\n   */\n  complete() {\n    this._done('complete');\n  }\n\n  /**\n   * Gets the step from a given id\n   * @param {Number|String} id The id of the step to retrieve\n   * @return {Step} The step corresponding to the `id`\n   */\n  getById(id) {\n    return this.steps.find(step => {\n      return step.id === id;\n    });\n  }\n\n  /**\n   * Gets the current step\n   * @returns {Step|null}\n   */\n  getCurrentStep() {\n    return this.currentStep;\n  }\n\n  /**\n   * Hide the current step\n   */\n  hide() {\n    const currentStep = this.getCurrentStep();\n    if (currentStep) {\n      return currentStep.hide();\n    }\n  }\n\n  /**\n   * Check if the tour is active\n   * @return {boolean}\n   */\n  isActive() {\n    return Shepherd.activeTour === this;\n  }\n\n  /**\n   * Go to the next step in the tour\n   * If we are at the end, call `complete`\n   */\n  next() {\n    const index = this.steps.indexOf(this.currentStep);\n    if (index === this.steps.length - 1) {\n      this.complete();\n    } else {\n      this.show(index + 1, true);\n    }\n  }\n\n  /**\n   * Removes the step from the tour\n   * @param {String} name The id for the step to remove\n   */\n  removeStep(name) {\n    const current = this.getCurrentStep();\n\n    // Find the step, destroy it and remove it from this.steps\n    this.steps.some((step, i) => {\n      if (step.id === name) {\n        if (step.isOpen()) {\n          step.hide();\n        }\n        step.destroy();\n        this.steps.splice(i, 1);\n        return true;\n      }\n    });\n    if (current && current.id === name) {\n      this.currentStep = undefined;\n\n      // If we have steps left, show the first one, otherwise just cancel the tour\n      this.steps.length ? this.show(0) : this.cancel();\n    }\n  }\n\n  /**\n   * Show a specific step in the tour\n   * @param {Number|String} key The key to look up the step by\n   * @param {Boolean} forward True if we are going forward, false if backward\n   */\n  show(key, forward) {\n    if (key === void 0) {\n      key = 0;\n    }\n    if (forward === void 0) {\n      forward = true;\n    }\n    const step = isString(key) ? this.getById(key) : this.steps[key];\n    if (step) {\n      this._updateStateBeforeShow();\n      const shouldSkipStep = isFunction(step.options.showOn) && !step.options.showOn();\n\n      // If `showOn` returns false, we want to skip the step, otherwise, show the step like normal\n      if (shouldSkipStep) {\n        this._skipStep(step, forward);\n      } else {\n        this.trigger('show', {\n          step,\n          previous: this.currentStep\n        });\n        this.currentStep = step;\n        step.show();\n      }\n    }\n  }\n\n  /**\n   * Start the tour\n   */\n  start() {\n    this.trigger('start');\n\n    // Save the focused element before the tour opens\n    this.focusedElBeforeOpen = document.activeElement;\n    this.currentStep = null;\n    this._setupModal();\n    this._setupActiveTour();\n    this.next();\n  }\n\n  /**\n   * Called whenever the tour is cancelled or completed, basically anytime we exit the tour\n   * @param {String} event The event name to trigger\n   * @private\n   */\n  _done(event) {\n    const index = this.steps.indexOf(this.currentStep);\n    if (Array.isArray(this.steps)) {\n      this.steps.forEach(step => step.destroy());\n    }\n    cleanupSteps(this);\n    this.trigger(event, {\n      index\n    });\n    Shepherd.activeTour = null;\n    this.trigger('inactive', {\n      tour: this\n    });\n    if (this.modal) {\n      this.modal.hide();\n    }\n    if (event === 'cancel' || event === 'complete') {\n      if (this.modal) {\n        const modalContainer = document.querySelector('.shepherd-modal-overlay-container');\n        if (modalContainer) {\n          modalContainer.remove();\n        }\n      }\n    }\n\n    // Focus the element that was focused before the tour started\n    if (isHTMLElement$1(this.focusedElBeforeOpen)) {\n      this.focusedElBeforeOpen.focus();\n    }\n  }\n\n  /**\n   * Make this tour \"active\"\n   * @private\n   */\n  _setupActiveTour() {\n    this.trigger('active', {\n      tour: this\n    });\n    Shepherd.activeTour = this;\n  }\n\n  /**\n   * _setupModal create the modal container and instance\n   * @private\n   */\n  _setupModal() {\n    this.modal = new Shepherd_modal({\n      target: this.options.modalContainer || document.body,\n      props: {\n        classPrefix: this.classPrefix,\n        styles: this.styles\n      }\n    });\n  }\n\n  /**\n   * Called when `showOn` evaluates to false, to skip the step or complete the tour if it's the last step\n   * @param {Step} step The step to skip\n   * @param {Boolean} forward True if we are going forward, false if backward\n   * @private\n   */\n  _skipStep(step, forward) {\n    const index = this.steps.indexOf(step);\n    if (index === this.steps.length - 1) {\n      this.complete();\n    } else {\n      const nextIndex = forward ? index + 1 : index - 1;\n      this.show(nextIndex, forward);\n    }\n  }\n\n  /**\n   * Before showing, hide the current step and if the tour is not\n   * already active, call `this._setupActiveTour`.\n   * @private\n   */\n  _updateStateBeforeShow() {\n    if (this.currentStep) {\n      this.currentStep.hide();\n    }\n    if (!this.isActive()) {\n      this._setupActiveTour();\n    }\n  }\n\n  /**\n   * Sets this.id to `${tourName}--${uuid}`\n   * @private\n   */\n  _setTourID() {\n    const tourName = this.options.tourName || 'tour';\n    this.id = `${tourName}--${uuid()}`;\n  }\n}\n\nconst isServerSide = typeof window === 'undefined';\nclass NoOp {\n  constructor() {}\n}\nif (isServerSide) {\n  Object.assign(Shepherd, {\n    Tour: NoOp,\n    Step: NoOp\n  });\n} else {\n  Object.assign(Shepherd, {\n    Tour,\n    Step\n  });\n}\n\nexport { Shepherd as default };\n//# sourceMappingURL=shepherd.esm.js.map\n", "import React, { FC, useMemo } from 'react';\nimport <PERSON> from 'shepherd.js';\nimport Step from 'shepherd.js/src/types/step';\nimport Tour from 'shepherd.js/src/types/tour';\n\ntype StepType = 'back' | 'cancel' | 'next';\nexport interface ShepherdButtonWithType extends Step.StepOptionsButton {\n  type?: StepType;\n}\n\nexport interface ShepherdOptionsWithType extends Step.StepOptions {\n  buttons?: ReadonlyArray<Step.StepOptionsButton | ShepherdButtonWithType>;\n}\n\ninterface ShepherdProps {\n  steps: Array<ShepherdOptionsWithType>;\n  tourOptions: Tour.TourOptions;\n  children: React.ReactNode;\n}\n\nconst ShepherdTourContext = React.createContext<Tour | null>(null);\nconst ShepherdTourContextConsumer = ShepherdTourContext.Consumer;\n\n/**\n * Take a set of steps and formats to use actions on the buttons in the current context\n * @param {Array} steps\n * @param {Array} tour\n * @private\n */\nconst addSteps = (steps: Array<Step.StepOptions>, tour: Tour) => {\n  // Return nothing if there are no steps\n  if (!steps.length) {\n    return [];\n  }\n\n  const parsedStepsforAction = steps.map((step: Step.StepOptions): Step.StepOptions => {\n    const { buttons } = step;\n\n    if (buttons) {\n      step.buttons = buttons.map((button: ShepherdButtonWithType) => {\n        const {\n          action, classes, disabled, label, secondary, text, type,\n        } = button;\n        return {\n          action: type ? tour[type] : action,\n          classes,\n          disabled,\n          label,\n          secondary,\n          text,\n          type,\n        };\n      });\n    }\n\n    return step;\n  });\n\n  return parsedStepsforAction.forEach((step: any) => tour.addStep(step));\n};\n\n// for instances where Context can't be used or doesn't make sense\nexport const useShepherdTour = ({ tourOptions, steps }: Pick<ShepherdProps, 'steps' | 'tourOptions'>) => {\n  const tourObject = useMemo(() => {\n    const tourInstance = new Shepherd.Tour(tourOptions);\n\n    addSteps(steps, tourInstance);\n\n    return tourInstance;\n  }, [tourOptions, steps]);\n\n  return tourObject;\n};\n\nexport const ShepherdTour: FC<ShepherdProps> = ({ children, tourOptions, steps }) => {\n  const tourObject = useMemo(() => {\n    const tourInstance = new Shepherd.Tour(tourOptions);\n\n    addSteps(steps, tourInstance);\n\n    return tourInstance;\n  }, [tourOptions, steps]);\n\n  return (\n    <ShepherdTourContext.Provider value={tourObject}>\n      {children}\n    </ShepherdTourContext.Provider>\n  );\n};\n\nexport type { default as Step } from 'shepherd.js/src/types/step';\nexport type { default as Tour } from 'shepherd.js/src/types/tour';\nexport { ShepherdTourContextConsumer as TourMethods, ShepherdTourContext };\n"], "mappings": ";;;;;;;;;AAEA,IAAIA,oBAAoB,SAASA,mBAAkBC,OAAO;AACzD,SAAOC,gBAAgBD,KAAK,KACxB,CAACE,UAAUF,KAAK;AACrB;AAEA,SAASC,gBAAgBD,OAAO;AAC/B,SAAO,CAAC,CAACA,SAAS,OAAOA,UAAU;AACpC;AAEA,SAASE,UAAUF,OAAO;AACzB,MAAIG,cAAcC,OAAOC,UAAUC,SAASC,KAAKP,KAAK;AAEtD,SAAOG,gBAAgB,qBACnBA,gBAAgB,mBAChBK,eAAeR,KAAK;AACzB;AAGA,IAAIS,eAAe,OAAOC,WAAW,cAAcA,OAAOC;AAC1D,IAAIC,qBAAqBH,eAAeC,OAAOC,IAAI,eAAe,IAAI;AAEtE,SAASH,eAAeR,OAAO;AAC9B,SAAOA,MAAMa,aAAaD;AAC3B;AAEA,SAASE,YAAYC,KAAK;AACzB,SAAOC,MAAMC,QAAQF,GAAG,IAAI,CAAA,IAAK,CAAA;AAClC;AAEA,SAASG,8BAA8BlB,OAAOmB,SAAS;AACtD,SAAQA,QAAQC,UAAU,SAASD,QAAQpB,kBAAkBC,KAAK,IAC/DqB,UAAUP,YAAYd,KAAK,GAAGA,OAAOmB,OAAO,IAC5CnB;AACJ;AAEA,SAASsB,kBAAkBC,QAAQC,QAAQL,SAAS;AACnD,SAAOI,OAAOE,OAAOD,MAAM,EAAEE,IAAI,SAASC,UAAS;AAClD,WAAOT,8BAA8BS,UAASR,OAAO;EACvD,CAAE;AACF;AAEA,SAASS,iBAAiBC,KAAKV,SAAS;AACvC,MAAI,CAACA,QAAQW,aAAa;AACzB,WAAOT;EACP;AACD,MAAIS,cAAcX,QAAQW,YAAYD,GAAG;AACzC,SAAO,OAAOC,gBAAgB,aAAaA,cAAcT;AAC1D;AAEA,SAASU,gCAAgCR,QAAQ;AAChD,SAAOnB,OAAO4B,wBACX5B,OAAO4B,sBAAsBT,MAAM,EAAEU,OAAO,SAASC,QAAQ;AAC9D,WAAOX,OAAOY,qBAAqBD,MAAM;EACzC,CAAA,IACC,CAAA;AACJ;AAEA,SAASE,QAAQb,QAAQ;AACxB,SAAOnB,OAAOiC,KAAKd,MAAM,EAAEE,OAAOM,gCAAgCR,MAAM,CAAC;AAC1E;AAEA,SAASe,mBAAmBC,QAAQC,UAAU;AAC7C,MAAI;AACH,WAAOA,YAAYD;WACZE,GAAG;AACV,WAAO;EACP;AACF;AAGA,SAASC,iBAAiBnB,QAAQM,KAAK;AACtC,SAAOS,mBAAmBf,QAAQM,GAAG,KACjC,EAAEzB,OAAOuC,eAAepC,KAAKgB,QAAQM,GAAG,KACvCzB,OAAO+B,qBAAqB5B,KAAKgB,QAAQM,GAAG;AAClD;AAEA,SAASe,YAAYrB,QAAQC,QAAQL,SAAS;AAC7C,MAAI0B,cAAc,CAAA;AAClB,MAAI1B,QAAQpB,kBAAkBwB,MAAM,GAAG;AACtCa,YAAQb,MAAM,EAAEuB,QAAQ,SAASjB,KAAK;AACrCgB,kBAAYhB,GAAG,IAAIX,8BAA8BK,OAAOM,GAAG,GAAGV,OAAO;IACxE,CAAG;EACD;AACDiB,UAAQZ,MAAM,EAAEsB,QAAQ,SAASjB,KAAK;AACrC,QAAIa,iBAAiBnB,QAAQM,GAAG,GAAG;AAClC;IACA;AAED,QAAIS,mBAAmBf,QAAQM,GAAG,KAAKV,QAAQpB,kBAAkByB,OAAOK,GAAG,CAAC,GAAG;AAC9EgB,kBAAYhB,GAAG,IAAID,iBAAiBC,KAAKV,OAAO,EAAEI,OAAOM,GAAG,GAAGL,OAAOK,GAAG,GAAGV,OAAO;IACtF,OAAS;AACN0B,kBAAYhB,GAAG,IAAIX,8BAA8BM,OAAOK,GAAG,GAAGV,OAAO;IACrE;EACH,CAAE;AACD,SAAO0B;AACR;AAEA,SAASxB,UAAUE,QAAQC,QAAQL,SAAS;AAC3CA,YAAUA,WAAW,CAAA;AACrBA,UAAQ4B,aAAa5B,QAAQ4B,cAAczB;AAC3CH,UAAQpB,oBAAoBoB,QAAQpB,qBAAqBA;AAGzDoB,UAAQD,gCAAgCA;AAExC,MAAI8B,gBAAgBhC,MAAMC,QAAQO,MAAM;AACxC,MAAIyB,gBAAgBjC,MAAMC,QAAQM,MAAM;AACxC,MAAI2B,4BAA4BF,kBAAkBC;AAElD,MAAI,CAACC,2BAA2B;AAC/B,WAAOhC,8BAA8BM,QAAQL,OAAO;aAC1C6B,eAAe;AACzB,WAAO7B,QAAQ4B,WAAWxB,QAAQC,QAAQL,OAAO;EACnD,OAAQ;AACN,WAAOyB,YAAYrB,QAAQC,QAAQL,OAAO;EAC1C;AACF;AAEAE,UAAU8B,MAAM,SAASC,aAAaC,OAAOlC,SAAS;AACrD,MAAI,CAACH,MAAMC,QAAQoC,KAAK,GAAG;AAC1B,UAAM,IAAIC,MAAM,mCAAmC;EACnD;AAED,SAAOD,MAAME,OAAO,SAASC,MAAMC,MAAM;AACxC,WAAOpC,UAAUmC,MAAMC,MAAMtC,OAAO;EACpC,GAAE,CAAA,CAAG;AACP;AAEA,IAAIuC,cAAcrC;AAElB,IAAA,MAAiBqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChHX,IAAA,sBAAsB,aAAAC,QAAM,cAA2B,IAAI;AACjE,IAAM,8BAA8B,oBAAoB;AAQxD,IAAM,WAAW,SAAC,OAAgC,MAAU;AAE1D,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO,CAAA;EACR;AAED,MAAM,uBAAuB,MAAM,IAAI,SAAC,MAAsB;AACpD,QAAA,UAAY,KAAI;AAExB,QAAI,SAAS;AACX,WAAK,UAAU,QAAQ,IAAI,SAAC,QAA8B;AAEtD,YAAA,SACE,OAAM,QADA,UACN,OAAM,SADS,WACf,OAAM,UADmB,QACzB,OAAM,OAD0B,YAChC,OAAM,WADqCC,QAC3C,OAAM,MAD2C,OACjD,OAAM;AACV,eAAO;UACL,QAAQ,OAAO,KAAK,IAAI,IAAI;UAC5B;UACA;UACA;UACA;UACA,MAAIA;UACJ;;MAEJ,CAAC;IACF;AAED,WAAO;EACT,CAAC;AAED,SAAO,qBAAqB,QAAQ,SAAC,MAAc;AAAA,WAAA,KAAK,QAAQ,IAAI;EAAjB,CAAkB;AACvE;AAGO,IAAM,kBAAkB,SAAC,IAAoE;MAAlE,cAAW,GAAA,aAAE,QAAK,GAAA;AAClD,MAAM,iBAAa,sBAAQ,WAAA;AACzB,QAAM,eAAe,IAAI,SAAS,KAAK,WAAW;AAElD,aAAS,OAAO,YAAY;AAE5B,WAAO;EACT,GAAG,CAAC,aAAa,KAAK,CAAC;AAEvB,SAAO;AACT;AAEO,IAAM,eAAkC,SAAC,IAAgC;AAA9B,MAAAC,YAAQ,GAAA,UAAE,cAAW,GAAA,aAAE,QAAK,GAAA;AAC5E,MAAM,iBAAa,sBAAQ,WAAA;AACzB,QAAM,eAAe,IAAI,SAAS,KAAK,WAAW;AAElD,aAAS,OAAO,YAAY;AAE5B,WAAO;EACT,GAAG,CAAC,aAAa,KAAK,CAAC;AAEvB,SACE,aAAAF,QAAA,cAAC,oBAAoB,UAAQ,EAAC,OAAO,WAAU,GAC5CE,SAAQ;AAGf;", "names": ["isMergeableObject", "value", "isNonNullObject", "isSpecial", "stringValue", "Object", "prototype", "toString", "call", "isReactElement", "canUseSymbol", "Symbol", "for", "REACT_ELEMENT_TYPE", "$$typeof", "emptyTarget", "val", "Array", "isArray", "cloneUnlessOtherwiseSpecified", "options", "clone", "deepmerge", "defaultArrayMerge", "target", "source", "concat", "map", "element", "getMergeFunction", "key", "customMerge", "getEnumerableOwnPropertySymbols", "getOwnPropertySymbols", "filter", "symbol", "propertyIsEnumerable", "get<PERSON><PERSON><PERSON>", "keys", "propertyIsOnObject", "object", "property", "_", "propertyIsUnsafe", "hasOwnProperty", "mergeObject", "destination", "for<PERSON>ach", "arrayMerge", "sourceIsArray", "targetIsArray", "sourceAndTargetTypesMatch", "all", "deepmergeAll", "array", "Error", "reduce", "prev", "next", "deepmerge_1", "React", "text", "children"]}