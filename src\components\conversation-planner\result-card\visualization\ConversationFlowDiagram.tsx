
import React, { useState } from 'react';
import { AnalysisResult } from '@/types/conversation';
import { <PERSON><PERSON>, HelpCircle, MessageSquare, Workflow, Eye, EyeOff } from 'lucide-react';
import { ConversationSectionsParser } from '@/services/openRouter/parsers/conversationSectionsParser';
import { AnalysisFlowDiagram } from '../../visualization/AnalysisFlowDiagram';
import { Checkbox } from '@/components/ui/checkbox';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface NodeProps {
  content: string;
  icon: React.ReactNode;
  type: 'question' | 'answer' | 'follow-up';
  onClick?: () => void;
  onToggleSelect?: () => void;
  isSelected?: boolean;
}

const Node: React.FC<NodeProps> = ({ content, icon, type, onClick, onToggleSelect, isSelected }) => {
  const typeClasses = {
    question: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-700/40 dark:text-blue-200',
    answer: 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-700/40 dark:text-green-200',
    'follow-up': 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-700/40 dark:text-yellow-200',
  };

  const clickableClasses = onClick ? 'cursor-pointer' : '';
  const selectedClasses = isSelected ? 'ring-2 ring-primary shadow-lg' : '';

  return (
    <div
      className={`relative flex items-start gap-3 p-3 pr-12 rounded-lg border shadow-sm w-full transition-all ${typeClasses[type]} ${selectedClasses}`}
    >
      {onToggleSelect && (
        <div className="absolute top-1/2 -translate-y-1/2 right-3">
          <Checkbox 
              checked={isSelected} 
              onCheckedChange={onToggleSelect} 
              aria-label={`Select item: ${content}`}
           />
        </div>
      )}
      <div 
        className={`flex-grow flex items-start gap-3 ${clickableClasses}`}
        onClick={onClick ? (e) => { e.stopPropagation(); onClick(); } : undefined}
      >
        <div className="flex-shrink-0 mt-1">{icon}</div>
        <p className="text-sm flex-grow">{content}</p>
      </div>
    </div>
  );
};

const BranchLine: React.FC = () => (
  <div className="w-8 h-full flex-shrink-0 relative">
    <div className="absolute top-0 left-1/2 w-px h-full bg-gray-300 dark:bg-gray-700"></div>
    <div className="absolute top-1/2 left-1/2 w-1/2 h-px bg-gray-300 dark:bg-gray-700"></div>
  </div>
);


export const ConversationFlowDiagram: React.FC<{
  result: AnalysisResult;
  onItemSelect: (item: string) => void;
  selectedItems: string[];
  onItemToggleSelect: (item: string) => void;
}> = ({ result, onItemSelect, selectedItems, onItemToggleSelect }) => {
  const [viewMode, setViewMode] = useState<'traditional' | 'enhanced'>('traditional');

  // Enhanced flow diagram is available for all analysis types
  const hasEnhancedView = true;

  // Traditional view is only for multiple choice
  const hasTraditionalView = result.analysisType === 'multiple' &&
    result.parsedOutput &&
    result.parsedOutput.type === 'multiple';

  if (!hasTraditionalView && !hasEnhancedView) {
    return null;
  }

  const renderTraditionalView = () => {
    if (!hasTraditionalView) return null;

    const { question, analysis } = result;
    const { answers } = result.parsedOutput!;
    const { followUpQuestions } = ConversationSectionsParser.parse(analysis);

    return (
      <div className="p-4 rounded-lg bg-gray-50 border dark:bg-gray-900/50 dark:border-border">
        <div className="flex flex-col items-start">
          {/* Question Node */}
          <Node content={question} icon={<HelpCircle className="text-blue-500" />} type="question" />

          <div className="flex w-full">
            <div className="w-8 flex-shrink-0 relative">
              <div className="absolute top-0 left-1/2 w-px h-full bg-gray-300 dark:bg-gray-700"></div>
            </div>
            <div className="flex-grow space-y-4 pt-4">
              {/* Answers Section */}
              {answers.length > 0 && (
                <div>
                  <h3 className="text-sm font-semibold text-muted-foreground mb-2">Potential Responses</h3>
                  <div className="space-y-4">
                    {answers.map((answer) => (
                        <div key={answer.id} className="flex items-start">
                            <BranchLine />
                            <div className="flex-grow">
                                <Node
                                  content={answer.content}
                                  icon={<Bot className="text-green-500" />}
                                  type="answer"
                                  onClick={() => onItemSelect(answer.content)}
                                  isSelected={selectedItems.includes(answer.content)}
                                  onToggleSelect={() => onItemToggleSelect(answer.content)}
                                />
                            </div>
                        </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Follow-up Questions Section */}
              {followUpQuestions.length > 0 && (
                <div className="pt-4">
                  <h3 className="text-sm font-semibold text-muted-foreground mb-2">Possible Follow-up Questions</h3>
                  <div className="space-y-4">
                      {followUpQuestions.map((fq, index) => (
                          <div key={index} className="flex items-start">
                              <BranchLine />
                              <div className="flex-grow">
                                  <Node
                                    content={fq}
                                    icon={<MessageSquare className="text-yellow-500" />}
                                    type="follow-up"
                                    onClick={() => onItemSelect(fq)}
                                    isSelected={selectedItems.includes(fq)}
                                    onToggleSelect={() => onItemToggleSelect(fq)}
                                  />
                              </div>
                          </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderEnhancedView = () => (
    <AnalysisFlowDiagram
      result={result}
      onNodeClick={(nodeId, data) => {
        console.log('Flow node clicked:', nodeId, data);
      }}
      onStepSelect={(step) => {
        console.log('Step selected:', step);
      }}
      animated={true}
      interactive={true}
      showProgress={true}
    />
  );

  return (
    <div className="mt-6 animate-fade-in">
      {/* View Toggle */}
      {hasTraditionalView && hasEnhancedView && (
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Workflow className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">Flow Visualization</span>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'traditional' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('traditional')}
              className="text-xs"
            >
              <Eye className="w-3 h-3 mr-1" />
              Traditional
            </Button>
            <Button
              variant={viewMode === 'enhanced' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('enhanced')}
              className="text-xs"
            >
              <Workflow className="w-3 h-3 mr-1" />
              Enhanced
            </Button>
          </div>
        </div>
      )}

      {/* Content */}
      {viewMode === 'traditional' && hasTraditionalView && renderTraditionalView()}
      {viewMode === 'enhanced' && hasEnhancedView && renderEnhancedView()}
      {!hasTraditionalView && hasEnhancedView && renderEnhancedView()}
    </div>
  );
};
