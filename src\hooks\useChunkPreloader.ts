import { useEffect } from 'react';

/**
 * Hook to preload critical chunks when user actions indicate they might be needed
 */
export const useChunkPreloader = () => {
  
  const preloadCanvas = () => {
    // Preload canvas visualization when user might access it
    import('@/components/conversation-planner/insights-canvas/VisualHistoricalAnalysisCanvas');
  };

  const preloadChatbot = () => {
    // Preload chatbot features when user might use them
    import('@/components/conversation-planner/ChatbotMode');
  };

  const preloadLibrary = () => {
    // Preload library when user might access historical analysis
    import('@/components/Library');
  };

  const preloadCharacterPersonas = () => {
    // Preload character persona features when user selects character analysis
    import('@/components/conversation-planner/CharacterPersonaModal');
  };

  const preloadAISettings = () => {
    // Preload AI settings modal when user might configure AI
    import('@/components/conversation-planner/historical-analysis/AISettingsModal');
  };

  return {
    preload<PERSON><PERSON><PERSON>,
    preloadChatbot,
    preloadLibrary,
    preload<PERSON>haracter<PERSON>ersonas,
    preloadAISettings,
  };
};

/**
 * Hook to automatically preload likely-needed chunks based on user behavior
 */
export const useSmartPreloader = () => {
  const preloader = useChunkPreloader();

  useEffect(() => {
    // Preload critical chunks after initial render
    const timer = setTimeout(() => {
      // If user has an API key, they're likely to use advanced features
      const hasApiKey = localStorage.getItem('openRouterApiKey');
      if (hasApiKey) {
        preloader.preloadChatbot();
        preloader.preloadLibrary();
      }
    }, 2000); // Wait 2 seconds after initial load

    return () => clearTimeout(timer);
  }, [preloader]);

  return preloader;
};
