{"version": 3, "sources": ["../../node_modules/@radix-ui/react-dropdown-menu/src/DropdownMenu.tsx", "../../node_modules/@radix-ui/react-menu/src/Menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ElementRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ElementRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ElementRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ElementRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ElementRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ElementRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ElementRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ElementRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ElementRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ElementRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ElementRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ElementRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Slot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ElementRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus({ preventScroll: true });\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n                preventScrollOnEntryFocus\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus({ preventScroll: true });\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ElementRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ElementRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ElementRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x;\n    const yi = polygon[i].y;\n    const xj = polygon[j].x;\n    const yj = polygon[j].y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACAvB,YAAuB;AAwHf,yBAAA;AA9FR,IAAM,iBAAiB,CAAC,SAAS,GAAG;AACpC,IAAM,aAAa,CAAC,aAAa,UAAU,MAAM;AACjD,IAAM,YAAY,CAAC,WAAW,YAAY,KAAK;AAC/C,IAAM,kBAAkB,CAAC,GAAG,YAAY,GAAG,SAAS;AACpD,IAAM,gBAA6C;EACjD,KAAK,CAAC,GAAG,gBAAgB,YAAY;EACrC,KAAK,CAAC,GAAG,gBAAgB,WAAW;AACtC;AACA,IAAM,iBAA8C;EAClD,KAAK,CAAC,WAAW;EACjB,KAAK,CAAC,YAAY;AACpB;AAMA,IAAM,YAAY;AAGlB,IAAM,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAGzD,SAAS;AAGX,IAAM,CAAC,mBAAmB,eAAe,IAAI,mBAAmB,WAAW;EACzE;EACA;EACA;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AACzC,IAAM,2BAA2B,4BAA4B;AAS7D,IAAM,CAAC,cAAc,cAAc,IAAI,kBAAoC,SAAS;AASpF,IAAM,CAAC,kBAAkB,kBAAkB,IAAI,kBAAwC,SAAS;AAUhG,IAAM,OAA4B,CAAC,UAAkC;AACnE,QAAM,EAAE,aAAa,OAAO,OAAO,UAAU,KAAK,cAAc,QAAQ,KAAK,IAAI;AACjF,QAAM,cAAc,eAAe,WAAW;AAC9C,QAAM,CAAC,SAAS,UAAU,IAAU,eAAoC,IAAI;AAC5E,QAAM,qBAA2B,aAAO,KAAK;AAC7C,QAAM,mBAAmB,eAAe,YAAY;AACpD,QAAM,YAAY,aAAa,GAAG;AAE5B,EAAA,gBAAU,MAAM;AAGpB,UAAM,gBAAgB,MAAM;AAC1B,yBAAmB,UAAU;AAC7B,eAAS,iBAAiB,eAAe,eAAe,EAAE,SAAS,MAAM,MAAM,KAAK,CAAC;AACrF,eAAS,iBAAiB,eAAe,eAAe,EAAE,SAAS,MAAM,MAAM,KAAK,CAAC;IACvF;AACA,UAAM,gBAAgB,MAAO,mBAAmB,UAAU;AAC1D,aAAS,iBAAiB,WAAW,eAAe,EAAE,SAAS,KAAK,CAAC;AACrE,WAAO,MAAM;AACX,eAAS,oBAAoB,WAAW,eAAe,EAAE,SAAS,KAAK,CAAC;AACxE,eAAS,oBAAoB,eAAe,eAAe,EAAE,SAAS,KAAK,CAAC;AAC5E,eAAS,oBAAoB,eAAe,eAAe,EAAE,SAAS,KAAK,CAAC;IAC9E;EACF,GAAG,CAAC,CAAC;AAEL,aACE,wBAAiB,OAAhB,EAAsB,GAAG,aACxB,cAAA;IAAC;IAAA;MACC,OAAO;MACP;MACA,cAAc;MACd;MACA,iBAAiB;MAEjB,cAAA;QAAC;QAAA;UACC,OAAO;UACP,SAAe,kBAAY,MAAM,iBAAiB,KAAK,GAAG,CAAC,gBAAgB,CAAC;UAC5E;UACA,KAAK;UACL;UAEC;QAAA;MACH;IAAA;EACF,EAAA,CACF;AAEJ;AAEA,KAAK,cAAc;AAMnB,IAAM,cAAc;AAMpB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,aAAa,GAAG,YAAY,IAAI;AACxC,UAAM,cAAc,eAAe,WAAW;AAC9C,eAAO,wBAAiB,QAAhB,EAAwB,GAAG,aAAc,GAAG,aAAa,KAAK,aAAA,CAAc;EACtF;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,kBAAsC,aAAa;EAC5F,YAAY;AACd,CAAC;AAgBD,IAAM,aAAwC,CAAC,UAAwC;AACrF,QAAM,EAAE,aAAa,YAAY,UAAU,UAAU,IAAI;AACzD,QAAM,UAAU,eAAe,aAAa,WAAW;AACvD,aACE,wBAAC,gBAAA,EAAe,OAAO,aAAa,YAClC,cAAA,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACvC,cAAA,wBAAC,QAAA,EAAgB,SAAO,MAAC,WACtB,SAAA,CACH,EAAA,CACF,EAAA,CACF;AAEJ;AAEA,WAAW,cAAc;AAMzB,IAAM,eAAe;AAUrB,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,kBAA2C,YAAY;AAgBzD,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;AACtE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,eAAe,cAAc,MAAM,WAAW;AAC9D,UAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;AAEtE,eACE,wBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,aAChC,cAAA,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACvC,cAAA,wBAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,aAC3B,UAAA,YAAY,YACX,wBAAC,sBAAA,EAAsB,GAAG,cAAc,KAAK,aAAA,CAAc,QAE3D,wBAAC,yBAAA,EAAyB,GAAG,cAAc,KAAK,aAAA,CAAc,EAAA,CAElE,EAAA,CACF,EAAA,CACF;EAEJ;AACF;AAQA,IAAM,uBAA6B;EACjC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,UAAU,eAAe,cAAc,MAAM,WAAW;AAC9D,UAAM,MAAY,aAAmC,IAAI;AACzD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AAGhD,IAAA,gBAAU,MAAM;AACpB,YAAM,UAAU,IAAI;AACpB,UAAI,QAAS,QAAO,WAAW,OAAO;IACxC,GAAG,CAAC,CAAC;AAEL,eACE;MAAC;MAAA;QACE,GAAG;QACJ,KAAK;QAGL,WAAW,QAAQ;QAGnB,6BAA6B,QAAQ;QACrC,sBAAoB;QAGpB,gBAAgB;UACd,MAAM;UACN,CAAC,UAAU,MAAM,eAAe;UAChC,EAAE,0BAA0B,MAAM;QACpC;QACA,WAAW,MAAM,QAAQ,aAAa,KAAK;MAAA;IAC7C;EAEJ;AACF;AAEA,IAAM,0BAAgC,iBAGpC,CAAC,OAA8C,iBAAiB;AAChE,QAAM,UAAU,eAAe,cAAc,MAAM,WAAW;AAC9D,aACE;IAAC;IAAA;MACE,GAAG;MACJ,KAAK;MACL,WAAW;MACX,6BAA6B;MAC7B,sBAAsB;MACtB,WAAW,MAAM,QAAQ,aAAa,KAAK;IAAA;EAC7C;AAEJ,CAAC;AAgDD,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM;MACJ;MACA,OAAO;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,eAAe,cAAc,WAAW;AACxD,UAAM,cAAc,mBAAmB,cAAc,WAAW;AAChE,UAAM,cAAc,eAAe,WAAW;AAC9C,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,UAAM,WAAW,cAAc,WAAW;AAC1C,UAAM,CAAC,eAAe,gBAAgB,IAAU,eAAwB,IAAI;AAC5E,UAAM,aAAmB,aAAuB,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,YAAY,QAAQ,eAAe;AACtF,UAAM,WAAiB,aAAO,CAAC;AAC/B,UAAM,YAAkB,aAAO,EAAE;AACjC,UAAM,uBAA6B,aAAO,CAAC;AAC3C,UAAM,wBAA8B,aAA2B,IAAI;AACnE,UAAM,gBAAsB,aAAa,OAAO;AAChD,UAAM,kBAAwB,aAAO,CAAC;AAEtC,UAAM,oBAAoB,uBAAuB,sBAAqB;AACtE,UAAM,yBAAyB,uBAC3B,EAAE,IAAI,MAAM,gBAAgB,KAAK,IACjC;AAEJ,UAAM,wBAAwB,CAAC,QAAgB;;AAC7C,YAAM,SAAS,UAAU,UAAU;AACnC,YAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,YAAM,cAAc,SAAS;AAC7B,YAAM,gBAAe,WAAM,KAAK,CAAC,SAAS,KAAK,IAAI,YAAY,WAAW,MAArD,mBAAwD;AAC7E,YAAM,SAAS,MAAM,IAAI,CAAC,SAAS,KAAK,SAAS;AACjD,YAAM,YAAY,aAAa,QAAQ,QAAQ,YAAY;AAC3D,YAAM,WAAU,WAAM,KAAK,CAAC,SAAS,KAAK,cAAc,SAAS,MAAjD,mBAAoD,IAAI;AAGxE,OAAC,SAAS,aAAa,OAAe;AACpC,kBAAU,UAAU;AACpB,eAAO,aAAa,SAAS,OAAO;AACpC,YAAI,UAAU,GAAI,UAAS,UAAU,OAAO,WAAW,MAAM,aAAa,EAAE,GAAG,GAAI;MACrF,GAAG,MAAM;AAET,UAAI,SAAS;AAKX,mBAAW,MAAO,QAAwB,MAAM,CAAC;MACnD;IACF;AAEM,IAAA,gBAAU,MAAM;AACpB,aAAO,MAAM,OAAO,aAAa,SAAS,OAAO;IACnD,GAAG,CAAC,CAAC;AAIL,mBAAe;AAEf,UAAM,2BAAiC,kBAAY,CAAC,UAA8B;;AAChF,YAAM,kBAAkB,cAAc,cAAY,2BAAsB,YAAtB,mBAA+B;AACjF,aAAO,mBAAmB,qBAAqB,QAAO,2BAAsB,YAAtB,mBAA+B,IAAI;IAC3F,GAAG,CAAC,CAAC;AAEL,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,aAAmB;UACjB,CAAC,UAAU;AACT,gBAAI,yBAAyB,KAAK,EAAG,OAAM,eAAe;UAC5D;UACA,CAAC,wBAAwB;QAC3B;QACA,aAAmB;UACjB,CAAC,UAAU;;AACT,gBAAI,yBAAyB,KAAK,EAAG;AACrC,6BAAW,YAAX,mBAAoB;AACpB,6BAAiB,IAAI;UACvB;UACA,CAAC,wBAAwB;QAC3B;QACA,gBAAsB;UACpB,CAAC,UAAU;AACT,gBAAI,yBAAyB,KAAK,EAAG,OAAM,eAAe;UAC5D;UACA,CAAC,wBAAwB;QAC3B;QACA;QACA,4BAAkC,kBAAY,CAAC,WAAW;AACxD,gCAAsB,UAAU;QAClC,GAAG,CAAC,CAAC;QAEL,cAAA,wBAAC,mBAAA,EAAmB,GAAG,wBACrB,cAAA;UAAC;UAAA;YACC,SAAO;YACP,SAAS;YACT,kBAAkB,qBAAqB,iBAAiB,CAAC,UAAU;;AAGjE,oBAAM,eAAe;AACrB,+BAAW,YAAX,mBAAoB,MAAM,EAAE,eAAe,KAAK;YAClD,CAAC;YACD,oBAAoB;YAEpB,cAAA;cAAC;cAAA;gBACC,SAAO;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA,cAAA;kBAAkB;kBAAjB;oBACC,SAAO;oBACN,GAAG;oBACJ,KAAK,YAAY;oBACjB,aAAY;oBACZ;oBACA,kBAAkB;oBAClB,0BAA0B;oBAC1B,cAAc,qBAAqB,cAAc,CAAC,UAAU;AAE1D,0BAAI,CAAC,YAAY,mBAAmB,QAAS,OAAM,eAAe;oBACpE,CAAC;oBACD,2BAAyB;oBAEzB,cAAA;sBAAiB;sBAAhB;wBACC,MAAK;wBACL,oBAAiB;wBACjB,cAAY,aAAa,QAAQ,IAAI;wBACrC,2BAAwB;wBACxB,KAAK,YAAY;wBAChB,GAAG;wBACH,GAAG;wBACJ,KAAK;wBACL,OAAO,EAAE,SAAS,QAAQ,GAAG,aAAa,MAAM;wBAChD,WAAW,qBAAqB,aAAa,WAAW,CAAC,UAAU;AAEjE,gCAAM,SAAS,MAAM;AACrB,gCAAM,kBACJ,OAAO,QAAQ,2BAA2B,MAAM,MAAM;AACxD,gCAAM,gBAAgB,MAAM,WAAW,MAAM,UAAU,MAAM;AAC7D,gCAAM,iBAAiB,MAAM,IAAI,WAAW;AAC5C,8BAAI,iBAAiB;AAEnB,gCAAI,MAAM,QAAQ,MAAO,OAAM,eAAe;AAC9C,gCAAI,CAAC,iBAAiB,eAAgB,uBAAsB,MAAM,GAAG;0BACvE;AAEA,gCAAM,UAAU,WAAW;AAC3B,8BAAI,MAAM,WAAW,QAAS;AAC9B,8BAAI,CAAC,gBAAgB,SAAS,MAAM,GAAG,EAAG;AAC1C,gCAAM,eAAe;AACrB,gCAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,gCAAM,iBAAiB,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAC5D,8BAAI,UAAU,SAAS,MAAM,GAAG,EAAG,gBAAe,QAAQ;AAC1D,qCAAW,cAAc;wBAC3B,CAAC;wBACD,QAAQ,qBAAqB,MAAM,QAAQ,CAAC,UAAU;AAEpD,8BAAI,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM,GAAG;AAC/C,mCAAO,aAAa,SAAS,OAAO;AACpC,sCAAU,UAAU;0BACtB;wBACF,CAAC;wBACD,eAAe;0BACb,MAAM;0BACN,UAAU,CAAC,UAAU;AACnB,kCAAM,SAAS,MAAM;AACrB,kCAAM,qBAAqB,gBAAgB,YAAY,MAAM;AAI7D,gCAAI,MAAM,cAAc,SAAS,MAAM,KAAK,oBAAoB;AAC9D,oCAAM,SAAS,MAAM,UAAU,gBAAgB,UAAU,UAAU;AACnE,4CAAc,UAAU;AACxB,8CAAgB,UAAU,MAAM;4BAClC;0BACF,CAAC;wBACH;sBAAA;oBACF;kBAAA;gBACF;cAAA;YACF;UAAA;QACF,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAMnB,IAAM,YAAkB;EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,GAAG,WAAW,IAAI;AACvC,eAAO,wBAAC,UAAU,KAAV,EAAc,MAAK,SAAS,GAAG,YAAY,KAAK,aAAA,CAAc;EACxE;AACF;AAEA,UAAU,cAAc;AAMxB,IAAM,aAAa;AAKnB,IAAM,YAAkB;EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,GAAG,WAAW,IAAI;AACvC,eAAO,wBAAC,UAAU,KAAV,EAAe,GAAG,YAAY,KAAK,aAAA,CAAc;EAC3D;AACF;AAEA,UAAU,cAAc;AAMxB,IAAM,YAAY;AAClB,IAAM,cAAc;AAOpB,IAAM,WAAiB;EACrB,CAAC,OAAmC,iBAAiB;AACnD,UAAM,EAAE,WAAW,OAAO,UAAU,GAAG,UAAU,IAAI;AACrD,UAAM,MAAY,aAAuB,IAAI;AAC7C,UAAM,cAAc,mBAAmB,WAAW,MAAM,WAAW;AACnE,UAAM,iBAAiB,sBAAsB,WAAW,MAAM,WAAW;AACzE,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,mBAAyB,aAAO,KAAK;AAE3C,UAAM,eAAe,MAAM;AACzB,YAAM,WAAW,IAAI;AACrB,UAAI,CAAC,YAAY,UAAU;AACzB,cAAM,kBAAkB,IAAI,YAAY,aAAa,EAAE,SAAS,MAAM,YAAY,KAAK,CAAC;AACxF,iBAAS,iBAAiB,aAAa,CAAC,UAAU,qCAAW,QAAQ,EAAE,MAAM,KAAK,CAAC;AACnF,oCAA4B,UAAU,eAAe;AACrD,YAAI,gBAAgB,kBAAkB;AACpC,2BAAiB,UAAU;QAC7B,OAAO;AACL,sBAAY,QAAQ;QACtB;MACF;IACF;AAEA,eACE;MAAC;MAAA;QACE,GAAG;QACJ,KAAK;QACL;QACA,SAAS,qBAAqB,MAAM,SAAS,YAAY;QACzD,eAAe,CAAC,UAAU;;AACxB,sBAAM,kBAAN,+BAAsB;AACtB,2BAAiB,UAAU;QAC7B;QACA,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;;AAI9D,cAAI,CAAC,iBAAiB,QAAS,aAAM,kBAAN,mBAAqB;QACtD,CAAC;QACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,gBAAgB,eAAe,UAAU,YAAY;AAC3D,cAAI,YAAa,iBAAiB,MAAM,QAAQ,IAAM;AACtD,cAAI,eAAe,SAAS,MAAM,GAAG,GAAG;AACtC,kBAAM,cAAc,MAAM;AAO1B,kBAAM,eAAe;UACvB;QACF,CAAC;MAAA;IACH;EAEJ;AACF;AAEA,SAAS,cAAc;AAUvB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,aAAa,WAAW,OAAO,WAAW,GAAG,UAAU,IAAI;AACnE,UAAM,iBAAiB,sBAAsB,WAAW,WAAW;AACnE,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,UAAM,MAAY,aAAuB,IAAI;AAC7C,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,CAAC,WAAW,YAAY,IAAU,eAAS,KAAK;AAGtD,UAAM,CAAC,aAAa,cAAc,IAAU,eAAS,EAAE;AACjD,IAAA,gBAAU,MAAM;AACpB,YAAM,WAAW,IAAI;AACrB,UAAI,UAAU;AACZ,wBAAgB,SAAS,eAAe,IAAI,KAAK,CAAC;MACpD;IACF,GAAG,CAAC,UAAU,QAAQ,CAAC;AAEvB,eACE;MAAC,WAAW;MAAX;QACC,OAAO;QACP;QACA,WAAW,aAAa;QAExB,cAAA,wBAAkB,MAAjB,EAAsB,SAAO,MAAE,GAAG,uBAAuB,WAAW,CAAC,UACpE,cAAA;UAAC,UAAU;UAAV;YACC,MAAK;YACL,oBAAkB,YAAY,KAAK;YACnC,iBAAe,YAAY;YAC3B,iBAAe,WAAW,KAAK;YAC9B,GAAG;YACJ,KAAK;YAYL,eAAe;cACb,MAAM;cACN,UAAU,CAAC,UAAU;AACnB,oBAAI,UAAU;AACZ,iCAAe,YAAY,KAAK;gBAClC,OAAO;AACL,iCAAe,YAAY,KAAK;AAChC,sBAAI,CAAC,MAAM,kBAAkB;AAC3B,0BAAM,OAAO,MAAM;AACnB,yBAAK,MAAM,EAAE,eAAe,KAAK,CAAC;kBACpC;gBACF;cACF,CAAC;YACH;YACA,gBAAgB;cACd,MAAM;cACN,UAAU,CAAC,UAAU,eAAe,YAAY,KAAK,CAAC;YACxD;YACA,SAAS,qBAAqB,MAAM,SAAS,MAAM,aAAa,IAAI,CAAC;YACrE,QAAQ,qBAAqB,MAAM,QAAQ,MAAM,aAAa,KAAK,CAAC;UAAA;QACtE,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAMA,IAAM,qBAAqB;AAY3B,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,UAAU,OAAO,iBAAiB,GAAG,kBAAkB,IAAI;AACnE,eACE,wBAAC,uBAAA,EAAsB,OAAO,MAAM,aAAa,SAC/C,cAAA;MAAC;MAAA;QACC,MAAK;QACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;QAClD,GAAG;QACJ,KAAK;QACL,cAAY,gBAAgB,OAAO;QACnC,UAAU;UACR,kBAAkB;UAClB,MAAM,mDAAkB,gBAAgB,OAAO,IAAI,OAAO,CAAC;UAC3D,EAAE,0BAA0B,MAAM;QACpC;MAAA;IACF,EAAA,CACF;EAEJ;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,mBAAmB;AAEzB,IAAM,CAAC,oBAAoB,oBAAoB,IAAI;EACjD;EACA,EAAE,OAAO,QAAW,eAAe,MAAM;EAAC,EAAE;AAC9C;AAQA,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,OAAO,eAAe,GAAG,WAAW,IAAI;AAChD,UAAM,oBAAoB,eAAe,aAAa;AACtD,eACE,wBAAC,oBAAA,EAAmB,OAAO,MAAM,aAAa,OAAc,eAAe,mBACzE,cAAA,wBAAC,WAAA,EAAW,GAAG,YAAY,KAAK,aAAA,CAAc,EAAA,CAChD;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,kBAAkB;AAOxB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,OAAO,GAAG,eAAe,IAAI;AACrC,UAAM,UAAU,qBAAqB,iBAAiB,MAAM,WAAW;AACvE,UAAM,UAAU,UAAU,QAAQ;AAClC,eACE,wBAAC,uBAAA,EAAsB,OAAO,MAAM,aAAa,SAC/C,cAAA;MAAC;MAAA;QACC,MAAK;QACL,gBAAc;QACb,GAAG;QACJ,KAAK;QACL,cAAY,gBAAgB,OAAO;QACnC,UAAU;UACR,eAAe;UACf,MAAA;;AAAM,iCAAQ,kBAAR,iCAAwB;;UAC9B,EAAE,0BAA0B,MAAM;QACpC;MAAA;IACF,EAAA,CACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,sBAAsB;AAI5B,IAAM,CAAC,uBAAuB,uBAAuB,IAAI;EACvD;EACA,EAAE,SAAS,MAAM;AACnB;AAYA,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,aAAa,YAAY,GAAG,mBAAmB,IAAI;AAC3D,UAAM,mBAAmB,wBAAwB,qBAAqB,WAAW;AACjF,eACE;MAAC;MAAA;QACC,SACE,cACA,gBAAgB,iBAAiB,OAAO,KACxC,iBAAiB,YAAY;QAG/B,cAAA;UAAC,UAAU;UAAV;YACE,GAAG;YACJ,KAAK;YACL,cAAY,gBAAgB,iBAAiB,OAAO;UAAA;QACtD;MAAA;IACF;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,iBAAiB;AAKvB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,aAAa,GAAG,eAAe,IAAI;AAC3C,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACL,oBAAiB;QAChB,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAMnB,IAAM,YAAkB;EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,GAAG,WAAW,IAAI;AACvC,UAAM,cAAc,eAAe,WAAW;AAC9C,eAAO,wBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc;EACpF;AACF;AAEA,UAAU,cAAc;AAMxB,IAAM,WAAW;AASjB,IAAM,CAAC,iBAAiB,iBAAiB,IAAI,kBAAuC,QAAQ;AAQ5F,IAAM,UAAkC,CAAC,UAAqC;AAC5E,QAAM,EAAE,aAAa,UAAU,OAAO,OAAO,aAAa,IAAI;AAC9D,QAAM,oBAAoB,eAAe,UAAU,WAAW;AAC9D,QAAM,cAAc,eAAe,WAAW;AAC9C,QAAM,CAAC,SAAS,UAAU,IAAU,eAAuC,IAAI;AAC/E,QAAM,CAAC,SAAS,UAAU,IAAU,eAAoC,IAAI;AAC5E,QAAM,mBAAmB,eAAe,YAAY;AAG9C,EAAA,gBAAU,MAAM;AACpB,QAAI,kBAAkB,SAAS,MAAO,kBAAiB,KAAK;AAC5D,WAAO,MAAM,iBAAiB,KAAK;EACrC,GAAG,CAAC,kBAAkB,MAAM,gBAAgB,CAAC;AAE7C,aACE,wBAAiB,OAAhB,EAAsB,GAAG,aACxB,cAAA;IAAC;IAAA;MACC,OAAO;MACP;MACA,cAAc;MACd;MACA,iBAAiB;MAEjB,cAAA;QAAC;QAAA;UACC,OAAO;UACP,WAAW,MAAM;UACjB,WAAW,MAAM;UACjB;UACA,iBAAiB;UAEhB;QAAA;MACH;IAAA;EACF,EAAA,CACF;AAEJ;AAEA,QAAQ,cAAc;AAMtB,IAAM,mBAAmB;AAKzB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,UAAU,eAAe,kBAAkB,MAAM,WAAW;AAClE,UAAM,cAAc,mBAAmB,kBAAkB,MAAM,WAAW;AAC1E,UAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;AACxE,UAAM,iBAAiB,sBAAsB,kBAAkB,MAAM,WAAW;AAChF,UAAM,eAAqB,aAAsB,IAAI;AACrD,UAAM,EAAE,sBAAsB,2BAA2B,IAAI;AAC7D,UAAM,QAAQ,EAAE,aAAa,MAAM,YAAY;AAE/C,UAAM,iBAAuB,kBAAY,MAAM;AAC7C,UAAI,aAAa,QAAS,QAAO,aAAa,aAAa,OAAO;AAClE,mBAAa,UAAU;IACzB,GAAG,CAAC,CAAC;AAEC,IAAA,gBAAU,MAAM,gBAAgB,CAAC,cAAc,CAAC;AAEhD,IAAA,gBAAU,MAAM;AACpB,YAAM,oBAAoB,qBAAqB;AAC/C,aAAO,MAAM;AACX,eAAO,aAAa,iBAAiB;AACrC,mCAA2B,IAAI;MACjC;IACF,GAAG,CAAC,sBAAsB,0BAA0B,CAAC;AAErD,eACE,wBAAC,YAAA,EAAW,SAAO,MAAE,GAAG,OACtB,cAAA;MAAC;MAAA;QACC,IAAI,WAAW;QACf,iBAAc;QACd,iBAAe,QAAQ;QACvB,iBAAe,WAAW;QAC1B,cAAY,aAAa,QAAQ,IAAI;QACpC,GAAG;QACJ,KAAK,YAAY,cAAc,WAAW,eAAe;QAGzD,SAAS,CAAC,UAAU;;AAClB,sBAAM,YAAN,+BAAgB;AAChB,cAAI,MAAM,YAAY,MAAM,iBAAkB;AAM9C,gBAAM,cAAc,MAAM;AAC1B,cAAI,CAAC,QAAQ,KAAM,SAAQ,aAAa,IAAI;QAC9C;QACA,eAAe;UACb,MAAM;UACN,UAAU,CAAC,UAAU;AACnB,2BAAe,YAAY,KAAK;AAChC,gBAAI,MAAM,iBAAkB;AAC5B,gBAAI,CAAC,MAAM,YAAY,CAAC,QAAQ,QAAQ,CAAC,aAAa,SAAS;AAC7D,6BAAe,2BAA2B,IAAI;AAC9C,2BAAa,UAAU,OAAO,WAAW,MAAM;AAC7C,wBAAQ,aAAa,IAAI;AACzB,+BAAe;cACjB,GAAG,GAAG;YACR;UACF,CAAC;QACH;QACA,gBAAgB;UACd,MAAM;UACN,UAAU,CAAC,UAAU;;AACnB,2BAAe;AAEf,kBAAM,eAAc,aAAQ,YAAR,mBAAiB;AACrC,gBAAI,aAAa;AAEf,oBAAM,QAAO,aAAQ,YAAR,mBAAiB,QAAQ;AACtC,oBAAM,YAAY,SAAS;AAC3B,oBAAM,QAAQ,YAAY,KAAK;AAC/B,oBAAM,kBAAkB,YAAY,YAAY,SAAS,OAAO;AAChE,oBAAM,iBAAiB,YAAY,YAAY,UAAU,MAAM;AAE/D,6BAAe,2BAA2B;gBACxC,MAAM;;;kBAGJ,EAAE,GAAG,MAAM,UAAU,OAAO,GAAG,MAAM,QAAQ;kBAC7C,EAAE,GAAG,iBAAiB,GAAG,YAAY,IAAI;kBACzC,EAAE,GAAG,gBAAgB,GAAG,YAAY,IAAI;kBACxC,EAAE,GAAG,gBAAgB,GAAG,YAAY,OAAO;kBAC3C,EAAE,GAAG,iBAAiB,GAAG,YAAY,OAAO;gBAC9C;gBACA;cACF,CAAC;AAED,qBAAO,aAAa,qBAAqB,OAAO;AAChD,mCAAqB,UAAU,OAAO;gBACpC,MAAM,eAAe,2BAA2B,IAAI;gBACpD;cACF;YACF,OAAO;AACL,6BAAe,eAAe,KAAK;AACnC,kBAAI,MAAM,iBAAkB;AAG5B,6BAAe,2BAA2B,IAAI;YAChD;UACF,CAAC;QACH;QACA,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;;AAC1D,gBAAM,gBAAgB,eAAe,UAAU,YAAY;AAC3D,cAAI,MAAM,YAAa,iBAAiB,MAAM,QAAQ,IAAM;AAC5D,cAAI,cAAc,YAAY,GAAG,EAAE,SAAS,MAAM,GAAG,GAAG;AACtD,oBAAQ,aAAa,IAAI;AAGzB,0BAAQ,YAAR,mBAAiB;AAEjB,kBAAM,eAAe;UACvB;QACF,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,mBAAmB;AAezB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;AACtE,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,gBAAgB,IAAI;AACtE,UAAM,UAAU,eAAe,cAAc,MAAM,WAAW;AAC9D,UAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;AACtE,UAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;AACxE,UAAM,MAAY,aAA8B,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,eACE,wBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,aAChC,cAAA,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACvC,cAAA,wBAAC,WAAW,MAAX,EAAgB,OAAO,MAAM,aAC5B,cAAA;MAAC;MAAA;QACC,IAAI,WAAW;QACf,mBAAiB,WAAW;QAC3B,GAAG;QACJ,KAAK;QACL,OAAM;QACN,MAAM,YAAY,QAAQ,QAAQ,SAAS;QAC3C,6BAA6B;QAC7B,sBAAsB;QACtB,WAAW;QACX,iBAAiB,CAAC,UAAU;;AAE1B,cAAI,YAAY,mBAAmB,QAAS,WAAI,YAAJ,mBAAa;AACzD,gBAAM,eAAe;QACvB;QAGA,kBAAkB,CAAC,UAAU,MAAM,eAAe;QAClD,gBAAgB,qBAAqB,MAAM,gBAAgB,CAAC,UAAU;AAGpE,cAAI,MAAM,WAAW,WAAW,QAAS,SAAQ,aAAa,KAAK;QACrE,CAAC;QACD,iBAAiB,qBAAqB,MAAM,iBAAiB,CAAC,UAAU;AACtE,sBAAY,QAAQ;AAEpB,gBAAM,eAAe;QACvB,CAAC;QACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;;AAE1D,gBAAM,kBAAkB,MAAM,cAAc,SAAS,MAAM,MAAqB;AAChF,gBAAM,aAAa,eAAe,YAAY,GAAG,EAAE,SAAS,MAAM,GAAG;AACrE,cAAI,mBAAmB,YAAY;AACjC,oBAAQ,aAAa,KAAK;AAE1B,6BAAW,YAAX,mBAAoB;AAEpB,kBAAM,eAAe;UACvB;QACF,CAAC;MAAA;IACH,EAAA,CACF,EAAA,CACF,EAAA,CACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAI7B,SAAS,aAAa,MAAe;AACnC,SAAO,OAAO,SAAS;AACzB;AAEA,SAAS,gBAAgB,SAAoD;AAC3E,SAAO,YAAY;AACrB;AAEA,SAAS,gBAAgB,SAAuB;AAC9C,SAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;AAEA,SAAS,WAAW,YAA2B;AAC7C,QAAM,6BAA6B,SAAS;AAC5C,aAAW,aAAa,YAAY;AAElC,QAAI,cAAc,2BAA4B;AAC9C,cAAU,MAAM;AAChB,QAAI,SAAS,kBAAkB,2BAA4B;EAC7D;AACF;AAMA,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAI,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAC;AAC3E;AAmBA,SAAS,aAAa,QAAkB,QAAgB,cAAuB;AAC7E,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,CAAC;AAC7F,QAAM,mBAAmB,aAAa,OAAO,CAAC,IAAI;AAClD,QAAM,oBAAoB,eAAe,OAAO,QAAQ,YAAY,IAAI;AACxE,MAAI,gBAAgB,UAAU,QAAQ,KAAK,IAAI,mBAAmB,CAAC,CAAC;AACpE,QAAM,sBAAsB,iBAAiB,WAAW;AACxD,MAAI,oBAAqB,iBAAgB,cAAc,OAAO,CAAC,MAAM,MAAM,YAAY;AACvF,QAAM,YAAY,cAAc;IAAK,CAAC,UACpC,MAAM,YAAY,EAAE,WAAW,iBAAiB,YAAY,CAAC;EAC/D;AACA,SAAO,cAAc,eAAe,YAAY;AAClD;AASA,SAAS,iBAAiB,OAAc,SAAkB;AACxD,QAAM,EAAE,GAAG,EAAE,IAAI;AACjB,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,IAAI,QAAQ,QAAQ,IAAI,KAAK;AACnE,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AACtB,UAAM,KAAK,QAAQ,CAAC,EAAE;AAGtB,UAAM,YAAc,KAAK,MAAQ,KAAK,KAAQ,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AACrF,QAAI,UAAW,UAAS,CAAC;EAC3B;AAEA,SAAO;AACT;AAEA,SAAS,qBAAqB,OAA2B,MAAgB;AACvE,MAAI,CAAC,KAAM,QAAO;AAClB,QAAM,YAAY,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;AACvD,SAAO,iBAAiB,WAAW,IAAI;AACzC;AAEA,SAAS,UAAa,SAAqE;AACzF,SAAO,CAAC,UAAW,MAAM,gBAAgB,UAAU,QAAQ,KAAK,IAAI;AACtE;AAEA,IAAMC,QAAO;AACb,IAAMC,UAAS;AACf,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAMC,QAAO;AACb,IAAM,eAAe;AACrB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,YAAY;AAClB,IAAMC,SAAQ;AACd,IAAM,MAAM;AACZ,IAAM,aAAa;AACnB,IAAM,aAAa;;;ADxuCb,IAAAC,sBAAA;AA5DN,IAAM,qBAAqB;AAG3B,IAAM,CAAC,2BAA2B,uBAAuB,IAAI;EAC3D;EACA,CAAC,eAAe;AAClB;AACA,IAAM,eAAe,gBAAgB;AAYrC,IAAM,CAAC,sBAAsB,sBAAsB,IACjD,0BAAoD,kBAAkB;AAWxE,IAAM,eAA4C,CAAC,UAA0C;AAC3F,QAAM;IACJ;IACA;IACA;IACA,MAAM;IACN;IACA;IACA,QAAQ;EACV,IAAI;AACJ,QAAM,YAAY,aAAa,mBAAmB;AAClD,QAAM,aAAmB,cAA0B,IAAI;AACvD,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;IACnD,MAAM;IACN,aAAa;IACb,UAAU;EACZ,CAAC;AAED,aACE;IAAC;IAAA;MACC,OAAO;MACP,WAAW,MAAM;MACjB;MACA,WAAW,MAAM;MACjB;MACA,cAAc;MACd,cAAoB,mBAAY,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;MACjF;MAEA,cAAA,yBAAe,OAAd,EAAoB,GAAG,WAAW,MAAY,cAAc,SAAS,KAAU,OAC7E,SAAA,CACH;IAAA;EACF;AAEJ;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAMrB,IAAM,sBAA4B;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,qBAAqB,WAAW,OAAO,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,uBAAuB,cAAc,mBAAmB;AACxE,UAAM,YAAY,aAAa,mBAAmB;AAClD,eACE,yBAAe,SAAd,EAAqB,SAAO,MAAE,GAAG,WAChC,cAAA;MAAC,UAAU;MAAV;QACC,MAAK;QACL,IAAI,QAAQ;QACZ,iBAAc;QACd,iBAAe,QAAQ;QACvB,iBAAe,QAAQ,OAAO,QAAQ,YAAY;QAClD,cAAY,QAAQ,OAAO,SAAS;QACpC,iBAAe,WAAW,KAAK;QAC/B;QACC,GAAG;QACJ,KAAK,YAAY,cAAc,QAAQ,UAAU;QACjD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAGlE,cAAI,CAAC,YAAY,MAAM,WAAW,KAAK,MAAM,YAAY,OAAO;AAC9D,oBAAQ,aAAa;AAGrB,gBAAI,CAAC,QAAQ,KAAM,OAAM,eAAe;UAC1C;QACF,CAAC;QACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,cAAI,SAAU;AACd,cAAI,CAAC,SAAS,GAAG,EAAE,SAAS,MAAM,GAAG,EAAG,SAAQ,aAAa;AAC7D,cAAI,MAAM,QAAQ,YAAa,SAAQ,aAAa,IAAI;AAGxD,cAAI,CAAC,SAAS,KAAK,WAAW,EAAE,SAAS,MAAM,GAAG,EAAG,OAAM,eAAe;QAC5E,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAEA,oBAAoB,cAAc;AAMlC,IAAMC,eAAc;AAKpB,IAAM,qBAAwD,CAC5D,UACG;AACH,QAAM,EAAE,qBAAqB,GAAG,YAAY,IAAI;AAChD,QAAM,YAAY,aAAa,mBAAmB;AAClD,aAAO,yBAAeC,SAAd,EAAsB,GAAG,WAAY,GAAG,YAAA,CAAa;AAC/D;AAEA,mBAAmB,cAAcD;AAMjC,IAAME,gBAAe;AAMrB,IAAM,sBAA4B;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,qBAAqB,GAAG,aAAa,IAAI;AACjD,UAAM,UAAU,uBAAuBA,eAAc,mBAAmB;AACxE,UAAM,YAAY,aAAa,mBAAmB;AAClD,UAAM,0BAAgC,cAAO,KAAK;AAElD,eACE;MAAe;MAAd;QACC,IAAI,QAAQ;QACZ,mBAAiB,QAAQ;QACxB,GAAG;QACH,GAAG;QACJ,KAAK;QACL,kBAAkB,qBAAqB,MAAM,kBAAkB,CAAC,UAAU;;AACxE,cAAI,CAAC,wBAAwB,QAAS,eAAQ,WAAW,YAAnB,mBAA4B;AAClE,kCAAwB,UAAU;AAElC,gBAAM,eAAe;QACvB,CAAC;QACD,mBAAmB,qBAAqB,MAAM,mBAAmB,CAAC,UAAU;AAC1E,gBAAM,gBAAgB,MAAM,OAAO;AACnC,gBAAM,gBAAgB,cAAc,WAAW,KAAK,cAAc,YAAY;AAC9E,gBAAM,eAAe,cAAc,WAAW,KAAK;AACnD,cAAI,CAAC,QAAQ,SAAS,aAAc,yBAAwB,UAAU;QACxE,CAAC;QACD,OAAO;UACL,GAAG,MAAM;;UAET,GAAG;YACD,kDACE;YACF,iDAAiD;YACjD,kDACE;YACF,uCAAuC;YACvC,wCAAwC;UAC1C;QACF;MAAA;IACF;EAEJ;AACF;AAEA,oBAAoB,cAAcA;AAMlC,IAAMC,cAAa;AAMnB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,qBAAqB,GAAG,WAAW,IAAI;AAC/C,UAAM,YAAY,aAAa,mBAAmB;AAClD,eAAO,yBAAe,OAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,kBAAkB,cAAcA;AAMhC,IAAMC,cAAa;AAMnB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,qBAAqB,GAAG,WAAW,IAAI;AAC/C,UAAM,YAAY,aAAa,mBAAmB;AAClD,eAAO,yBAAe,OAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,kBAAkB,cAAcA;AAMhC,IAAMC,aAAY;AAMlB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,qBAAqB,GAAG,UAAU,IAAI;AAC9C,UAAM,YAAY,aAAa,mBAAmB;AAClD,eAAO,yBAAe,OAAd,EAAoB,GAAG,WAAY,GAAG,WAAW,KAAK,aAAA,CAAc;EAC9E;AACF;AAEA,iBAAiB,cAAcA;AAM/B,IAAMC,sBAAqB;AAM3B,IAAM,2BAAiC,kBAGrC,CAAC,OAAmD,iBAAiB;AACrE,QAAM,EAAE,qBAAqB,GAAG,kBAAkB,IAAI;AACtD,QAAM,YAAY,aAAa,mBAAmB;AAClD,aAAO,yBAAe,cAAd,EAA4B,GAAG,WAAY,GAAG,mBAAmB,KAAK,aAAA,CAAc;AAC9F,CAAC;AAED,yBAAyB,cAAcA;AAMvC,IAAMC,oBAAmB;AAMzB,IAAM,yBAA+B,kBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,qBAAqB,GAAG,gBAAgB,IAAI;AACpD,QAAM,YAAY,aAAa,mBAAmB;AAClD,aAAO,yBAAe,YAAd,EAA0B,GAAG,WAAY,GAAG,iBAAiB,KAAK,aAAA,CAAc;AAC1F,CAAC;AAED,uBAAuB,cAAcA;AAMrC,IAAMC,mBAAkB;AAMxB,IAAM,wBAA8B,kBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,qBAAqB,GAAG,eAAe,IAAI;AACnD,QAAM,YAAY,aAAa,mBAAmB;AAClD,aAAO,yBAAe,WAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,aAAA,CAAc;AACxF,CAAC;AAED,sBAAsB,cAAcA;AAMpC,IAAM,iBAAiB;AAMvB,IAAM,4BAAkC,kBAGtC,CAAC,OAAoD,iBAAiB;AACtE,QAAM,EAAE,qBAAqB,GAAG,mBAAmB,IAAI;AACvD,QAAM,YAAY,aAAa,mBAAmB;AAClD,aAAO,yBAAe,eAAd,EAA6B,GAAG,WAAY,GAAG,oBAAoB,KAAK,aAAA,CAAc;AAChG,CAAC;AAED,0BAA0B,cAAc;AAMxC,IAAMC,kBAAiB;AAMvB,IAAM,wBAA8B,kBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,qBAAqB,GAAG,eAAe,IAAI;AACnD,QAAM,YAAY,aAAa,mBAAmB;AAClD,aAAO,yBAAe,WAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,aAAA,CAAc;AACxF,CAAC;AAED,sBAAsB,cAAcA;AAMpC,IAAMC,cAAa;AAMnB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,qBAAqB,GAAG,WAAW,IAAI;AAC/C,UAAM,YAAY,aAAa,mBAAmB;AAClD,eAAO,yBAAe,QAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,kBAAkB,cAAcA;AAahC,IAAM,kBAAkD,CACtD,UACG;AACH,QAAM,EAAE,qBAAqB,UAAU,MAAM,UAAU,cAAc,YAAY,IAAI;AACrF,QAAM,YAAY,aAAa,mBAAmB;AAClD,QAAM,CAAC,OAAO,OAAO,OAAO,IAAI,qBAAqB;IACnD,MAAM;IACN,aAAa;IACb,UAAU;EACZ,CAAC;AAED,aACE,yBAAe,KAAd,EAAmB,GAAG,WAAW,MAAY,cAAc,SACzD,SAAA,CACH;AAEJ;AAMA,IAAMC,oBAAmB;AAMzB,IAAM,yBAA+B,kBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,qBAAqB,GAAG,gBAAgB,IAAI;AACpD,QAAM,YAAY,aAAa,mBAAmB;AAClD,aAAO,yBAAe,YAAd,EAA0B,GAAG,WAAY,GAAG,iBAAiB,KAAK,aAAA,CAAc;AAC1F,CAAC;AAED,uBAAuB,cAAcA;AAMrC,IAAMC,oBAAmB;AAMzB,IAAM,yBAA+B,kBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,qBAAqB,GAAG,gBAAgB,IAAI;AACpD,QAAM,YAAY,aAAa,mBAAmB;AAElD,aACE;IAAe;IAAd;MACE,GAAG;MACH,GAAG;MACJ,KAAK;MACL,OAAO;QACL,GAAG,MAAM;;QAET,GAAG;UACD,kDAAkD;UAClD,iDAAiD;UACjD,kDAAkD;UAClD,uCAAuC;UACvC,wCAAwC;QAC1C;MACF;IAAA;EACF;AAEJ,CAAC;AAED,uBAAuB,cAAcA;AAIrC,IAAMC,SAAO;AACb,IAAM,UAAU;AAChB,IAAMZ,WAAS;AACf,IAAMa,YAAU;AAChB,IAAMC,SAAQ;AACd,IAAMC,SAAQ;AACd,IAAMC,SAAO;AACb,IAAMC,gBAAe;AACrB,IAAMC,cAAa;AACnB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AACtB,IAAMC,aAAY;AAClB,IAAMC,UAAQ;AACd,IAAMC,OAAM;AACZ,IAAMC,cAAa;AACnB,IAAMC,cAAa;", "names": ["React", "Root", "<PERSON><PERSON>", "Portal", "Content", "<PERSON><PERSON>", "Arrow", "import_jsx_runtime", "PORTAL_NAME", "Portal", "CONTENT_NAME", "GROUP_NAME", "LABEL_NAME", "ITEM_NAME", "CHECKBOX_ITEM_NAME", "RADIO_GROUP_NAME", "RADIO_ITEM_NAME", "SEPARATOR_NAME", "ARROW_NAME", "SUB_TRIGGER_NAME", "SUB_CONTENT_NAME", "Root", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"]}