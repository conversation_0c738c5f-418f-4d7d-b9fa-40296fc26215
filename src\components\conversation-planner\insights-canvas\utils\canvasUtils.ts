
import { NodePosition } from "@/stores/useCanvasStore";
import { NODE_WIDTH, NODE_HEIGHT } from "../canvasConstants";

export const calculateBounds = (
  nodeIds: string[],
  positions: NodePosition[]
): { x: number; y: number; width: number; height: number } => {
  try {
    const relevantPositions = positions.filter(p => nodeIds.includes(p.id) && 
      p.x !== undefined && p.y !== undefined && 
      typeof p.x === 'number' && typeof p.y === 'number');
      
    if (relevantPositions.length === 0) {
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    const padding = 20;

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    relevantPositions.forEach(pos => {
      minX = Math.min(minX, pos.x);
      minY = Math.min(minY, pos.y);
      maxX = Math.max(maxX, pos.x);
      maxY = Math.max(maxY, pos.y);
    });

    // Check for invalid calculations
    if (!isFinite(minX) || !isFinite(minY) || !isFinite(maxX) || !isFinite(maxY)) {
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    return {
      x: minX - padding,
      y: minY - padding,
      width: (maxX - minX) + NODE_WIDTH + padding * 2,
      height: (maxY - minY) + NODE_HEIGHT + padding * 2,
    };
  } catch (error) {
    console.warn('Failed to calculate bounds:', error);
    return { x: 0, y: 0, width: 0, height: 0 };
  }
};

export const calculateCenter = (
    nodeIds: string[],
    positions: NodePosition[]
): { x: number; y: number } => {
    try {
        const relevantPositions = positions.filter(p => nodeIds.includes(p.id) && 
          p.x !== undefined && p.y !== undefined && 
          typeof p.x === 'number' && typeof p.y === 'number');
          
        if (relevantPositions.length === 0) {
            return { x: 0, y: 0 };
        }

        const centerX = relevantPositions.reduce((sum, pos) => sum + pos.x, 0) / relevantPositions.length;
        const centerY = relevantPositions.reduce((sum, pos) => sum + pos.y, 0) / relevantPositions.length;

        // Check for invalid calculations
        if (!isFinite(centerX) || !isFinite(centerY)) {
            return { x: 0, y: 0 };
        }

        return {
            x: centerX + NODE_WIDTH / 2,
            y: centerY + NODE_HEIGHT / 2,
        };
    } catch (error) {
        console.warn('Failed to calculate center:', error);
        return { x: 0, y: 0 };
    }
};
