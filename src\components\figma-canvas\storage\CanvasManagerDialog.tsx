import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Save, 
  FolderOpen, 
  Trash2, 
  Clock, 
  HardDrive,
  AlertTriangle,
  Loader2
} from 'lucide-react';
import { useSavedCanvases, useEmergencyRecover } from '@/hooks/useAutoSave';
import { cn } from '@/lib/utils';

interface CanvasManagerDialogProps {
  className?: string;
}

export const CanvasManagerDialog: React.FC<CanvasManagerDialogProps> = ({ className }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'save' | 'load' | 'storage'>('save');
  const [saveName, setSaveName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [savedCanvases, setSavedCanvases] = useState<any[]>([]);
  const [storageUsage, setStorageUsage] = useState({ used: 0, quota: 0 });

  const { saveCanvas, loadCanvas, getSavedCanvases, deleteCanvas, getStorageUsage } = useSavedCanvases();
  const { checkForEmergencySave, recoverFromEmergencySave, clearEmergencySave } = useEmergencyRecover();

  const emergencyCheck = checkForEmergencySave();

  useEffect(() => {
    if (isOpen) {
      loadSavedCanvases();
      loadStorageUsage();
    }
  }, [isOpen]);

  const loadSavedCanvases = async () => {
    try {
      const canvases = await getSavedCanvases();
      setSavedCanvases(canvases);
    } catch (error) {
      console.error('Failed to load saved canvases:', error);
    }
  };

  const loadStorageUsage = async () => {
    try {
      const usage = await getStorageUsage();
      setStorageUsage(usage);
    } catch (error) {
      console.error('Failed to load storage usage:', error);
    }
  };

  const handleSave = async () => {
    if (!saveName.trim() || isSaving) return;

    setIsSaving(true);
    try {
      await saveCanvas(saveName.trim());
      setSaveName('');
      setIsOpen(false);
      await loadSavedCanvases();
    } catch (error) {
      console.error('Failed to save canvas:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleLoad = async (id: string) => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      await loadCanvas(id);
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to load canvas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteCanvas(id);
      await loadSavedCanvases();
      await loadStorageUsage();
    } catch (error) {
      console.error('Failed to delete canvas:', error);
    }
  };

  const handleEmergencyRecover = async () => {
    try {
      const recovered = await recoverFromEmergencySave();
      if (recovered) {
        setIsOpen(false);
      }
    } catch (error) {
      console.error('Failed to recover from emergency save:', error);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date) => {
    return date.toLocaleString();
  };

  return (
    <>
      {/* Emergency Recovery Banner */}
      {emergencyCheck.hasEmergencySave && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-2 rounded-lg shadow-lg">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4" />
            <span className="text-sm">
              Emergency save detected from {emergencyCheck.timestamp?.toLocaleString()}
            </span>
            <Button size="sm" variant="outline" onClick={handleEmergencyRecover}>
              Recover
            </Button>
            <Button size="sm" variant="ghost" onClick={clearEmergencySave}>
              Dismiss
            </Button>
          </div>
        </div>
      )}

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className={className}>
            <Save className="w-4 h-4 mr-2" />
            Manage Canvases
          </Button>
        </DialogTrigger>
        
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Canvas Manager</DialogTitle>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={(tab) => setActiveTab(tab as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="save" className="flex items-center gap-2">
                <Save className="w-4 h-4" />
                Save
              </TabsTrigger>
              <TabsTrigger value="load" className="flex items-center gap-2">
                <FolderOpen className="w-4 h-4" />
                Load
              </TabsTrigger>
              <TabsTrigger value="storage" className="flex items-center gap-2">
                <HardDrive className="w-4 h-4" />
                Storage
              </TabsTrigger>
            </TabsList>

            <TabsContent value="save" className="space-y-4 mt-4">
              <div>
                <Label htmlFor="canvas-name" className="text-sm">Canvas Name</Label>
                <Input
                  id="canvas-name"
                  value={saveName}
                  onChange={(e) => setSaveName(e.target.value)}
                  placeholder="Enter canvas name..."
                  className="mt-1"
                  onKeyDown={(e) => e.key === 'Enter' && handleSave()}
                />
              </div>

              <Button 
                onClick={handleSave} 
                disabled={!saveName.trim() || isSaving}
                className="w-full"
              >
                {isSaving ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save Canvas
                  </>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="load" className="space-y-4 mt-4">
              <ScrollArea className="h-96">
                {savedCanvases.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    <FolderOpen className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No saved canvases found</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {savedCanvases.map((canvas) => (
                      <div
                        key={canvas.id}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{canvas.name}</h4>
                          <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                            <span className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {formatDate(canvas.updatedAt)}
                            </span>
                            <span>
                              {Object.keys(canvas.canvasState.objects).length} objects
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleLoad(canvas.id)}
                            disabled={isLoading}
                          >
                            {isLoading ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              'Load'
                            )}
                          </Button>
                          
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDelete(canvas.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="storage" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Storage Usage</h4>
                  <div className="bg-gray-100 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm">Used Storage</span>
                      <span className="text-sm font-mono">
                        {formatBytes(storageUsage.used)} / {formatBytes(storageUsage.quota)}
                      </span>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all"
                        style={{
                          width: `${storageUsage.quota > 0 ? (storageUsage.used / storageUsage.quota) * 100 : 0}%`
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Saved Canvases</h4>
                  <div className="text-sm text-gray-600">
                    <p>{savedCanvases.length} canvas{savedCanvases.length !== 1 ? 'es' : ''} saved</p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Auto-Save</h4>
                  <div className="text-sm text-gray-600">
                    <p>Automatically saves your work every 30 seconds</p>
                    <p>Emergency backup saved on page close</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
};
