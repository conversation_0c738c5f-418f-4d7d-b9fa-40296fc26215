import * as THREE from 'three';
import { AnalysisCluster, SimpleNode } from './types';

/**
 * Utility class for creating and managing cluster visualizations in the 3D scene
 */
export class ClusterVisualization {
  private scene: THREE.Scene;
  private clusterMeshes: Map<string, THREE.Mesh> = new Map();

  constructor(scene: THREE.Scene) {
    this.scene = scene;
  }

  /**
   * Create or update cluster visualization
   */
  updateCluster(cluster: AnalysisCluster, nodes: Map<string, SimpleNode>): void {
    // Remove existing cluster mesh if it exists
    this.removeCluster(cluster.id);

    // Get nodes that belong to this cluster
    const clusterNodes = cluster.nodeIds
      .map(nodeId => nodes.get(nodeId))
      .filter(node => node !== undefined) as SimpleNode[];

    if (clusterNodes.length === 0) return;

    // Calculate cluster bounds
    const bounds = this.calculateClusterBounds(clusterNodes);
    
    // Create cluster visualization mesh
    const clusterMesh = this.createClusterMesh(cluster, bounds);
    
    // Add to scene and store reference
    this.scene.add(clusterMesh);
    this.clusterMeshes.set(cluster.id, clusterMesh);
  }

  /**
   * Remove cluster visualization
   */
  removeCluster(clusterId: string): void {
    const existingMesh = this.clusterMeshes.get(clusterId);
    if (existingMesh) {
      this.scene.remove(existingMesh);
      existingMesh.geometry.dispose();
      (existingMesh.material as THREE.Material).dispose();
      this.clusterMeshes.delete(clusterId);
    }
  }

  /**
   * Update all cluster visualizations
   */
  updateAllClusters(clusters: AnalysisCluster[], nodes: Map<string, SimpleNode>): void {
    // Remove clusters that no longer exist
    const currentClusterIds = new Set(clusters.map(c => c.id));
    for (const [clusterId] of this.clusterMeshes) {
      if (!currentClusterIds.has(clusterId)) {
        this.removeCluster(clusterId);
      }
    }

    // Update or create clusters
    clusters.forEach(cluster => {
      this.updateCluster(cluster, nodes);
    });
  }

  /**
   * Calculate bounding box for cluster nodes
   */
  private calculateClusterBounds(nodes: SimpleNode[]): {
    center: THREE.Vector2;
    width: number;
    height: number;
    padding: number;
  } {
    if (nodes.length === 0) {
      return { center: new THREE.Vector2(), width: 2, height: 2, padding: 0.5 };
    }

    let minX = Infinity, maxX = -Infinity;
    let minY = Infinity, maxY = -Infinity;

    nodes.forEach(node => {
      minX = Math.min(minX, node.position.x);
      maxX = Math.max(maxX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxY = Math.max(maxY, node.position.y);
    });

    const padding = Math.max(1.0, Math.sqrt(nodes.length) * 0.5); // Dynamic padding based on node count
    const width = Math.max(2, maxX - minX + padding * 2);
    const height = Math.max(2, maxY - minY + padding * 2);
    const center = new THREE.Vector2((minX + maxX) / 2, (minY + maxY) / 2);

    return { center, width, height, padding };
  }

  /**
   * Create cluster visualization mesh
   */
  private createClusterMesh(cluster: AnalysisCluster, bounds: {
    center: THREE.Vector2;
    width: number;
    height: number;
    padding: number;
  }): THREE.Mesh {
    // Create rounded rectangle geometry for cluster boundary
    const shape = new THREE.Shape();
    const radius = Math.min(bounds.width, bounds.height) * 0.1; // Corner radius
    const halfWidth = bounds.width / 2;
    const halfHeight = bounds.height / 2;

    // Create rounded rectangle path
    shape.moveTo(-halfWidth + radius, -halfHeight);
    shape.lineTo(halfWidth - radius, -halfHeight);
    shape.quadraticCurveTo(halfWidth, -halfHeight, halfWidth, -halfHeight + radius);
    shape.lineTo(halfWidth, halfHeight - radius);
    shape.quadraticCurveTo(halfWidth, halfHeight, halfWidth - radius, halfHeight);
    shape.lineTo(-halfWidth + radius, halfHeight);
    shape.quadraticCurveTo(-halfWidth, halfHeight, -halfWidth, halfHeight - radius);
    shape.lineTo(-halfWidth, -halfHeight + radius);
    shape.quadraticCurveTo(-halfWidth, -halfHeight, -halfWidth + radius, -halfHeight);

    const geometry = new THREE.ShapeGeometry(shape);

    // Create material with cluster color
    const color = new THREE.Color(cluster.color || 0x3b82f6);
    const material = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.15,
      side: THREE.DoubleSide,
      depthWrite: false, // Prevent z-fighting with nodes
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(bounds.center.x, bounds.center.y, -0.1); // Slightly behind nodes
    mesh.userData.clusterId = cluster.id;
    mesh.userData.clusterName = cluster.name;

    // Add border outline
    const borderGeometry = new THREE.EdgesGeometry(geometry);
    const borderMaterial = new THREE.LineBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.6,
      linewidth: 2,
    });
    const borderMesh = new THREE.LineSegments(borderGeometry, borderMaterial);
    mesh.add(borderMesh);

    // Add cluster label
    this.addClusterLabel(mesh, cluster, bounds);

    return mesh;
  }

  /**
   * Add text label to cluster
   */
  private addClusterLabel(clusterMesh: THREE.Mesh, cluster: AnalysisCluster, bounds: {
    center: THREE.Vector2;
    width: number;
    height: number;
  }): void {
    // Create canvas for text rendering
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    
    // Set canvas size
    canvas.width = 256;
    canvas.height = 64;
    
    // Configure text style
    context.fillStyle = 'rgba(255, 255, 255, 0.9)';
    context.font = 'bold 16px Inter, Arial, sans-serif';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    
    // Add background
    context.fillStyle = 'rgba(0, 0, 0, 0.7)';
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add text
    context.fillStyle = 'rgba(255, 255, 255, 0.9)';
    context.fillText(cluster.name, canvas.width / 2, canvas.height / 2);
    
    // Create texture and material
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    
    const labelMaterial = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0.8,
      depthWrite: false,
    });
    
    // Create label geometry
    const labelGeometry = new THREE.PlaneGeometry(2, 0.5);
    const labelMesh = new THREE.Mesh(labelGeometry, labelMaterial);
    
    // Position label at top of cluster
    labelMesh.position.set(0, bounds.height / 2 + 0.5, 0.1);
    
    clusterMesh.add(labelMesh);
  }

  /**
   * Get cluster mesh by ID
   */
  getClusterMesh(clusterId: string): THREE.Mesh | undefined {
    return this.clusterMeshes.get(clusterId);
  }

  /**
   * Highlight cluster
   */
  highlightCluster(clusterId: string, highlight: boolean): void {
    const mesh = this.clusterMeshes.get(clusterId);
    if (!mesh) return;

    const material = mesh.material as THREE.MeshBasicMaterial;
    if (highlight) {
      material.opacity = 0.3;
      mesh.scale.setScalar(1.05);
    } else {
      material.opacity = 0.15;
      mesh.scale.setScalar(1.0);
    }
  }

  /**
   * Dispose all cluster visualizations
   */
  dispose(): void {
    for (const [clusterId] of this.clusterMeshes) {
      this.removeCluster(clusterId);
    }
    this.clusterMeshes.clear();
  }

  /**
   * Get all cluster meshes for raycasting
   */
  getAllClusterMeshes(): THREE.Mesh[] {
    return Array.from(this.clusterMeshes.values());
  }
}

/**
 * Utility functions for cluster operations
 */
export class ClusterUtils {
  /**
   * Check if nodes can form a meaningful cluster
   */
  static canFormCluster(nodes: SimpleNode[]): boolean {
    if (nodes.length < 2) return false;
    
    // Check if nodes have some common properties
    const analysisTypes = new Set<string>();
    const styles = new Set<string>();
    const categories = new Set<string>();
    
    nodes.forEach(node => {
      if (node.data.analysisRecord) {
        analysisTypes.add(node.data.analysisRecord.analysisType);
        styles.add(node.data.analysisRecord.style);
      }
      if (node.data.category) {
        categories.add(node.data.category);
      }
    });
    
    // Nodes can form a cluster if they share at least one common property
    return analysisTypes.size <= 2 || styles.size <= 2 || categories.size <= 2;
  }

  /**
   * Suggest cluster name based on node properties
   */
  static suggestClusterName(nodes: SimpleNode[]): string {
    if (nodes.length === 0) return 'Empty Cluster';
    
    const analysisTypes = new Set<string>();
    const styles = new Set<string>();
    const categories = new Set<string>();
    
    nodes.forEach(node => {
      if (node.data.analysisRecord) {
        analysisTypes.add(node.data.analysisRecord.analysisType);
        styles.add(node.data.analysisRecord.style);
      }
      if (node.data.category) {
        categories.add(node.data.category);
      }
    });
    
    // Generate name based on common properties
    if (analysisTypes.size === 1) {
      const type = Array.from(analysisTypes)[0];
      return `${type} Analysis Group`;
    }
    
    if (styles.size === 1) {
      const style = Array.from(styles)[0];
      return `${style} Style Cluster`;
    }
    
    if (categories.size === 1) {
      const category = Array.from(categories)[0];
      return `${category} Cluster`;
    }
    
    return `Analysis Cluster (${nodes.length} nodes)`;
  }
}
