import { useState, useCallback, useEffect, useRef } from 'react';
import * as THREE from 'three';
import { SimpleNode, VisualConnection } from './types'; // Assuming types are in a shared file

export interface ForceDirectedLayoutOptions {
  repulsionStrength: number;
  attractionStrength: number;
  restLength: number;
  dampingFactor: number;
  maxSpeed: number;
  centerForceStrength: number;
  iterationsPerFrame?: number; // Number of simulation steps per animation frame
}

export const useForceDirectedLayout = (
  nodes: Map<string, SimpleNode>,
  connections: Map<string, VisualConnection>,
  options: ForceDirectedLayoutOptions
) => {
  const [isRunning, setIsRunning] = useState(true);
  const optionsRef = useRef(options);

  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  const step = useCallback((draggedNodeId: string | null) => {
    if (!nodes || nodes.size === 0) return;

    const currentOptions = optionsRef.current;
    const iterations = currentOptions.iterationsPerFrame || 1;

    for (let iter = 0; iter < iterations; iter++) {
      // 1. Reset forces
      nodes.forEach(node => {
        if (!node.force) node.force = new THREE.Vector2();
        node.force.set(0, 0);
      });

      // 2. Calculate Repulsion Forces
      const nodeList = Array.from(nodes.values());
      for (let i = 0; i < nodeList.length; i++) {
        const nodeA = nodeList[i];
        if (nodeA.id === draggedNodeId || nodeA.isDragged) continue;

        for (let j = i + 1; j < nodeList.length; j++) {
          const nodeB = nodeList[j];
          if (nodeB.id === draggedNodeId || nodeB.isDragged) continue;

          const delta = new THREE.Vector2().subVectors(nodeA.position, nodeB.position);
          const distanceSq = delta.lengthSq();
          const distance = Math.sqrt(distanceSq);

          if (distance > 0) {
            // Ensure repulsionStrength is a positive value to prevent attraction
            const effectiveRepulsion = Math.max(0.001, currentOptions.repulsionStrength);
            const repulsiveForce = (effectiveRepulsion * 0.1) / distanceSq; 
            const forceVec = delta.clone().normalize().multiplyScalar(repulsiveForce);
            nodeA.force.add(forceVec);
            nodeB.force.sub(forceVec);
          } else {
            // Add a small random force if nodes are exactly on top of each other
            const randomForce = new THREE.Vector2(Math.random() - 0.5, Math.random() - 0.5).multiplyScalar(0.1);
            nodeA.force.add(randomForce);
            nodeB.force.sub(randomForce);
          }
        }
      }

      // 3. Calculate Attraction Forces (Springs)
      connections.forEach(conn => {
        const sourceNode = nodes.get(conn.sourceNode.id);
        const targetNode = nodes.get(conn.targetNode.id);

        if (sourceNode && targetNode) {
          const delta = new THREE.Vector2().subVectors(targetNode.position, sourceNode.position);
          const distance = delta.length();
          const displacement = distance - currentOptions.restLength;
          const attractiveForce = currentOptions.attractionStrength * displacement;

          const forceVec = delta.clone().normalize().multiplyScalar(attractiveForce);
          
          if (sourceNode.id !== draggedNodeId && !sourceNode.isDragged) {
            sourceNode.force.add(forceVec);
          }
          if (targetNode.id !== draggedNodeId && !targetNode.isDragged) {
            targetNode.force.sub(forceVec);
          }
        }
      });
      
      // 4. Calculate Center Force
      if (currentOptions.centerForceStrength > 0) {
        nodeList.forEach(node => {
          if (node.id === draggedNodeId || node.isDragged) return;
          const directionToCenter = node.position.clone().multiplyScalar(-1); // Vector towards origin (0,0)
          // Ensure centerForceStrength is positive
          const effectiveCenterForce = Math.max(0, currentOptions.centerForceStrength);
          const centerForce = directionToCenter.multiplyScalar(effectiveCenterForce * 0.01); 
          node.force.add(centerForce);
        });
      }

      // 5. Update velocities and positions (Euler integration)
      nodes.forEach(node => {
        if (node.id === draggedNodeId || node.isDragged) {
          if (node.velocity) node.velocity.set(0,0); // Stop movement if dragged
          return;
        }
        
        if (!node.velocity) node.velocity = new THREE.Vector2();
        
        // Acceleration = Force / Mass (assuming mass = 1 for simplicity)
        // Velocity_new = Velocity_old * Damping + Acceleration * dt (assuming dt = 1 for simplicity)
        node.velocity.add(node.force); 
        node.velocity.multiplyScalar(currentOptions.dampingFactor);

        // Clamp speed to maxSpeed
        if (node.velocity.length() > currentOptions.maxSpeed) {
          node.velocity.normalize().multiplyScalar(currentOptions.maxSpeed);
        }

        node.position.add(node.velocity); // Position_new = Position_old + Velocity_new * dt
        
        // Update the Three.js mesh position
        if (node.mesh) {
            node.mesh.position.set(node.position.x, node.position.y, 0);
        }
      });
    }

  }, [nodes, connections, optionsRef]); // dependencies for useCallback

  return { step, isRunning, setIsRunning, config: optionsRef.current };
};
