import React, { useState, useCallback } from 'react';
import { AnalysisCluster, SimpleNode } from './types';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Badge } from '../ui/badge';
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Eye,
  Settings,
  Palette
} from 'lucide-react';

interface ClusterManagerProps {
  clusters: AnalysisCluster[];
  selectedNodes: SimpleNode[];
  position: { x: number; y: number };
  onCreateCluster: (name: string, description: string, nodeIds: string[], color?: number) => void;
  onUpdateCluster: (clusterId: string, updates: Partial<AnalysisCluster>) => void;
  onDeleteCluster: (clusterId: string) => void;
  onClose: () => void;
  existingCluster?: AnalysisCluster;
}

export const ClusterManager: React.FC<ClusterManagerProps> = ({
  clusters,
  selectedNodes,
  position,
  onCreateCluster,
  onUpdateCluster,
  onDeleteCluster,
  onClose,
  existingCluster
}) => {
  const [name, setName] = useState(existingCluster?.name || '');
  const [description, setDescription] = useState(existingCluster?.description || '');
  const [selectedColor, setSelectedColor] = useState(existingCluster?.color || 0x3b82f6);
  const [isEditing, setIsEditing] = useState(!existingCluster);

  const predefinedColors = [
    { name: 'Blue', value: 0x3b82f6 },
    { name: 'Green', value: 0x10b981 },
    { name: 'Purple', value: 0x8b5cf6 },
    { name: 'Red', value: 0xef4444 },
    { name: 'Orange', value: 0xf97316 },
    { name: 'Pink', value: 0xec4899 },
    { name: 'Indigo', value: 0x6366f1 },
    { name: 'Teal', value: 0x14b8a6 },
  ];

  const handleSave = useCallback(() => {
    if (!name.trim()) {
      alert('Please enter a cluster name');
      return;
    }

    if (existingCluster) {
      // Update existing cluster
      onUpdateCluster(existingCluster.id, {
        name: name.trim(),
        description: description.trim() || undefined,
        color: selectedColor,
      });
      setIsEditing(false);
    } else {
      // Create new cluster
      if (selectedNodes.length === 0) {
        alert('Please select at least one node to create a cluster');
        return;
      }
      
      onCreateCluster(
        name.trim(),
        description.trim(),
        selectedNodes.map(node => node.id),
        selectedColor
      );
      onClose();
    }
  }, [name, description, selectedColor, existingCluster, selectedNodes, onCreateCluster, onUpdateCluster, onClose]);

  const handleDelete = useCallback(() => {
    if (existingCluster && confirm(`Are you sure you want to delete the cluster "${existingCluster.name}"?`)) {
      onDeleteCluster(existingCluster.id);
      onClose();
    }
  }, [existingCluster, onDeleteCluster, onClose]);

  const generateSuggestedNames = useCallback(() => {
    const suggestions: string[] = [];
    
    if (selectedNodes.length > 0) {
      // Analyze node types and analysis records
      const analysisTypes = new Set<string>();
      const styles = new Set<string>();
      const models = new Set<string>();
      
      selectedNodes.forEach(node => {
        if (node.data.analysisRecord) {
          analysisTypes.add(node.data.analysisRecord.analysisType);
          styles.add(node.data.analysisRecord.style);
          models.add(node.data.analysisRecord.model);
        }
        if (node.data.category) {
          suggestions.push(`${node.data.category} Group`);
        }
      });
      
      // Generate suggestions based on common properties
      if (analysisTypes.size === 1) {
        const type = Array.from(analysisTypes)[0];
        suggestions.push(`${type} Analysis Cluster`);
      }
      
      if (styles.size === 1) {
        const style = Array.from(styles)[0];
        suggestions.push(`${style} Style Group`);
      }
      
      if (models.size === 1) {
        const model = Array.from(models)[0];
        suggestions.push(`${model} Results`);
      }
    }
    
    // Generic suggestions
    suggestions.push('Research Group', 'Analysis Set', 'Related Studies', 'Comparison Set', 'Workflow Cluster');
    
    return [...new Set(suggestions)].slice(0, 5); // Remove duplicates and limit to 5
  }, [selectedNodes]);

  const modalStyle: React.CSSProperties = {
    position: 'absolute',
    left: Math.min(position.x, window.innerWidth - 450),
    top: Math.min(position.y, window.innerHeight - 600),
    zIndex: 25,
    width: '420px',
    backgroundColor: 'rgba(20, 20, 40, 0.98)',
    backdropFilter: 'blur(15px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '12px',
    boxShadow: '0 12px 48px rgba(0, 0, 0, 0.6)',
  };

  return (
    <Card style={modalStyle}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <CardTitle className="text-white text-base flex items-center">
            <Users className="w-5 h-5 mr-2" />
            {existingCluster ? (isEditing ? 'Edit Cluster' : 'Cluster Details') : 'Create Cluster'}
          </CardTitle>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white text-lg leading-none"
            style={{ background: 'none', border: 'none', cursor: 'pointer' }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {/* Cluster Preview */}
        {existingCluster && !isEditing && (
          <div className="mt-3 p-3 bg-white/5 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">{existingCluster.name}</div>
                {existingCluster.description && (
                  <div className="text-gray-300 text-sm mt-1">{existingCluster.description}</div>
                )}
                <div className="text-gray-400 text-xs mt-1">
                  {existingCluster.nodeIds.length} nodes • Created {new Date(existingCluster.createdAt).toLocaleDateString()}
                </div>
              </div>
              <div 
                className="w-6 h-6 rounded-full border-2 border-white/20"
                style={{ backgroundColor: `#${existingCluster.color?.toString(16).padStart(6, '0')}` }}
              />
            </div>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {(isEditing || !existingCluster) && (
          <>
            {/* Cluster Name */}
            <div>
              <Label htmlFor="cluster-name" className="text-white text-sm">
                Cluster Name *
              </Label>
              <Input
                id="cluster-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter cluster name..."
                className="mt-1 bg-white/10 border-white/20 text-white placeholder-gray-400"
              />
              
              {/* Suggested Names */}
              {!existingCluster && (
                <div className="mt-2">
                  <div className="text-xs text-gray-400 mb-1">Suggestions:</div>
                  <div className="flex flex-wrap gap-1">
                    {generateSuggestedNames().map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => setName(suggestion)}
                        className="text-xs px-2 py-1 bg-white/10 hover:bg-white/20 rounded text-gray-300 hover:text-white transition-colors"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Cluster Description */}
            <div>
              <Label htmlFor="cluster-description" className="text-white text-sm">
                Description
              </Label>
              <Textarea
                id="cluster-description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe the purpose of this cluster..."
                className="mt-1 bg-white/10 border-white/20 text-white placeholder-gray-400 resize-none"
                rows={3}
              />
            </div>

            {/* Color Selection */}
            <div>
              <Label className="text-white text-sm">Cluster Color</Label>
              <div className="grid grid-cols-4 gap-2 mt-2">
                {predefinedColors.map((color, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedColor(color.value)}
                    className={`w-full h-8 rounded border-2 transition-all ${
                      selectedColor === color.value 
                        ? 'border-white scale-110' 
                        : 'border-white/20 hover:border-white/40'
                    }`}
                    style={{ backgroundColor: `#${color.value.toString(16).padStart(6, '0')}` }}
                    title={color.name}
                  />
                ))}
              </div>
            </div>

            {/* Selected Nodes Preview */}
            {!existingCluster && selectedNodes.length > 0 && (
              <div>
                <Label className="text-white text-sm">Selected Nodes ({selectedNodes.length})</Label>
                <div className="mt-2 max-h-32 overflow-y-auto space-y-1">
                  {selectedNodes.map((node, index) => (
                    <div key={index} className="flex items-center justify-between bg-white/5 rounded px-2 py-1">
                      <span className="text-gray-300 text-sm truncate">
                        {node.data.label || node.id}
                      </span>
                      {node.data.analysisRecord && (
                        <Badge variant="outline" className="text-xs">
                          {node.data.analysisRecord.analysisType}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          {existingCluster && !isEditing ? (
            <>
              <Button
                onClick={() => setIsEditing(true)}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Cluster
              </Button>
              <Button
                onClick={handleDelete}
                variant="destructive"
                size="sm"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </>
          ) : (
            <>
              <Button
                onClick={handleSave}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              >
                <Save className="w-4 h-4 mr-2" />
                {existingCluster ? 'Update' : 'Create'} Cluster
              </Button>
              {existingCluster && (
                <Button
                  onClick={() => setIsEditing(false)}
                  variant="outline"
                  className="border-white/20 text-white hover:bg-white/10"
                >
                  Cancel
                </Button>
              )}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
