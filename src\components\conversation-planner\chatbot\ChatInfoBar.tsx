
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { User } from "lucide-react";
import { AnalysisResult, CharacterPersona } from "@/types/conversation";

interface ChatInfoBarProps {
  initialAnalysis: AnalysisResult;
  selectedModel: string;
  selectedPersona: CharacterPersona | null;
}

export const ChatInfoBar: React.FC<ChatInfoBarProps> = ({
  initialAnalysis,
  selectedModel,
  selectedPersona
}) => {
  return (
    <div className="flex items-center gap-3 flex-wrap">
      <Badge
        variant="outline"
        className="bg-white/20 text-white border-white/30 hover:bg-white/30 font-medium"
      >
        {initialAnalysis.style}
      </Badge>
      <Badge
        variant="outline"
        className="bg-white/20 text-white border-white/30 hover:bg-white/30 font-medium"
      >
        {selectedModel}
      </Badge>
      {selectedPersona && (
        <Badge
          variant="secondary"
          className="flex items-center gap-1 bg-white/20 text-white border-white/30 hover:bg-white/30 font-medium"
        >
          <User className="h-3 w-3" />
          {selectedPersona.name}
        </Badge>
      )}
    </div>
  );
};
