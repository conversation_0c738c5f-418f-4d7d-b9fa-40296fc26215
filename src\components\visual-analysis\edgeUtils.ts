import * as THREE from 'three';
// @ts-ignore
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial';

// Utility functions for advanced edge styling

export function createCurvedLineGeometry(
  start: THREE.Vector3,
  end: THREE.Vector3,
  curvature: number = 0.2,
  segments: number = 20
): THREE.BufferGeometry {
  const curve = new THREE.QuadraticBezierCurve3(
    start,
    new THREE.Vector3(
      (start.x + end.x) / 2 + (start.y - end.y) * curvature,
      (start.y + end.y) / 2 + (end.x - start.x) * curvature,
      (start.z + end.z) / 2
    ),
    end
  );
  
  const points = curve.getPoints(segments);
  const geometry = new THREE.BufferGeometry().setFromPoints(points);
  return geometry;
}

export function createFlowingParticleEffect(
  start: THREE.Vector3,
  end: THREE.Vector3,
  particleCount: number = 5
): THREE.Group {
  const group = new THREE.Group();
  
  for (let i = 0; i < particleCount; i++) {
    const particle = new THREE.Mesh(
      new THREE.CircleGeometry(0.05, 8),
      new THREE.MeshBasicMaterial({ 
        color: 0x00ffff, 
        transparent: true, 
        opacity: 0.8 
      })
    );
    
    // Position particles along the line
    const t = i / particleCount;
    particle.position.lerpVectors(start, end, t);
    particle.userData = { t, speed: 0.01 + Math.random() * 0.02 };
    
    group.add(particle);
  }
  
  return group;
}

export function getEdgeColorByStrength(strength: number): THREE.Color {
  // Create a color gradient from weak (blue) to strong (red)
  const hue = (1 - Math.min(Math.max(strength, 0), 1)) * 0.6; // 0.6 = blue, 0 = red
  return new THREE.Color().setHSL(hue, 0.8, 0.6);
}

export function getEdgeWidthByStrength(strength: number, baseWidth: number = 2): number {
  return baseWidth + (strength * 4); // Scale width based on strength
}

// Create animated dashed line effect
export function createAnimatedDashedMaterial(color: THREE.Color, dashSize: number = 1, gapSize: number = 0.5): LineMaterial {
  return new LineMaterial({
    color: color,
    linewidth: 2,
    dashed: true,
    dashSize: dashSize,
    gapSize: gapSize,
    opacity: 0.8,
    transparent: true,
  });
}

// Update animated dash offset for flowing effect
export function updateDashAnimation(material: LineMaterial, deltaTime: number, speed: number = 1): void {
  if (material.dashed && material.uniforms?.dashOffset) {
    material.uniforms.dashOffset.value -= deltaTime * speed;
  }
}
