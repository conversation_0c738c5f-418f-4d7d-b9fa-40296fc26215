import React, { useState } from 'react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Search, Filter, X } from 'lucide-react';
import { SimpleNode, VisualConnection } from './types';

interface FilterSearchPanelProps {
  nodes: Map<string, SimpleNode>;
  connections: Map<string, VisualConnection>;
  onSearchResult: (nodeIds: string[]) => void;
  onFilterApply: (filteredNodes: string[]) => void;
  onClearFilters: () => void;
  isVisible: boolean;
  onToggle: () => void;
}

export const FilterSearchPanel: React.FC<FilterSearchPanelProps> = ({
  nodes,
  connections,
  onSearchResult,
  onFilterApply,
  onClearFilters,
  isVisible,
  onToggle
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set());
  const [selectedTags, setSelectedTags] = useState<Set<string>>(new Set());
  const [minValue, setMinValue] = useState<number | ''>('');
  const [maxValue, setMaxValue] = useState<number | ''>('');

  // Extract unique categories and tags from nodes
  const categories = new Set<string>();
  const tags = new Set<string>();
  
  nodes.forEach(node => {
    if (node.data.category) categories.add(node.data.category);
    if (node.data.tags) {
      node.data.tags.forEach(tag => tags.add(tag));
    }
  });

  const handleSearch = () => {
    if (!searchTerm.trim()) {
      onSearchResult([]);
      return;
    }

    const searchResults: string[] = [];
    const term = searchTerm.toLowerCase();

    nodes.forEach((node, nodeId) => {
      const label = node.data.label?.toLowerCase() || '';
      const id = nodeId.toLowerCase();
      const category = node.data.category?.toLowerCase() || '';
      const tags = node.data.tags?.map(t => t.toLowerCase()).join(' ') || '';

      if (label.includes(term) || id.includes(term) || category.includes(term) || tags.includes(term)) {
        searchResults.push(nodeId);
      }
    });

    onSearchResult(searchResults);
  };

  const handleFilter = () => {
    const filteredNodeIds: string[] = [];

    nodes.forEach((node, nodeId) => {
      let passesFilter = true;

      // Category filter
      if (selectedCategories.size > 0) {
        if (!node.data.category || !selectedCategories.has(node.data.category)) {
          passesFilter = false;
        }
      }

      // Tags filter
      if (selectedTags.size > 0 && passesFilter) {
        if (!node.data.tags || !node.data.tags.some(tag => selectedTags.has(tag))) {
          passesFilter = false;
        }
      }

      // Value range filter
      if (passesFilter && (minValue !== '' || maxValue !== '')) {
        const nodeValue = node.data.value;
        if (nodeValue === undefined) {
          passesFilter = false;
        } else {
          if (minValue !== '' && nodeValue < minValue) passesFilter = false;
          if (maxValue !== '' && nodeValue > maxValue) passesFilter = false;
        }
      }

      if (passesFilter) {
        filteredNodeIds.push(nodeId);
      }
    });

    onFilterApply(filteredNodeIds);
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedCategories(new Set());
    setSelectedTags(new Set());
    setMinValue('');
    setMaxValue('');
    onClearFilters();
  };

  const toggleCategory = (category: string) => {
    const newSelected = new Set(selectedCategories);
    if (newSelected.has(category)) {
      newSelected.delete(category);
    } else {
      newSelected.add(category);
    }
    setSelectedCategories(newSelected);
  };

  const toggleTag = (tag: string) => {
    const newSelected = new Set(selectedTags);
    if (newSelected.has(tag)) {
      newSelected.delete(tag);
    } else {
      newSelected.add(tag);
    }
    setSelectedTags(newSelected);
  };
  if (!isVisible) {
    return (      <Button
        onClick={onToggle}
        className="fixed top-4 left-4 z-10"
        size="sm"
        variant="outline"
      >
        <Search className="h-4 w-4 mr-2" />
        Search & Filter
      </Button>
    );
  }

  return (
    <Card className="fixed top-4 left-4 z-10 w-80 max-h-96 overflow-y-auto bg-gray-900 border-gray-700">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-white text-sm flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Search & Filter
          </CardTitle>
          <Button onClick={onToggle} size="sm" variant="ghost">
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4 text-sm">
        {/* Search */}
        <div>
          <label className="text-gray-300 block mb-1">Search</label>
          <div className="flex gap-2">
            <Input
              placeholder="Search nodes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-gray-800 border-gray-600 text-white"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch} size="sm">
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Separator className="bg-gray-600" />

        {/* Categories */}
        {categories.size > 0 && (
          <div>
            <label className="text-gray-300 block mb-2">Categories</label>
            <div className="flex flex-wrap gap-1">
              {Array.from(categories).map(category => (
                <Badge
                  key={category}
                  variant={selectedCategories.has(category) ? "default" : "outline"}
                  className="cursor-pointer text-xs"
                  onClick={() => toggleCategory(category)}
                >
                  {category}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Tags */}
        {tags.size > 0 && (
          <div>
            <label className="text-gray-300 block mb-2">Tags</label>
            <div className="flex flex-wrap gap-1">
              {Array.from(tags).map(tag => (
                <Badge
                  key={tag}
                  variant={selectedTags.has(tag) ? "default" : "outline"}
                  className="cursor-pointer text-xs"
                  onClick={() => toggleTag(tag)}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Value Range */}
        <div>
          <label className="text-gray-300 block mb-2">Value Range</label>
          <div className="flex gap-2 items-center">
            <Input
              type="number"
              placeholder="Min"
              value={minValue}
              onChange={(e) => setMinValue(e.target.value ? Number(e.target.value) : '')}
              className="bg-gray-800 border-gray-600 text-white text-xs"
            />
            <span className="text-gray-400">to</span>
            <Input
              type="number"
              placeholder="Max"
              value={maxValue}
              onChange={(e) => setMaxValue(e.target.value ? Number(e.target.value) : '')}
              className="bg-gray-800 border-gray-600 text-white text-xs"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button onClick={handleFilter} size="sm" className="flex-1">
            Apply Filter
          </Button>
          <Button onClick={clearAllFilters} size="sm" variant="outline" className="flex-1">
            Clear All
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
