
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";
import { QuestionContext } from "@/components/conversation-planner/QuestionContextSelector";
import { formatInlineContent } from "./ContentFormatter";
import { Checkbox } from "@/components/ui/checkbox";

// Discriminated union for better type safety
type FollowUpSectionProps = {
  questionContext: QuestionContext;
  followUpQuestions: string[];
  onFollowUpQuestion: (question: string) => void;
} & (
  | {
      // When selection is enabled, both props are required
      selectionEnabled: true;
      onItemToggleSelect: (question: string) => void;
      isItemSelected: (question: string) => boolean;
    }
  | {
      // When selection is disabled, props are not needed
      selectionEnabled?: false;
      onItemToggleSelect?: never;
      isItemSelected?: never;
    }
);

export const FollowUpSection: React.FC<FollowUpSectionProps> = ({
  questionContext,
  followUpQuestions,
  onFollowUpQuestion,
  selectionEnabled = false,
  onItemToggleSelect,
  isItemSelected,
}) => {
  // Only show the section if we have actual follow-up questions
  if (!followUpQuestions || followUpQuestions.length === 0) {
    return null;
  }

  const getFollowUpTitle = () => {
    return questionContext === "asking"
      ? "Keep the Conversation Going"
      : "Redirect or Continue the Conversation";
  };

  const getFollowUpDescription = () => {
    return questionContext === "asking"
      ? "Use these AI-generated follow-up questions to continue the conversation:"
      : "Use these AI-generated questions to redirect or learn more about their intent:";
  };

  return (
    <div className="bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-200 p-8 rounded-xl shadow-sm">
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
          <MessageSquare className="h-5 w-5 text-purple-600" />
        </div>
        <div>
          <h4 className="text-xl font-bold text-slate-800">
            {getFollowUpTitle()}
          </h4>
          <p className="text-sm text-slate-600 mt-1">
            {getFollowUpDescription()}
          </p>
        </div>
      </div>
      <div className="space-y-4">
        {followUpQuestions.map((question, index) => (
          <div key={index} className="flex items-start gap-4">
            {selectionEnabled && onItemToggleSelect && isItemSelected && (
              <Checkbox
                checked={isItemSelected(question)}
                onCheckedChange={() => onItemToggleSelect(question)}
                className="mt-2"
              />
            )}
            <Button
              variant="outline"
              onClick={() => onFollowUpQuestion(question)}
              className="flex-1 text-left p-5 h-auto bg-white border-slate-200 hover:bg-slate-50 hover:border-purple-300 justify-start group transition-all duration-200 shadow-sm"
            >
              <div className="flex items-start w-full">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 mt-0.5 group-hover:bg-purple-200 transition-colors">
                  <span className="text-purple-700 font-bold text-sm">{index + 1}</span>
                </div>
                <span
                  className="text-sm text-slate-700 text-left break-words whitespace-normal leading-relaxed font-medium"
                  dangerouslySetInnerHTML={{ __html: formatInlineContent(question) }}
                />
              </div>
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
