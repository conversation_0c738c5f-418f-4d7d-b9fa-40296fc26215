import * as THREE from 'three';

export const createLights = (scene: THREE.Scene): void => {
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 15, 10);
  directionalLight.castShadow = true; // Enable shadows for this light
  // Configure shadow properties for better quality/performance
  directionalLight.shadow.mapSize.width = 1024;
  directionalLight.shadow.mapSize.height = 1024;
  directionalLight.shadow.camera.near = 0.5;
  directionalLight.shadow.camera.far = 50;
  scene.add(directionalLight);
};

export const createCamera = (mountElement: HTMLElement): THREE.PerspectiveCamera => {
  const camera = new THREE.PerspectiveCamera(
    75,
    mountElement.clientWidth / mountElement.clientHeight,
    0.1,
    1000
  );
  camera.position.set(0, 5, 15); // Adjusted initial camera position
  return camera;
};

export const createRenderer = (mountElement: HTMLElement): THREE.WebGLRenderer => {
  const renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(mountElement.clientWidth, mountElement.clientHeight);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.setClearColor(0x1a1a2e); // Dark blue/purpleish background
  renderer.shadowMap.enabled = true; // Enable shadow mapping in the renderer
  renderer.shadowMap.type = THREE.PCFSoftShadowMap; // Softer shadows
  mountElement.appendChild(renderer.domElement);
  return renderer;
};

export const createGroundPlane = (): THREE.Mesh => {
  const planeGeometry = new THREE.PlaneGeometry(50, 50);
  const planeMaterial = new THREE.MeshStandardMaterial({
    color: 0x333344, // Darker ground
    side: THREE.DoubleSide,
    roughness: 0.8,
    metalness: 0.2,
  });
  const groundPlane = new THREE.Mesh(planeGeometry, planeMaterial);
  groundPlane.rotation.x = -Math.PI / 2; // Rotate to be horizontal
  groundPlane.receiveShadow = true; // Ground should receive shadows
  return groundPlane;
};
