# Living Data Canvas - Complete Documentation

## Overview

The Living Data Canvas is an advanced, interactive visualization platform designed specifically for analyzing and exploring chat analysis records. It transforms static analysis data into a dynamic, explorable 3D environment where users can discover patterns, create connections, and generate insights through visual interaction.

## Key Features

### 🎯 Core Functionality
- **Interactive 3D Visualization**: Real-time rendering of analysis records as interactive nodes
- **Dynamic Connections**: Visual representation of relationships between analyses
- **Cluster Management**: Group related analyses for better organization
- **Chat Simulations**: Test scenarios and generate new insights
- **Real-time Updates**: Live synchronization with analysis data

### 🚀 Advanced Features
- **Performance Optimization**: Web workers, level-of-detail rendering, and memory management
- **Accessibility Support**: Full keyboard navigation, screen reader support, and customizable UI
- **Advanced Controls**: Comprehensive UI controls for customization and configuration
- **Contextual Help**: Built-in help system with searchable documentation
- **Keyboard Shortcuts**: Extensive keyboard shortcuts for power users

## Architecture

### Component Structure
```
LivingDataCanvas/
├── Core Components/
│   ├── LivingDataCanvas.tsx          # Main canvas component
│   ├── ChatAnalysisNodeMenu.tsx     # Contextual node menus
│   ├── NodeConnectionManager.tsx    # Connection management
│   ├── ClusterManager.tsx           # Cluster creation and management
│   └── ChatSimulationManager.tsx    # Simulation framework
├── Advanced UI/
│   ├── AdvancedControlPanel.tsx     # Advanced controls
│   ├── KeyboardShortcutsManager.tsx # Keyboard shortcuts
│   ├── ContextualHelpSystem.tsx     # Help system
│   └── EnhancedTooltip.tsx          # Enhanced tooltips
├── Performance/
│   ├── performanceProfiler.ts       # Performance monitoring
│   ├── canvasOptimization.ts        # Canvas optimizations
│   └── dataOptimization.ts          # Data processing optimizations
└── Workers/
    └── dataProcessingWorker.ts      # Web worker for heavy processing
```

### Data Flow
1. **Data Input**: Analysis results from various sources (API, local storage, real-time)
2. **Processing**: Data transformation, similarity calculation, and optimization
3. **Visualization**: 3D rendering with Three.js and interactive controls
4. **User Interaction**: Node selection, connection creation, clustering, simulations
5. **Output**: Insights, exports, and new analysis generation

## Getting Started

### Basic Usage

```tsx
import { LivingDataCanvas } from '@/components/visual-analysis/LivingDataCanvas';
import { AnalysisResult } from '@/types/conversation';

const analysisResults: AnalysisResult[] = [
  {
    id: '1',
    question: 'What is the main topic?',
    analysis: 'The main topic is artificial intelligence.',
    analysisType: 'multiple',
    model: 'gpt-4',
    timestamp: new Date().toISOString(),
    rating: 5,
    metadata: { tokens: 100, processingTime: 1000 }
  }
  // ... more results
];

function MyApp() {
  return (
    <LivingDataCanvas 
      analysisResults={analysisResults}
      enableChatAnalysisIntegration={true}
      enableRealtimeUpdates={true}
    />
  );
}
```

### Configuration Options

```tsx
interface LivingDataCanvasProps {
  analysisResults: AnalysisResult[];
  enableChatAnalysisIntegration?: boolean;
  enableRealtimeUpdates?: boolean;
  initialData?: CanvasData;
  onNodeSelect?: (nodeId: string) => void;
  onClusterCreate?: (cluster: ClusterData) => void;
  onSimulationComplete?: (results: SimulationResults) => void;
}
```

## User Guide

### Navigation
- **Pan**: Left mouse drag or arrow keys
- **Zoom**: Mouse wheel or Ctrl + +/-
- **Select**: Click on nodes
- **Multi-select**: Ctrl + click
- **Context Menu**: Right-click on nodes

### Keyboard Shortcuts
- `C`: Create cluster from selected nodes
- `S`: Create simulation from selected nodes
- `Escape`: Clear selections and cancel operations
- `Ctrl+Shift+?`: Show keyboard shortcuts panel
- `Ctrl+Shift+C`: Toggle advanced controls
- `F1`: Toggle help system

### Node Interactions
1. **Single Selection**: Click any node to view details
2. **Multi-Selection**: Hold Ctrl and click multiple nodes
3. **Connection Creation**: Ctrl+Shift+click source, then click target
4. **Context Actions**: Right-click for available actions

### Cluster Management
1. Select multiple nodes (Ctrl+click)
2. Press `C` or use context menu
3. Configure cluster properties
4. Create and manage cluster relationships

### Chat Simulations
1. Select source nodes for simulation
2. Press `S` or use context menu
3. Configure simulation parameters
4. Run simulation and view results

## Advanced Features

### Performance Optimization

The canvas includes several performance optimizations:

- **Level of Detail (LOD)**: Automatic quality adjustment based on zoom level
- **Web Workers**: Heavy processing offloaded to background threads
- **Memory Management**: Efficient object pooling and cleanup
- **Viewport Culling**: Only render visible elements
- **Batch Processing**: Group operations for better performance

### Accessibility Features

- **Keyboard Navigation**: Full keyboard support for all features
- **Screen Reader Support**: ARIA labels and semantic markup
- **High Contrast Mode**: Enhanced visibility options
- **Reduced Motion**: Respect user motion preferences
- **Font Size Control**: Adjustable text sizing

### Real-time Updates

The canvas supports real-time updates through:

- **WebSocket Integration**: Live data synchronization
- **Incremental Updates**: Efficient partial data updates
- **Conflict Resolution**: Handle concurrent modifications
- **Offline Support**: Continue working without connection

## API Reference

### Core Methods

#### LivingDataCanvas Component

```tsx
// Node selection
onNodeSelect?: (nodeId: string, analysisResult?: AnalysisResult) => void;

// Cluster operations
onClusterCreate?: (cluster: ClusterData) => void;
onClusterUpdate?: (clusterId: string, updates: Partial<ClusterData>) => void;
onClusterDelete?: (clusterId: string) => void;

// Simulation operations
onSimulationCreate?: (simulation: SimulationData) => void;
onSimulationRun?: (simulationId: string) => void;
onSimulationComplete?: (simulationId: string, results: SimulationResults) => void;

// Data operations
onDataExport?: (format: 'json' | 'csv' | 'png' | 'svg') => void;
onDataImport?: (data: CanvasData) => void;
```

### Hooks

#### useChatAnalysisCanvas

```tsx
const {
  canvasData,
  isLoading,
  error,
  clusters,
  simulations,
  loadCanvasData,
  createCluster,
  updateCluster,
  deleteCluster,
  createSimulation,
  runSimulation
} = useChatAnalysisCanvas();
```

#### useDataProcessingWorker

```tsx
const {
  processAnalysisResults,
  calculateSimilarities,
  calculateForceDirectedLayout,
  isWorkerAvailable
} = useDataProcessingWorker();
```

### Performance Monitoring

```tsx
import { performanceProfiler } from '@/services/performance/performanceProfiler';

// Start timing
performanceProfiler.startTiming('operation-name');

// End timing
const duration = performanceProfiler.endTiming('operation-name');

// Record custom metrics
performanceProfiler.recordMetric('custom-metric', { value: 42 });

// Get performance summary
const summary = performanceProfiler.getPerformanceSummary();
```

## Customization

### Themes

The canvas supports multiple themes:

- **Dark**: Default dark theme with blue accents
- **Light**: Light theme with high contrast
- **High Contrast**: Enhanced accessibility theme
- **Custom**: Define your own color schemes

### Layout Algorithms

Available layout algorithms:

- **Force-Directed**: Physics-based node positioning
- **Circular**: Nodes arranged in circles
- **Grid**: Regular grid layout
- **Hierarchical**: Tree-like structure
- **Custom**: Implement your own algorithm

### Visual Customization

```tsx
// Node appearance
const nodeStyle = {
  size: 'dynamic', // 'small', 'medium', 'large', 'dynamic'
  color: 'category', // 'category', 'rating', 'model', 'custom'
  shape: 'circle', // 'circle', 'square', 'diamond'
  opacity: 0.9
};

// Connection appearance
const connectionStyle = {
  width: 'strength', // 'thin', 'medium', 'thick', 'strength'
  color: 'type', // 'type', 'strength', 'custom'
  style: 'curved' // 'straight', 'curved', 'bezier'
};
```

## Troubleshooting

### Common Issues

1. **Performance Issues**
   - Enable performance optimizations in advanced controls
   - Reduce particle count in background effects
   - Use level-of-detail rendering for large datasets

2. **Memory Issues**
   - Clear old data periodically
   - Enable object pooling
   - Monitor memory usage in performance panel

3. **Rendering Issues**
   - Check WebGL support
   - Update graphics drivers
   - Reduce visual complexity

### Debug Mode

Enable debug mode for additional information:

```tsx
<LivingDataCanvas 
  debugMode={true}
  showPerformanceMetrics={true}
  logLevel="verbose"
/>
```

## Contributing

### Development Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Start development server: `npm run dev`
4. Run tests: `npm test`

### Testing

- **Unit Tests**: `npm run test:unit`
- **Integration Tests**: `npm run test:integration`
- **Performance Tests**: `npm run test:performance`
- **E2E Tests**: `npm run test:e2e`

### Code Style

- Follow TypeScript best practices
- Use ESLint and Prettier for formatting
- Write comprehensive tests for new features
- Document all public APIs

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Performance Benchmarks

### Recommended System Requirements

**Minimum Requirements:**
- 4GB RAM
- Modern browser with WebGL support
- 1000 analysis records: < 2 seconds load time
- 30 FPS rendering with 500 nodes

**Recommended Requirements:**
- 8GB RAM
- Dedicated graphics card
- 5000 analysis records: < 5 seconds load time
- 60 FPS rendering with 1000+ nodes

### Performance Metrics

| Dataset Size | Load Time | Memory Usage | Rendering FPS |
|-------------|-----------|--------------|---------------|
| 100 records | < 0.5s    | < 50MB       | 60 FPS        |
| 500 records | < 1s      | < 100MB      | 60 FPS        |
| 1000 records| < 2s      | < 200MB      | 45+ FPS       |
| 5000 records| < 5s      | < 500MB      | 30+ FPS       |

## Support

For support and questions:

- GitHub Issues: Report bugs and feature requests
- Documentation: Check this guide and inline help
- Community: Join our Discord server
- Email: <EMAIL>
