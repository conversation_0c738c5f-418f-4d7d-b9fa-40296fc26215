import { useEffect } from 'react';
import React from 'react';

interface PerformanceMetrics {
  chunkName: string;
  loadTime: number;
  timestamp: number;
}

/**
 * Performance monitoring hook for tracking chunk load times
 * Useful for monitoring the effectiveness of lazy loading optimizations
 */
export const usePerformanceMonitor = (chunkName: string) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      // Log performance metrics (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 [Performance] ${chunkName} loaded in ${loadTime.toFixed(2)}ms`);
        
        // Store metrics for potential analytics
        const metrics: PerformanceMetrics = {
          chunkName,
          loadTime,
          timestamp: Date.now()
        };
        
        // You could send this to analytics service in production
        // analytics.track('chunk_load_time', metrics);
      }
    };
  }, [chunkName]);
};

/**
 * Higher-order component for measuring component load times
 */
export const withPerformanceMonitor = <P extends object>(
  Component: React.ComponentType<P>,
  chunkName: string
): React.FC<P> => {
  const WrappedComponent: React.FC<P> = (props: P) => {
    usePerformanceMonitor(chunkName);
    return React.createElement(Component, props);
  };
  
  WrappedComponent.displayName = `withPerformanceMonitor(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

/**
 * Navigation timing metrics
 */
export const getNavigationMetrics = () => {
  if (typeof window === 'undefined') return null;
  
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  
  return {
    // Time to first byte
    ttfb: navigation.responseStart - navigation.fetchStart,
    
    // DOM content loaded
    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
    
    // Full page load
    pageLoad: navigation.loadEventEnd - navigation.fetchStart,
    
    // DNS lookup time
    dnsTime: navigation.domainLookupEnd - navigation.domainLookupStart,
    
    // Connection time
    connectionTime: navigation.connectEnd - navigation.connectStart,
  };
};

/**
 * Log bundle performance summary (call this in main app)
 */
export const logBundlePerformance = () => {
  if (process.env.NODE_ENV !== 'development') return;
  
  setTimeout(() => {
    const metrics = getNavigationMetrics();
    if (metrics) {
      console.group('📊 Bundle Performance Metrics');
      console.log(`⚡ TTFB: ${metrics.ttfb.toFixed(2)}ms`);
      console.log(`📄 DOM Content Loaded: ${metrics.domContentLoaded.toFixed(2)}ms`);
      console.log(`🎯 Page Load Complete: ${metrics.pageLoad.toFixed(2)}ms`);
      console.log(`🌐 DNS Lookup: ${metrics.dnsTime.toFixed(2)}ms`);
      console.log(`🔗 Connection: ${metrics.connectionTime.toFixed(2)}ms`);
      console.groupEnd();
    }
  }, 3000); // Wait 3 seconds after initial load
};
