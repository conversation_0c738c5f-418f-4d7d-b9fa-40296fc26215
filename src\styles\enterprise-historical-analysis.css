/* Fortune 500 Enterprise-Grade Historical Analysis Styling */

:root {
  /* Enterprise Color Palette */
  --enterprise-primary: #0066cc;
  --enterprise-primary-light: #3388dd;
  --enterprise-primary-dark: #004499;
  --enterprise-secondary: #f8fafc;
  --enterprise-text-primary: #1a202c;
  --enterprise-text-secondary: #4a5568;
  --enterprise-text-muted: #718096;
  --enterprise-border-light: #e2e8f0;
  --enterprise-border-medium: #cbd5e0;
  --enterprise-success: #10b981;
  --enterprise-warning: #f59e0b;
  --enterprise-error: #ef4444;
  --enterprise-surface-white: #ffffff;
  --enterprise-surface-gray: #f7fafc;
  --enterprise-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --enterprise-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --enterprise-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
  --enterprise-shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
  --enterprise-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --enterprise-gradient-surface: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

/* Main Container */
.enterprise-historical-analysis {
  background: var(--enterprise-surface-white);
  border-radius: 16px;
  padding: 32px;
  margin: 24px 0;
  box-shadow: var(--enterprise-shadow-lg);
  border: 1px solid var(--enterprise-border-light);
  position: relative;
  overflow: hidden;
}

.enterprise-historical-analysis::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--enterprise-gradient-primary);
}

/* Header Section */
.enterprise-analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--enterprise-border-light);
}

.enterprise-analysis-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--enterprise-text-primary);
  margin: 0;
  letter-spacing: -0.5px;
}

.enterprise-analysis-subtitle {
  font-size: 16px;
  color: var(--enterprise-text-secondary);
  margin: 4px 0 0 0;
  font-weight: 400;
}

.enterprise-analysis-actions {
  display: flex;
  gap: 12px;
}

.enterprise-action-button {
  background: var(--enterprise-surface-white);
  border: 1px solid var(--enterprise-border-medium);
  color: var(--enterprise-text-secondary);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.enterprise-action-button:hover {
  background: var(--enterprise-secondary);
  border-color: var(--enterprise-primary);
  color: var(--enterprise-primary);
  transform: translateY(-1px);
  box-shadow: var(--enterprise-shadow-md);
}

.enterprise-action-button.primary {
  background: var(--enterprise-primary);
  color: white;
  border-color: var(--enterprise-primary);
}

.enterprise-action-button.primary:hover {
  background: var(--enterprise-primary-dark);
  transform: translateY(-2px);
}

/* Enhanced Visual Canvas */
.enterprise-visual-canvas {
  background: var(--enterprise-gradient-surface);
  border: 1px solid var(--enterprise-border-light);
  border-radius: 12px;
  padding: 24px;
  margin: 24px 0;
  box-shadow: var(--enterprise-shadow-sm);
  position: relative;
  min-height: 500px;
  overflow: hidden;
}

.enterprise-canvas-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
  height: 100%;
}

/* Professional Chart Container */
.enterprise-chart-container {
  background: var(--enterprise-surface-white);
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--enterprise-shadow-sm);
  border: 1px solid var(--enterprise-border-light);
  position: relative;
  overflow: hidden;
}

.enterprise-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--enterprise-border-light);
}

.enterprise-chart-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--enterprise-text-primary);
  margin: 0;
}

.enterprise-chart-controls {
  display: flex;
  gap: 8px;
}

.enterprise-chart-control {
  background: var(--enterprise-surface-gray);
  border: 1px solid var(--enterprise-border-light);
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  color: var(--enterprise-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.enterprise-chart-control:hover,
.enterprise-chart-control.active {
  background: var(--enterprise-primary);
  color: white;
  border-color: var(--enterprise-primary);
}

/* Advanced SVG Styling */
.enterprise-chart-svg {
  width: 100%;
  height: 350px;
  overflow: visible;
}

.enterprise-chart-grid-line {
  stroke: var(--enterprise-border-light);
  stroke-width: 1;
  stroke-dasharray: 2,2;
  opacity: 0.6;
}

.enterprise-chart-axis {
  stroke: var(--enterprise-border-medium);
  stroke-width: 1;
}

.enterprise-chart-axis-label {
  fill: var(--enterprise-text-muted);
  font-size: 12px;
  font-weight: 500;
}

.enterprise-chart-line {
  stroke: var(--enterprise-primary);
  stroke-width: 3;
  fill: none;
  filter: drop-shadow(0 2px 4px rgba(0, 102, 204, 0.2));
  stroke-linecap: round;
  stroke-linejoin: round;
}

.enterprise-chart-area {
  fill: url(#enterpriseAreaGradient);
  opacity: 0.6;
}

.enterprise-chart-point {
  fill: var(--enterprise-primary);
  stroke: var(--enterprise-surface-white);
  stroke-width: 3;
  r: 5;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.enterprise-chart-point:hover {
  fill: var(--enterprise-primary-dark);
  r: 7;
  stroke-width: 4;
  filter: drop-shadow(0 4px 8px rgba(0, 102, 204, 0.3));
}

.enterprise-chart-point.active {
  fill: var(--enterprise-success);
  stroke: var(--enterprise-surface-white);
  r: 6;
  animation: enterprise-pulse 2s infinite;
}

@keyframes enterprise-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Enterprise Data Cards */
.enterprise-data-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin: 24px 0;
}

.enterprise-data-card {
  background: var(--enterprise-surface-white);
  border: 1px solid var(--enterprise-border-light);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enterprise-data-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--enterprise-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.enterprise-data-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--enterprise-shadow-lg);
  border-color: var(--enterprise-primary-light);
}

.enterprise-data-card:hover::before {
  transform: scaleX(1);
}

.enterprise-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.enterprise-card-icon {
  width: 48px;
  height: 48px;
  background: var(--enterprise-gradient-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: var(--enterprise-shadow-sm);
}

.enterprise-card-menu {
  color: var(--enterprise-text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.enterprise-card-menu:hover {
  background: var(--enterprise-secondary);
  color: var(--enterprise-text-secondary);
}

.enterprise-card-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--enterprise-text-primary);
  margin: 0;
  line-height: 1.2;
}

.enterprise-card-label {
  font-size: 14px;
  color: var(--enterprise-text-secondary);
  font-weight: 500;
  margin: 4px 0 8px 0;
}

.enterprise-card-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
}

.enterprise-card-change.positive {
  color: var(--enterprise-success);
}

.enterprise-card-change.negative {
  color: var(--enterprise-error);
}

.enterprise-card-change.neutral {
  color: var(--enterprise-text-muted);
}

/* Professional Timeline */
.enterprise-timeline-container {
  background: var(--enterprise-surface-white);
  border: 1px solid var(--enterprise-border-light);
  border-radius: 12px;
  padding: 24px;
  position: relative;
}

.enterprise-timeline-header {
  margin-bottom: 24px;
}

.enterprise-timeline-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--enterprise-text-primary);
  margin: 0 0 8px 0;
}

.enterprise-timeline-subtitle {
  font-size: 14px;
  color: var(--enterprise-text-secondary);
  margin: 0;
}

.enterprise-timeline-items {
  position: relative;
}

.enterprise-timeline-items::before {
  content: '';
  position: absolute;
  left: 24px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--enterprise-border-light);
}

.enterprise-timeline-item {
  position: relative;
  padding: 16px 0 16px 60px;
  margin-bottom: 20px;
}

.enterprise-timeline-item::before {
  content: '';
  position: absolute;
  left: 16px;
  top: 20px;
  width: 16px;
  height: 16px;
  background: var(--enterprise-primary);
  border: 3px solid var(--enterprise-surface-white);
  border-radius: 50%;
  box-shadow: 0 0 0 3px var(--enterprise-border-light);
}

.enterprise-timeline-item.important::before {
  background: var(--enterprise-success);
  animation: enterprise-pulse 2s infinite;
}

.enterprise-timeline-date {
  font-size: 12px;
  font-weight: 600;
  color: var(--enterprise-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.enterprise-timeline-title-item {
  font-size: 16px;
  font-weight: 600;
  color: var(--enterprise-text-primary);
  margin-bottom: 4px;
}

.enterprise-timeline-content {
  font-size: 14px;
  color: var(--enterprise-text-secondary);
  line-height: 1.5;
}

/* Enhanced Tooltips */
.enterprise-tooltip {
  position: fixed;
  background: rgba(26, 32, 44, 0.95);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  pointer-events: none;
  z-index: 1000;
  box-shadow: var(--enterprise-shadow-xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 250px;
  word-wrap: break-word;
}

.enterprise-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -6px;
  border-width: 6px;
  border-style: solid;
  border-color: rgba(26, 32, 44, 0.95) transparent transparent transparent;
}

/* Professional Legend */
.enterprise-legend {
  background: var(--enterprise-surface-white);
  border: 1px solid var(--enterprise-border-light);
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
}

.enterprise-legend-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--enterprise-text-primary);
  margin: 0 0 12px 0;
}

.enterprise-legend-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.enterprise-legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--enterprise-text-secondary);
}

.enterprise-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  flex-shrink: 0;
}

/* Loading States */
.enterprise-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.enterprise-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--enterprise-border-light);
  border-top: 3px solid var(--enterprise-primary);
  border-radius: 50%;
  animation: enterprise-spin 1s linear infinite;
}

@keyframes enterprise-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .enterprise-canvas-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .enterprise-historical-analysis {
    padding: 20px;
    margin: 16px 0;
  }
  
  .enterprise-analysis-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .enterprise-data-cards-grid {
    grid-template-columns: 1fr;
  }
  
  .enterprise-chart-svg {
    height: 250px;
  }
  
  .enterprise-timeline-item {
    padding-left: 40px;
  }
  
  .enterprise-timeline-items::before {
    left: 16px;
  }
  
  .enterprise-timeline-item::before {
    left: 8px;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  :root {
    --enterprise-surface-white: #1a202c;
    --enterprise-surface-gray: #2d3748;
    --enterprise-text-primary: #f7fafc;
    --enterprise-text-secondary: #e2e8f0;
    --enterprise-text-muted: #a0aec0;
    --enterprise-border-light: #4a5568;
    --enterprise-border-medium: #718096;
  }
}

/* Print Styles */
@media print {
  .enterprise-historical-analysis {
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .enterprise-action-button {
    display: none;
  }
  
  .enterprise-chart-svg {
    height: 400px;
  }
}
