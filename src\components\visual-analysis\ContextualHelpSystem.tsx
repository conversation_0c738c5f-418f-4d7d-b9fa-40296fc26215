import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs';
import { 
  HelpCircle, 
  X, 
  BookOpen, 
  Video, 
  MessageCircle, 
  ExternalLink,
  ChevronRight,
  ChevronDown,
  Search,
  Lightbulb,
  Target,
  Zap,
  Users,
  Settings,
  Database
} from 'lucide-react';

interface HelpTopic {
  id: string;
  title: string;
  description: string;
  category: string;
  content: React.ReactNode;
  keywords: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime?: string;
  relatedTopics?: string[];
}

interface ContextualHelpSystemProps {
  isVisible: boolean;
  onToggle: () => void;
  currentContext?: string;
  onTopicSelect?: (topicId: string) => void;
}

export const ContextualHelpSystem: React.FC<ContextualHelpSystemProps> = ({
  isVisible,
  onToggle,
  currentContext,
  onTopicSelect
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('getting-started');
  const [expandedTopics, setExpandedTopics] = useState<Set<string>>(new Set());
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);

  const helpTopics: HelpTopic[] = [
    {
      id: 'canvas-overview',
      title: 'Canvas Overview',
      description: 'Understanding the Living Data Canvas interface and basic concepts',
      category: 'getting-started',
      keywords: ['canvas', 'interface', 'overview', 'basics'],
      difficulty: 'beginner',
      estimatedTime: '5 min',
      content: (
        <div className="space-y-4">
          <p>The Living Data Canvas is an interactive visualization platform for chat analysis records.</p>
          <div className="bg-blue-600/20 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Key Components:</h4>
            <ul className="space-y-1 text-sm">
              <li>• <strong>Nodes:</strong> Represent individual analysis records</li>
              <li>• <strong>Connections:</strong> Show relationships between analyses</li>
              <li>• <strong>Clusters:</strong> Group related analyses together</li>
              <li>• <strong>Simulations:</strong> Test scenarios and generate insights</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'node-interactions',
      title: 'Working with Nodes',
      description: 'How to select, connect, and manage analysis record nodes',
      category: 'basic-operations',
      keywords: ['nodes', 'selection', 'interaction', 'analysis'],
      difficulty: 'beginner',
      estimatedTime: '3 min',
      content: (
        <div className="space-y-4">
          <h4 className="font-medium">Node Operations:</h4>
          <div className="grid gap-3">
            <div className="bg-gray-800 p-3 rounded">
              <strong>Single Selection:</strong> Click on any node to select it
            </div>
            <div className="bg-gray-800 p-3 rounded">
              <strong>Multi-Selection:</strong> Hold Ctrl and click multiple nodes
            </div>
            <div className="bg-gray-800 p-3 rounded">
              <strong>Context Menu:</strong> Right-click on nodes for available actions
            </div>
            <div className="bg-gray-800 p-3 rounded">
              <strong>Information Panel:</strong> Selected nodes show details in the side panel
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'creating-connections',
      title: 'Creating Connections',
      description: 'How to establish relationships between analysis records',
      category: 'basic-operations',
      keywords: ['connections', 'relationships', 'linking'],
      difficulty: 'intermediate',
      estimatedTime: '4 min',
      content: (
        <div className="space-y-4">
          <p>Connections help visualize relationships between different analyses.</p>
          <div className="space-y-3">
            <div className="bg-green-600/20 p-3 rounded">
              <strong>Method 1:</strong> Ctrl+Shift+Click on source node, then click target node
            </div>
            <div className="bg-green-600/20 p-3 rounded">
              <strong>Method 2:</strong> Use the connection manager from the context menu
            </div>
            <div className="bg-green-600/20 p-3 rounded">
              <strong>Auto-suggestions:</strong> The system suggests connections based on content similarity
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'cluster-management',
      title: 'Managing Clusters',
      description: 'Organizing related analyses into clusters for better visualization',
      category: 'advanced-features',
      keywords: ['clusters', 'organization', 'grouping'],
      difficulty: 'intermediate',
      estimatedTime: '6 min',
      content: (
        <div className="space-y-4">
          <p>Clusters help organize related analyses and provide visual grouping.</p>
          <div className="space-y-3">
            <div className="bg-purple-600/20 p-3 rounded">
              <strong>Creating:</strong> Select multiple nodes and press 'C' or use the cluster manager
            </div>
            <div className="bg-purple-600/20 p-3 rounded">
              <strong>Editing:</strong> Right-click on cluster boundaries to modify properties
            </div>
            <div className="bg-purple-600/20 p-3 rounded">
              <strong>Management:</strong> Add/remove nodes, change colors, and update descriptions
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'simulation-framework',
      title: 'Chat Simulations',
      description: 'Creating and running chat simulations for testing scenarios',
      category: 'advanced-features',
      keywords: ['simulations', 'testing', 'scenarios', 'chat'],
      difficulty: 'advanced',
      estimatedTime: '8 min',
      content: (
        <div className="space-y-4">
          <p>Simulations allow you to test different scenarios and generate new insights.</p>
          <div className="space-y-3">
            <div className="bg-orange-600/20 p-3 rounded">
              <strong>Setup:</strong> Select source nodes and press 'S' to open simulation manager
            </div>
            <div className="bg-orange-600/20 p-3 rounded">
              <strong>Prompts:</strong> Auto-generated or custom prompts for testing
            </div>
            <div className="bg-orange-600/20 p-3 rounded">
              <strong>Execution:</strong> Run simulations and view results in real-time
            </div>
            <div className="bg-orange-600/20 p-3 rounded">
              <strong>Analysis:</strong> Export results and generate insights from simulation data
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'performance-optimization',
      title: 'Performance Tips',
      description: 'Optimizing canvas performance for large datasets',
      category: 'troubleshooting',
      keywords: ['performance', 'optimization', 'large datasets'],
      difficulty: 'advanced',
      estimatedTime: '5 min',
      content: (
        <div className="space-y-4">
          <p>Tips for maintaining smooth performance with large datasets:</p>
          <div className="space-y-3">
            <div className="bg-red-600/20 p-3 rounded">
              <strong>Visibility Controls:</strong> Hide unnecessary elements to improve rendering
            </div>
            <div className="bg-red-600/20 p-3 rounded">
              <strong>Level of Detail:</strong> Automatic optimization reduces detail for distant nodes
            </div>
            <div className="bg-red-600/20 p-3 rounded">
              <strong>Filtering:</strong> Use filters to show only relevant data
            </div>
            <div className="bg-red-600/20 p-3 rounded">
              <strong>Clustering:</strong> Group nodes to reduce visual complexity
            </div>
          </div>
        </div>
      )
    }
  ];

  const categories = [
    { id: 'getting-started', name: 'Getting Started', icon: <Target className="w-4 h-4" /> },
    { id: 'basic-operations', name: 'Basic Operations', icon: <Zap className="w-4 h-4" /> },
    { id: 'advanced-features', name: 'Advanced Features', icon: <Settings className="w-4 h-4" /> },
    { id: 'troubleshooting', name: 'Troubleshooting', icon: <HelpCircle className="w-4 h-4" /> }
  ];

  const filteredTopics = helpTopics.filter(topic => {
    const matchesSearch = topic.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         topic.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         topic.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = activeCategory === 'all' || topic.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleTopicExpansion = (topicId: string) => {
    setExpandedTopics(prev => {
      const newSet = new Set(prev);
      if (newSet.has(topicId)) {
        newSet.delete(topicId);
      } else {
        newSet.add(topicId);
      }
      return newSet;
    });
  };

  const handleTopicSelect = (topicId: string) => {
    setSelectedTopic(topicId);
    onTopicSelect?.(topicId);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-600';
      case 'intermediate': return 'bg-yellow-600';
      case 'advanced': return 'bg-red-600';
      default: return 'bg-gray-600';
    }
  };

  if (!isVisible) {
    return (
      <Button
        onClick={onToggle}
        className="fixed bottom-4 left-4 z-20 bg-white/10 hover:bg-white/20 backdrop-blur-md border border-white/20"
        size="sm"
      >
        <HelpCircle className="w-4 h-4" />
      </Button>
    );
  }

  const selectedTopicData = selectedTopic ? helpTopics.find(t => t.id === selectedTopic) : null;

  return (
    <Card className="fixed inset-4 z-30 bg-black/90 backdrop-blur-md border-white/20 text-white overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center">
            <BookOpen className="w-5 h-5 mr-2" />
            Help & Documentation
          </CardTitle>
          <Button
            onClick={onToggle}
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/10"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex gap-4 items-center mt-4">
          <div className="relative flex-1">
            <Search className="w-4 h-4 absolute left-3 top-2.5 text-gray-400" />
            <input
              type="text"
              placeholder="Search help topics..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg focus:outline-none focus:border-white/40"
            />
          </div>
          
          {currentContext && (
            <Badge variant="secondary" className="bg-blue-600">
              Context: {currentContext}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="h-full overflow-hidden flex">
        {/* Sidebar */}
        <div className="w-1/3 border-r border-white/20 pr-4 overflow-y-auto">
          <Tabs value={activeCategory} onValueChange={setActiveCategory}>
            <TabsList className="grid w-full grid-cols-1 bg-white/10 mb-4">
              {categories.map((category) => (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className="text-xs justify-start"
                >
                  {category.icon}
                  <span className="ml-2">{category.name}</span>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>

          <div className="space-y-2">
            {filteredTopics.map((topic) => (
              <div
                key={topic.id}
                className={`p-3 rounded-lg border cursor-pointer transition-all ${
                  selectedTopic === topic.id
                    ? 'bg-blue-600/20 border-blue-400'
                    : 'bg-white/5 border-white/10 hover:bg-white/10'
                }`}
                onClick={() => handleTopicSelect(topic.id)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="font-medium text-sm mb-1">{topic.title}</div>
                    <div className="text-xs text-gray-400 mb-2">{topic.description}</div>
                    <div className="flex gap-2 items-center">
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${getDifficultyColor(topic.difficulty)}`}
                      >
                        {topic.difficulty}
                      </Badge>
                      {topic.estimatedTime && (
                        <span className="text-xs text-gray-400">{topic.estimatedTime}</span>
                      )}
                    </div>
                  </div>
                  <ChevronRight className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 pl-4 overflow-y-auto">
          {selectedTopicData ? (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-bold mb-2">{selectedTopicData.title}</h2>
                <p className="text-gray-300 mb-4">{selectedTopicData.description}</p>
                
                <div className="flex gap-2 mb-6">
                  <Badge 
                    variant="secondary" 
                    className={`${getDifficultyColor(selectedTopicData.difficulty)}`}
                  >
                    {selectedTopicData.difficulty}
                  </Badge>
                  {selectedTopicData.estimatedTime && (
                    <Badge variant="outline">
                      {selectedTopicData.estimatedTime}
                    </Badge>
                  )}
                </div>
              </div>

              <div className="prose prose-invert max-w-none">
                {selectedTopicData.content}
              </div>

              {selectedTopicData.relatedTopics && (
                <div>
                  <h3 className="font-medium mb-3">Related Topics</h3>
                  <div className="grid gap-2">
                    {selectedTopicData.relatedTopics.map((relatedId) => {
                      const relatedTopic = helpTopics.find(t => t.id === relatedId);
                      return relatedTopic ? (
                        <Button
                          key={relatedId}
                          onClick={() => handleTopicSelect(relatedId)}
                          variant="outline"
                          className="justify-start text-left h-auto p-3"
                        >
                          <div>
                            <div className="font-medium text-sm">{relatedTopic.title}</div>
                            <div className="text-xs opacity-70">{relatedTopic.description}</div>
                          </div>
                        </Button>
                      ) : null;
                    })}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <Lightbulb className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">Select a Help Topic</h3>
              <p className="text-gray-400">
                Choose a topic from the sidebar to view detailed help information.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Hook for managing contextual help
 */
export const useContextualHelp = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentContext, setCurrentContext] = useState<string | null>(null);
  const [viewedTopics, setViewedTopics] = useState<Set<string>>(new Set());

  const toggleVisibility = useCallback(() => {
    setIsVisible(prev => !prev);
  }, []);

  const setContext = useCallback((context: string) => {
    setCurrentContext(context);
  }, []);

  const markTopicViewed = useCallback((topicId: string) => {
    setViewedTopics(prev => new Set([...prev, topicId]));
  }, []);

  return {
    isVisible,
    currentContext,
    viewedTopics,
    toggleVisibility,
    setContext,
    markTopicViewed
  };
};
