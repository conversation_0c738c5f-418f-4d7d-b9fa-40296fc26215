import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { LivingDataCanvas } from '@/components/visual-analysis/LivingDataCanvas';
import { ChatAnalysisCanvasContainer } from '@/components/visual-analysis/LivingDataCanvas';
import { AnalysisResult } from '@/types/conversation';

// Mock Three.js
vi.mock('three', () => ({
  Scene: vi.fn(() => ({
    add: vi.fn(),
    remove: vi.fn(),
    traverse: vi.fn(),
  })),
  WebGLRenderer: vi.fn(() => ({
    setSize: vi.fn(),
    render: vi.fn(),
    domElement: document.createElement('canvas'),
    dispose: vi.fn(),
  })),
  OrthographicCamera: vi.fn(() => ({
    position: { set: vi.fn(), copy: vi.fn() },
    lookAt: vi.fn(),
    updateProjectionMatrix: vi.fn(),
  })),
  SphereGeometry: vi.fn(),
  MeshBasicMaterial: vi.fn(),
  Mesh: vi.fn(() => ({
    position: { set: vi.fn(), copy: vi.fn() },
    scale: { setScalar: vi.fn() },
    material: { color: { set: vi.fn() } },
    userData: {},
  })),
  Vector3: vi.fn(() => ({
    set: vi.fn(),
    copy: vi.fn(),
    distanceTo: vi.fn(() => 10),
  })),
  LineBasicMaterial: vi.fn(),
  Line: vi.fn(),
  BufferGeometry: vi.fn(),
  Frustum: vi.fn(() => ({
    setFromProjectionMatrix: vi.fn(),
    containsPoint: vi.fn(() => true),
  })),
  Matrix4: vi.fn(() => ({
    multiplyMatrices: vi.fn(),
  })),
  Color: vi.fn(() => ({
    set: vi.fn(),
    copy: vi.fn(),
    lerp: vi.fn(),
  })),
}));

// Mock WebSocket
global.WebSocket = vi.fn(() => ({
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  send: vi.fn(),
  close: vi.fn(),
  readyState: 1,
})) as any;

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    memory: {
      usedJSHeapSize: 1000000,
    },
  },
});

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Sample test data
const mockAnalysisResults: AnalysisResult[] = [
  {
    id: 'test-1',
    question: 'What are the benefits of AI?',
    analysis: 'AI offers numerous benefits including automation, efficiency, and enhanced decision-making capabilities.',
    analysisType: 'multiple',
    style: 'detailed',
    model: 'gpt-4',
    timestamp: '2024-01-15T10:00:00Z',
    rating: 5,
    followUpQuestions: ['How can we implement AI safely?', 'What are the costs involved?']
  },
  {
    id: 'test-2',
    question: 'Explain quantum computing',
    analysis: 'Quantum computing leverages quantum mechanical phenomena to process information in fundamentally different ways.',
    analysisType: 'deep',
    style: 'technical',
    model: 'gpt-4',
    timestamp: '2024-01-15T11:00:00Z',
    rating: 4,
    followUpQuestions: ['What are practical applications?', 'When will it become mainstream?']
  }
];

describe('LivingDataCanvas', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<LivingDataCanvas />);
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('renders with chat analysis integration enabled', () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('displays connection status when real-time updates are enabled', async () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      await waitFor(() => {
        expect(screen.getByText(/Live|Offline|Connecting/)).toBeInTheDocument();
      });
    });
  });

  describe('Node Interactions', () => {
    it('handles node selection', async () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      // Simulate node click
      const canvas = screen.getByRole('main').firstChild as HTMLElement;
      fireEvent.click(canvas, { clientX: 100, clientY: 100 });
      
      // Should not throw errors
      expect(canvas).toBeInTheDocument();
    });

    it('handles multi-selection with Ctrl+click', async () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      const canvas = screen.getByRole('main').firstChild as HTMLElement;
      
      // Simulate Ctrl+click
      fireEvent.click(canvas, { 
        clientX: 100, 
        clientY: 100, 
        ctrlKey: true 
      });
      
      expect(canvas).toBeInTheDocument();
    });

    it('starts connection mode with Ctrl+Shift+click', async () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      const canvas = screen.getByRole('main').firstChild as HTMLElement;
      
      // Simulate Ctrl+Shift+click
      fireEvent.click(canvas, { 
        clientX: 100, 
        clientY: 100, 
        ctrlKey: true,
        shiftKey: true
      });
      
      expect(canvas).toBeInTheDocument();
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('handles cluster creation shortcut', async () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      // Simulate 'C' key press
      fireEvent.keyDown(document, { key: 'c', code: 'KeyC' });
      
      // Should not throw errors
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('handles simulation creation shortcut', async () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      // Simulate 'S' key press
      fireEvent.keyDown(document, { key: 's', code: 'KeyS' });
      
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    it('handles escape key for canceling operations', async () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      // Simulate Escape key press
      fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' });
      
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('Advanced Controls', () => {
    it('toggles advanced control panel', async () => {
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      // Look for settings button
      const settingsButton = screen.getByRole('button');
      if (settingsButton) {
        fireEvent.click(settingsButton);
      }
      
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('Performance Monitoring', () => {
    it('displays performance warnings when FPS is low', async () => {
      // Mock low FPS
      const mockPerformanceMonitor = {
        update: vi.fn(),
        getMetrics: vi.fn(() => ({
          fps: 25, // Low FPS
          frameTime: 40,
          memoryUsage: 50,
          frameCount: 100
        })),
        shouldOptimize: vi.fn(() => true)
      };

      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      // Performance warning should appear for low FPS
      await waitFor(() => {
        // The component should handle low FPS gracefully
        expect(screen.getByRole('main')).toBeInTheDocument();
      });
    });
  });

  describe('Data Integration', () => {
    it('handles API errors gracefully', async () => {
      // Mock API failure
      global.fetch = vi.fn(() => Promise.reject(new Error('API Error')));
      
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });
    });

    it('falls back to local data when API fails', async () => {
      // Mock API failure
      global.fetch = vi.fn(() => Promise.reject(new Error('Network Error')));
      
      render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
      
      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });
    });
  });
});

describe('ChatAnalysisCanvasContainer', () => {
  it('renders with default props', () => {
    render(<ChatAnalysisCanvasContainer />);
    expect(screen.getByRole('main')).toBeInTheDocument();
  });

  it('integrates with analysis store', async () => {
    render(<ChatAnalysisCanvasContainer />);
    
    await waitFor(() => {
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });
});

describe('Onboarding Tour', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockReturnValue(null); // First time user
  });

  it('shows onboarding tour for new users', async () => {
    render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
    
    await waitFor(() => {
      // Tour should appear for new users
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  it('skips tour for returning users', async () => {
    localStorageMock.getItem.mockReturnValue('true'); // Returning user
    
    render(<LivingDataCanvas enableChatAnalysisIntegration={true} />);
    
    await waitFor(() => {
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });
});

describe('Accessibility', () => {
  it('has proper ARIA labels', () => {
    render(<LivingDataCanvas />);
    
    const main = screen.getByRole('main');
    expect(main).toBeInTheDocument();
  });

  it('supports keyboard navigation', () => {
    render(<LivingDataCanvas />);
    
    // Test tab navigation
    fireEvent.keyDown(document, { key: 'Tab' });
    
    expect(screen.getByRole('main')).toBeInTheDocument();
  });
});

describe('Error Boundaries', () => {
  it('handles rendering errors gracefully', () => {
    // Mock console.error to avoid noise in tests
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(<LivingDataCanvas />);
    
    expect(screen.getByRole('main')).toBeInTheDocument();
    
    consoleSpy.mockRestore();
  });
});
