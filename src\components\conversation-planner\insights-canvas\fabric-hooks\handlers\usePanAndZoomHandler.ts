
import { useRef } from 'react';
import { Canvas, TEvent, Point, TPointerEvent, TPointerEventInfo } from 'fabric';

interface PanAndZoomHandlerProps {
    canvas: Canvas | null;
}

export const usePanAndZoomHandler = ({ canvas }: PanAndZoomHandlerProps) => {
    const dragState = useRef({ isDragging: false, lastPosX: 0, lastPosY: 0 });
    
    const isPanning = () => dragState.current.isDragging;

    const isPanAttempt = (evt: TPointerEvent): boolean => {
        if (evt instanceof MouseEvent || evt instanceof PointerEvent) {
            return evt.altKey || evt.button === 1;
        }
        if (evt instanceof TouchEvent) {
            return evt.touches.length === 1 && evt.altKey;
        }
        return false;
    };    const handleMouseWheel = (opt: TEvent<WheelEvent>) => {
        if (!canvas || !canvas.viewportTransform) return;
        const delta = opt.e.deltaY;
        let zoom = canvas.getZoom();
        zoom *= 0.999 ** delta;
        if (zoom > 3) zoom = 3;
        if (zoom < 0.3) zoom = 0.3;
        
        try {
            canvas.zoomToPoint(new Point(opt.e.offsetX, opt.e.offsetY), zoom);
            opt.e.preventDefault();
            opt.e.stopPropagation();
        } catch (error) {
            console.warn('Zoom operation failed:', error);
        }
    };

    const handlePanStart = (evt: TPointerEvent) => {
        if (!canvas) return;
        let panX = 0, panY = 0;
        if (evt instanceof MouseEvent || evt instanceof PointerEvent) {
            panX = evt.clientX;
            panY = evt.clientY;
        } else if (evt instanceof TouchEvent) {
            panX = evt.touches[0].clientX;
            panY = evt.touches[0].clientY;
        } else {
            return;
        }

        dragState.current.isDragging = true;
        canvas.selection = false;
        dragState.current.lastPosX = panX;
        dragState.current.lastPosY = panY;
        canvas.defaultCursor = 'grabbing';
        canvas.requestRenderAll();
    };

    const handlePanMove = (opt: TPointerEventInfo) => {
        if (!canvas || !dragState.current.isDragging) return;
        const e = opt.e;
        let clientX = 0, clientY = 0;
        if (e instanceof MouseEvent || e instanceof PointerEvent) {
            clientX = e.clientX;
            clientY = e.clientY;
        } else if (e instanceof TouchEvent && e.touches.length > 0) {
            clientX = e.touches[0].clientX;
            clientY = e.touches[0].clientY;
        } else return;
          const vpt = canvas.viewportTransform;
        if (vpt && vpt.length === 6) {
            vpt[4] += clientX - dragState.current.lastPosX;
            vpt[5] += clientY - dragState.current.lastPosY;
            canvas.requestRenderAll();
        }
        dragState.current.lastPosX = clientX;
        dragState.current.lastPosY = clientY;
    };
      const handlePanEnd = (opt: TPointerEventInfo) => {
        if (!canvas) return;
        const vpt = canvas.viewportTransform;
        if (vpt && vpt.length === 6) {
            canvas.setViewportTransform(vpt);
        }
        dragState.current.isDragging = false;
        canvas.selection = true;
        
        const event = opt.e as TPointerEvent;
        if ('altKey' in event && event.altKey) {
            canvas.defaultCursor = 'grab';
        } else {
            canvas.defaultCursor = 'default';
        }
        canvas.requestRenderAll();
    };

    const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Alt' && canvas && !dragState.current.isDragging) {
            canvas.defaultCursor = 'grab';
            canvas.requestRenderAll();
        }
    };
    const handleKeyUp = (e: KeyboardEvent) => {
        if (e.key === 'Alt' && canvas && !dragState.current.isDragging) {
            canvas.defaultCursor = 'default';
            canvas.requestRenderAll();
        }
    };

    return { isPanning, isPanAttempt, handleMouseWheel, handlePanStart, handlePanMove, handlePanEnd, handleKeyDown, handleKeyUp };
};
