/**
 * Design Tokens System
 * 
 * Centralized design tokens for consistent styling across the application.
 * These tokens define the visual language and ensure consistency.
 */

// Color Palette
export const colors = {
  // Primary colors
  primary: {
    50: 'hsl(221, 83%, 95%)',
    100: 'hsl(221, 83%, 90%)',
    200: 'hsl(221, 83%, 80%)',
    300: 'hsl(221, 83%, 70%)',
    400: 'hsl(221, 83%, 60%)',
    500: 'hsl(221, 83%, 53%)', // Main primary
    600: 'hsl(221, 83%, 48%)',
    700: 'hsl(221, 83%, 40%)',
    800: 'hsl(221, 83%, 30%)',
    900: 'hsl(221, 83%, 20%)',
    950: 'hsl(221, 83%, 10%)',
  },
  
  // Neutral colors
  neutral: {
    0: 'hsl(0, 0%, 100%)',
    50: 'hsl(220, 14%, 96%)',
    100: 'hsl(220, 13%, 91%)',
    200: 'hsl(220, 13%, 85%)',
    300: 'hsl(220, 13%, 75%)',
    400: 'hsl(215, 20%, 65%)',
    500: 'hsl(215, 16%, 47%)',
    600: 'hsl(215, 19%, 35%)',
    700: 'hsl(215, 25%, 27%)',
    800: 'hsl(217, 33%, 17%)',
    900: 'hsl(224, 71%, 4%)',
    950: 'hsl(224, 84%, 2%)',
  },
  
  // Semantic colors
  success: {
    50: 'hsl(142, 76%, 95%)',
    100: 'hsl(142, 76%, 90%)',
    500: 'hsl(142, 76%, 36%)',
    600: 'hsl(142, 76%, 30%)',
    foreground: 'hsl(0, 0%, 100%)',
  },
  
  warning: {
    50: 'hsl(48, 96%, 95%)',
    100: 'hsl(48, 96%, 90%)',
    500: 'hsl(48, 96%, 53%)',
    600: 'hsl(48, 96%, 47%)',
    foreground: 'hsl(224, 71%, 4%)',
  },
  
  error: {
    50: 'hsl(0, 84%, 95%)',
    100: 'hsl(0, 84%, 90%)',
    500: 'hsl(0, 84%, 60%)',
    600: 'hsl(0, 84%, 54%)',
    foreground: 'hsl(0, 0%, 100%)',
  },
  
  // Analysis-specific colors
  analysis: {
    multiple: {
      bg: 'hsl(262, 83%, 95%)',
      fg: 'hsl(262, 83%, 20%)',
    },
    deep: {
      bg: 'hsl(142, 76%, 95%)',
      fg: 'hsl(142, 76%, 20%)',
    },
    character: {
      bg: 'hsl(48, 96%, 95%)',
      fg: 'hsl(48, 96%, 20%)',
    },
  },
  
  // Suggestion colors
  suggestion: {
    semantic: {
      bg: 'hsl(221, 83%, 95%)',
      fg: 'hsl(221, 83%, 20%)',
    },
    temporal: {
      bg: 'hsl(142, 76%, 95%)',
      fg: 'hsl(142, 76%, 20%)',
    },
    thematic: {
      bg: 'hsl(262, 83%, 95%)',
      fg: 'hsl(262, 83%, 20%)',
    },
    complementary: {
      bg: 'hsl(48, 96%, 95%)',
      fg: 'hsl(48, 96%, 20%)',
    },
  },
} as const;

// Typography Scale
export const typography = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'Consolas', 'monospace'],
  },
  
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem', // 60px
  },
  
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
  },
  
  letterSpacing: {
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
  },
} as const;

// Spacing Scale
export const spacing = {
  0: '0',
  px: '1px',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px
  3.5: '0.875rem',  // 14px
  4: '1rem',        // 16px
  5: '1.25rem',     // 20px
  6: '1.5rem',      // 24px
  7: '1.75rem',     // 28px
  8: '2rem',        // 32px
  9: '2.25rem',     // 36px
  10: '2.5rem',     // 40px
  11: '2.75rem',    // 44px
  12: '3rem',       // 48px
  14: '3.5rem',     // 56px
  16: '4rem',       // 64px
  20: '5rem',       // 80px
  24: '6rem',       // 96px
  28: '7rem',       // 112px
  32: '8rem',       // 128px
  36: '9rem',       // 144px
  40: '10rem',      // 160px
  44: '11rem',      // 176px
  48: '12rem',      // 192px
  52: '13rem',      // 208px
  56: '14rem',      // 224px
  60: '15rem',      // 240px
  64: '16rem',      // 256px
  72: '18rem',      // 288px
  80: '20rem',      // 320px
  96: '24rem',      // 384px
} as const;

// Border Radius Scale
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  base: '0.25rem',  // 4px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
} as const;

// Shadow Scale
export const shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  soft: '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
} as const;

// Animation Tokens
export const animations = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
  
  keyframes: {
    fadeIn: {
      '0%': { opacity: '0', transform: 'translateY(10px)' },
      '100%': { opacity: '1', transform: 'translateY(0)' },
    },
    scaleIn: {
      '0%': { transform: 'scale(0.95)', opacity: '0' },
      '100%': { transform: 'scale(1)', opacity: '1' },
    },
    slideIn: {
      '0%': { transform: 'translateX(-100%)' },
      '100%': { transform: 'translateX(0)' },
    },
    pulseGlow: {
      '0%, 100%': { opacity: '0.7' },
      '50%': { opacity: '1' },
    },
  },
} as const;

// Breakpoints
export const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Z-Index Scale
export const zIndex = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
} as const;

// Component Sizes
export const componentSizes = {
  xs: {
    height: '1.5rem',   // 24px
    padding: '0.25rem 0.5rem',
    fontSize: typography.fontSize.xs,
  },
  sm: {
    height: '2rem',     // 32px
    padding: '0.375rem 0.75rem',
    fontSize: typography.fontSize.sm,
  },
  md: {
    height: '2.5rem',   // 40px
    padding: '0.5rem 1rem',
    fontSize: typography.fontSize.base,
  },
  lg: {
    height: '3rem',     // 48px
    padding: '0.75rem 1.5rem',
    fontSize: typography.fontSize.lg,
  },
  xl: {
    height: '3.5rem',   // 56px
    padding: '1rem 2rem',
    fontSize: typography.fontSize.xl,
  },
} as const;

// Export all tokens as a single object
export const designTokens = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animations,
  breakpoints,
  zIndex,
  componentSizes,
} as const;

export type DesignTokens = typeof designTokens;
export type ColorPalette = typeof colors;
export type TypographyScale = typeof typography;
export type SpacingScale = typeof spacing;
