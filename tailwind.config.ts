
import type { Config } from "tailwindcss";
import { designTokens } from "./src/design-system/tokens";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			// Design system integration
			fontFamily: {
				sans: designTokens.typography.fontFamily.sans,
				mono: designTokens.typography.fontFamily.mono,
			},
			fontSize: designTokens.typography.fontSize,
			fontWeight: designTokens.typography.fontWeight,
			lineHeight: designTokens.typography.lineHeight,
			letterSpacing: designTokens.typography.letterSpacing,
			spacing: designTokens.spacing,
			borderRadius: {
				...designTokens.borderRadius,
				xl: `calc(var(--radius) + 4px)`,
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			boxShadow: designTokens.shadows,
			zIndex: designTokens.zIndex,

			// Color system
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					hover: 'hsl(var(--primary-hover))',
					foreground: 'hsl(var(--primary-foreground))',
					50: 'hsl(var(--primary-50))',
					100: 'hsl(var(--primary-100))',
					500: 'hsl(var(--primary-500))',
					600: 'hsl(var(--primary-600))',
					900: 'hsl(var(--primary-900))',
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				success: {
					DEFAULT: 'hsl(var(--success))',
					foreground: 'hsl(var(--success-foreground))'
				},
				warning: {
					DEFAULT: 'hsl(var(--warning))',
					foreground: 'hsl(var(--warning-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
        'surface-hover': 'hsl(var(--surface-hover))',
        'analysis-multiple': {
          DEFAULT: 'hsl(var(--analysis-multiple-bg))',
          foreground: 'hsl(var(--analysis-multiple-fg))'
        },
        'analysis-deep': {
          DEFAULT: 'hsl(var(--analysis-deep-bg))',
          foreground: 'hsl(var(--analysis-deep-fg))'
        },
        'analysis-character': {
          DEFAULT: 'hsl(var(--analysis-character-bg))',
          foreground: 'hsl(var(--analysis-character-fg))'
        },
        'suggestion-semantic': 'hsl(var(--suggestion-semantic-background))',
        'suggestion-semantic-foreground': 'hsl(var(--suggestion-semantic-foreground))',
        'suggestion-temporal': 'hsl(var(--suggestion-temporal-background))',
        'suggestion-temporal-foreground': 'hsl(var(--suggestion-temporal-foreground))',
        'suggestion-thematic': 'hsl(var(--suggestion-thematic-background))',
        'suggestion-thematic-foreground': 'hsl(var(--suggestion-thematic-foreground))',
        'suggestion-complementary': 'hsl(var(--suggestion-complementary-background))',
        'suggestion-complementary-foreground': 'hsl(var(--suggestion-complementary-foreground))',
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				}
			},
			borderRadius: {
        xl: `calc(var(--radius) + 4px)`,
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			// Animation system from design tokens
			keyframes: {
				...designTokens.animations.keyframes,
				'accordion-down': {
					from: { height: '0' },
					to: { height: 'var(--radix-accordion-content-height)' }
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: '0' }
				},
				'shimmer': {
					'0%': { backgroundPosition: '-200% 0' },
					'100%': { backgroundPosition: '200% 0' }
				},
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': `fade-in ${designTokens.animations.duration.normal} ${designTokens.animations.easing.easeOut} forwards`,
				'scale-in': `scale-in ${designTokens.animations.duration.normal} ${designTokens.animations.easing.easeOut} forwards`,
				'pulse-glow': `pulse-glow ${designTokens.animations.duration.slow} ${designTokens.animations.easing.easeInOut} infinite`,
				'shimmer': 'shimmer 1.5s infinite',
			},

			// Transition timing functions
			transitionTimingFunction: {
				'ease-in': designTokens.animations.easing.easeIn,
				'ease-out': designTokens.animations.easing.easeOut,
				'ease-in-out': designTokens.animations.easing.easeInOut,
			},

			// Transition durations
			transitionDuration: {
				'fast': designTokens.animations.duration.fast,
				'normal': designTokens.animations.duration.normal,
				'slow': designTokens.animations.duration.slow,
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
