import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  ArrowRight, 
  ArrowLeft, 
  X, 
  Play, 
  MousePointer, 
  Keyboard,
  Eye,
  Users,
  Zap
} from 'lucide-react';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  position: { x: number; y: number };
  highlight?: string;
  action?: string;
}

interface OnboardingTourProps {
  isVisible: boolean;
  onComplete: () => void;
  onSkip: () => void;
}

export const OnboardingTour: React.FC<OnboardingTourProps> = ({
  isVisible,
  onComplete,
  onSkip
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Living Data Canvas',
      description: 'An interactive platform for visualizing and managing chat analysis records. Let\'s take a quick tour!',
      icon: <Play className="w-5 h-5" />,
      position: { x: 50, y: 30 }
    },
    {
      id: 'nodes',
      title: 'Analysis Record Nodes',
      description: 'Each node represents a chat analysis record. Hover over nodes to see details, click to select them.',
      icon: <Eye className="w-5 h-5" />,
      position: { x: 30, y: 50 },
      highlight: 'nodes',
      action: 'Try clicking on a node to see its information panel.'
    },
    {
      id: 'connections',
      title: 'Node Connections',
      description: 'Lines between nodes show relationships. Hold Ctrl+Shift and click two nodes to create connections.',
      icon: <MousePointer className="w-5 h-5" />,
      position: { x: 70, y: 40 },
      highlight: 'connections',
      action: 'Try Ctrl+Shift+Click on a node to start connection mode.'
    },
    {
      id: 'multi-select',
      title: 'Multi-Selection',
      description: 'Hold Ctrl and click multiple nodes to select them. This enables batch operations like clustering.',
      icon: <Keyboard className="w-5 h-5" />,
      position: { x: 20, y: 70 },
      action: 'Try Ctrl+Click on multiple nodes to select them.'
    },
    {
      id: 'clustering',
      title: 'Create Clusters',
      description: 'Select multiple nodes and press \'C\' to group them into clusters. Clusters help organize related analyses.',
      icon: <Users className="w-5 h-5" />,
      position: { x: 60, y: 20 },
      highlight: 'clusters',
      action: 'Select nodes and press \'C\' to create a cluster.'
    },
    {
      id: 'simulations',
      title: 'Chat Simulations',
      description: 'Select nodes and press \'S\' to create chat simulations. Test scenarios and generate new insights.',
      icon: <Zap className="w-5 h-5" />,
      position: { x: 80, y: 60 },
      action: 'Select nodes and press \'S\' to create a simulation.'
    },
    {
      id: 'context-menu',
      title: 'Context Menus',
      description: 'Right-click on analysis nodes to access actions like viewing details, re-running analysis, or creating simulations.',
      icon: <MousePointer className="w-5 h-5" />,
      position: { x: 40, y: 80 },
      action: 'Try right-clicking on an analysis node.'
    },
    {
      id: 'controls',
      title: 'Advanced Controls',
      description: 'Use the settings panel (top-right) to customize layouts, themes, and visibility options.',
      icon: <Eye className="w-5 h-5" />,
      position: { x: 85, y: 15 },
      highlight: 'controls'
    },
    {
      id: 'complete',
      title: 'You\'re Ready!',
      description: 'You now know the basics of the Living Data Canvas. Start exploring your chat analysis data!',
      icon: <Play className="w-5 h-5" />,
      position: { x: 50, y: 50 }
    }
  ];

  const currentStepData = steps[currentStep];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying) {
      const timer = setTimeout(() => {
        if (currentStep < steps.length - 1) {
          setCurrentStep(currentStep + 1);
        } else {
          setIsPlaying(false);
          onComplete();
        }
      }, 4000); // 4 seconds per step

      return () => clearTimeout(timer);
    }
  }, [isPlaying, currentStep, onComplete]);

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-30" />
      
      {/* Highlight overlay for specific elements */}
      {currentStepData.highlight && (
        <div 
          className="fixed inset-0 z-31 pointer-events-none"
          style={{
            background: `radial-gradient(circle at ${currentStepData.position.x}% ${currentStepData.position.y}%, transparent 100px, rgba(0,0,0,0.7) 200px)`
          }}
        />
      )}

      {/* Tour Card */}
      <Card 
        className="fixed z-32 w-80 bg-white/95 backdrop-blur-md border-blue-200 shadow-2xl"
        style={{
          left: `${Math.min(Math.max(currentStepData.position.x, 10), 70)}%`,
          top: `${Math.min(Math.max(currentStepData.position.y, 10), 70)}%`,
          transform: 'translate(-50%, -50%)'
        }}
      >
        <CardContent className="p-6">
          {/* Header */}
          <div className="flex justify-between items-start mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
                {currentStepData.icon}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{currentStepData.title}</h3>
                <Badge variant="secondary" className="text-xs">
                  Step {currentStep + 1} of {steps.length}
                </Badge>
              </div>
            </div>
            <Button
              onClick={onSkip}
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Content */}
          <div className="space-y-4">
            <p className="text-gray-700 text-sm leading-relaxed">
              {currentStepData.description}
            </p>

            {currentStepData.action && (
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-blue-800 text-xs font-medium">
                  💡 Try it: {currentStepData.action}
                </p>
              </div>
            )}
          </div>

          {/* Progress Dots */}
          <div className="flex justify-center gap-2 my-4">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => handleStepClick(index)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentStep 
                    ? 'bg-blue-600' 
                    : index < currentStep 
                      ? 'bg-blue-300' 
                      : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          {/* Controls */}
          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Button
                onClick={handlePrevious}
                disabled={currentStep === 0}
                variant="outline"
                size="sm"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Previous
              </Button>
              
              <Button
                onClick={() => setIsPlaying(!isPlaying)}
                variant="outline"
                size="sm"
                className={isPlaying ? 'bg-orange-100 text-orange-700' : ''}
              >
                {isPlaying ? 'Pause' : 'Auto-play'}
              </Button>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={onSkip}
                variant="ghost"
                size="sm"
                className="text-gray-500"
              >
                Skip Tour
              </Button>
              
              <Button
                onClick={handleNext}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
                {currentStep < steps.length - 1 && <ArrowRight className="w-4 h-4 ml-1" />}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Keyboard shortcuts hint */}
      <div className="fixed bottom-4 left-4 z-32 bg-white/90 backdrop-blur-md rounded-lg p-3 shadow-lg border">
        <div className="text-xs text-gray-600">
          <div className="font-medium mb-1">Quick Navigation:</div>
          <div>← → Arrow keys to navigate</div>
          <div>Esc to skip tour</div>
        </div>
      </div>
    </>
  );
};

/**
 * Hook to manage onboarding tour state
 */
export const useOnboardingTour = () => {
  const [showTour, setShowTour] = useState(false);
  const [hasSeenTour, setHasSeenTour] = useState(false);

  useEffect(() => {
    // Check if user has seen the tour before
    const tourCompleted = localStorage.getItem('canvas-tour-completed');
    if (!tourCompleted) {
      setShowTour(true);
    } else {
      setHasSeenTour(true);
    }
  }, []);

  const completeTour = () => {
    setShowTour(false);
    setHasSeenTour(true);
    localStorage.setItem('canvas-tour-completed', 'true');
  };

  const skipTour = () => {
    setShowTour(false);
    setHasSeenTour(true);
    localStorage.setItem('canvas-tour-completed', 'true');
  };

  const restartTour = () => {
    setShowTour(true);
    setHasSeenTour(false);
  };

  return {
    showTour,
    hasSeenTour,
    completeTour,
    skipTour,
    restartTour
  };
};
