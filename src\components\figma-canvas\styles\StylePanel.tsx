import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ColorPicker } from './ColorPicker';
import { GradientEditor } from './GradientEditor';
import { 
  Palette, 
  Brush, 
  Droplets, 
  Square, 
  Circle,
  Minus,
  MoreHorizontal,
  Save,
  Trash2
} from 'lucide-react';
import { StyleProperties, GradientStyle, ShadowStyle } from '@/types/figma';
import { cn } from '@/lib/utils';

interface StylePanelProps {
  value?: StyleProperties;
  onChange: (style: StyleProperties) => void;
  className?: string;
}

export const StylePanel: React.FC<StylePanelProps> = ({
  value = {},
  onChange,
  className,
}) => {
  const [activeTab, setActiveTab] = useState<'fill' | 'stroke' | 'effects'>('fill');
  const [fillType, setFillType] = useState<'solid' | 'gradient'>('solid');
  const [strokeType, setStrokeType] = useState<'solid' | 'gradient'>('solid');

  // Stroke dash patterns
  const strokePatterns = [
    { name: 'Solid', value: [] },
    { name: 'Dashed', value: [10, 5] },
    { name: 'Dotted', value: [2, 3] },
    { name: 'Dash-Dot', value: [10, 5, 2, 5] },
    { name: 'Long Dash', value: [20, 10] },
  ];

  const updateStyle = (updates: Partial<StyleProperties>) => {
    onChange({ ...value, ...updates });
  };

  const updateShadow = (updates: Partial<ShadowStyle>) => {
    const newShadow = { ...value.shadow, ...updates };
    updateStyle({ shadow: newShadow });
  };

  return (
    <div className={cn("w-64 bg-white border border-gray-200 rounded-lg", className)}>
      <Tabs value={activeTab} onValueChange={(tab) => setActiveTab(tab as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="fill" className="flex items-center gap-1">
            <Square className="w-3 h-3" />
            Fill
          </TabsTrigger>
          <TabsTrigger value="stroke" className="flex items-center gap-1">
            <Circle className="w-3 h-3" />
            Stroke
          </TabsTrigger>
          <TabsTrigger value="effects" className="flex items-center gap-1">
            <Droplets className="w-3 h-3" />
            Effects
          </TabsTrigger>
        </TabsList>

        <div className="p-4">
          <TabsContent value="fill" className="space-y-4 mt-0">
            {/* Fill Type Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={fillType === 'solid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setFillType('solid')}
                className="flex-1"
              >
                <Palette className="w-4 h-4 mr-1" />
                Solid
              </Button>
              <Button
                variant={fillType === 'gradient' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setFillType('gradient')}
                className="flex-1"
              >
                <Brush className="w-4 h-4 mr-1" />
                Gradient
              </Button>
            </div>

            {fillType === 'solid' ? (
              <div>
                <Label className="text-xs">Fill Color</Label>
                <div className="flex items-center gap-2 mt-1">
                  <ColorPicker
                    value={value.fill || '#000000'}
                    onChange={(color) => updateStyle({ fill: color })}
                  />
                  <Input
                    value={value.fill || '#000000'}
                    onChange={(e) => updateStyle({ fill: e.target.value })}
                    className="flex-1 h-8 text-xs font-mono"
                    placeholder="#000000"
                  />
                </div>
              </div>
            ) : (
              <div>
                <Label className="text-xs">Fill Gradient</Label>
                <GradientEditor
                  value={value.gradient}
                  onChange={(gradient) => updateStyle({ gradient })}
                />
              </div>
            )}

            {/* Fill Opacity */}
            <div>
              <Label className="text-xs">Opacity: {Math.round((value.opacity || 1) * 100)}%</Label>
              <Slider
                value={[value.opacity || 1]}
                onValueChange={([opacity]) => updateStyle({ opacity })}
                min={0}
                max={1}
                step={0.01}
                className="w-full mt-1"
              />
            </div>
          </TabsContent>

          <TabsContent value="stroke" className="space-y-4 mt-0">
            {/* Stroke Type Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={strokeType === 'solid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setStrokeType('solid')}
                className="flex-1"
              >
                <Palette className="w-4 h-4 mr-1" />
                Solid
              </Button>
              <Button
                variant={strokeType === 'gradient' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setStrokeType('gradient')}
                className="flex-1"
              >
                <Brush className="w-4 h-4 mr-1" />
                Gradient
              </Button>
            </div>

            {strokeType === 'solid' ? (
              <div>
                <Label className="text-xs">Stroke Color</Label>
                <div className="flex items-center gap-2 mt-1">
                  <ColorPicker
                    value={value.stroke || '#000000'}
                    onChange={(color) => updateStyle({ stroke: color })}
                  />
                  <Input
                    value={value.stroke || '#000000'}
                    onChange={(e) => updateStyle({ stroke: e.target.value })}
                    className="flex-1 h-8 text-xs font-mono"
                    placeholder="#000000"
                  />
                </div>
              </div>
            ) : (
              <div>
                <Label className="text-xs">Stroke Gradient</Label>
                <GradientEditor
                  value={value.gradient}
                  onChange={(gradient) => updateStyle({ gradient })}
                />
              </div>
            )}

            {/* Stroke Width */}
            <div>
              <Label className="text-xs">Width</Label>
              <Input
                type="number"
                min="0"
                value={value.strokeWidth || 0}
                onChange={(e) => updateStyle({ strokeWidth: parseFloat(e.target.value) || 0 })}
                className="h-8 text-xs mt-1"
              />
            </div>

            {/* Stroke Pattern */}
            <div>
              <Label className="text-xs">Pattern</Label>
              <Select
                value={JSON.stringify(value.strokeDashArray || [])}
                onValueChange={(pattern) => {
                  const dashArray = JSON.parse(pattern);
                  updateStyle({ strokeDashArray: dashArray.length > 0 ? dashArray : undefined });
                }}
              >
                <SelectTrigger className="h-8 text-xs mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {strokePatterns.map((pattern) => (
                    <SelectItem key={pattern.name} value={JSON.stringify(pattern.value)}>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center">
                          {pattern.value.length === 0 ? (
                            <Minus className="w-4 h-1" />
                          ) : (
                            <div className="flex items-center gap-px">
                              {pattern.value.map((dash, i) => (
                                <div
                                  key={i}
                                  className={cn(
                                    "bg-current",
                                    i % 2 === 0 ? "h-px" : "h-0"
                                  )}
                                  style={{ width: `${Math.min(dash, 8)}px` }}
                                />
                              ))}
                            </div>
                          )}
                        </div>
                        <span>{pattern.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </TabsContent>

          <TabsContent value="effects" className="space-y-4 mt-0">
            {/* Shadow */}
            <div>
              <Label className="text-xs">Drop Shadow</Label>
              <div className="space-y-2 mt-2">
                <div className="flex items-center gap-2">
                  <ColorPicker
                    value={value.shadow?.color || '#000000'}
                    onChange={(color) => updateShadow({ color })}
                  />
                  <Input
                    value={value.shadow?.color || '#000000'}
                    onChange={(e) => updateShadow({ color: e.target.value })}
                    className="flex-1 h-8 text-xs font-mono"
                    placeholder="#000000"
                  />
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">X Offset</Label>
                    <Input
                      type="number"
                      value={value.shadow?.offsetX || 0}
                      onChange={(e) => updateShadow({ offsetX: parseFloat(e.target.value) || 0 })}
                      className="h-8 text-xs"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Y Offset</Label>
                    <Input
                      type="number"
                      value={value.shadow?.offsetY || 0}
                      onChange={(e) => updateShadow({ offsetY: parseFloat(e.target.value) || 0 })}
                      className="h-8 text-xs"
                    />
                  </div>
                </div>

                <div>
                  <Label className="text-xs">Blur: {value.shadow?.blur || 0}px</Label>
                  <Slider
                    value={[value.shadow?.blur || 0]}
                    onValueChange={([blur]) => updateShadow({ blur })}
                    min={0}
                    max={50}
                    step={1}
                    className="w-full mt-1"
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Opacity */}
            <div>
              <Label className="text-xs">Overall Opacity: {Math.round((value.opacity || 1) * 100)}%</Label>
              <Slider
                value={[value.opacity || 1]}
                onValueChange={([opacity]) => updateStyle({ opacity })}
                min={0}
                max={1}
                step={0.01}
                className="w-full mt-1"
              />
            </div>
          </TabsContent>
        </div>
      </Tabs>

      {/* Style Presets */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center justify-between mb-2">
          <Label className="text-xs">Style Presets</Label>
          <div className="flex gap-1">
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0" title="Save Preset">
              <Save className="w-3 h-3" />
            </Button>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0" title="More Options">
              <MoreHorizontal className="w-3 h-3" />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-4 gap-2">
          {STYLE_PRESETS.map((preset, index) => (
            <button
              key={index}
              className="h-8 border border-gray-200 rounded cursor-pointer hover:border-blue-500 transition-colors relative overflow-hidden"
              onClick={() => onChange(preset)}
              title={`Preset ${index + 1}`}
            >
              <div
                className="absolute inset-0"
                style={{
                  backgroundColor: preset.fill || 'transparent',
                  border: preset.stroke ? `2px solid ${preset.stroke}` : 'none',
                  opacity: preset.opacity || 1,
                }}
              />
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

// Style presets
const STYLE_PRESETS: StyleProperties[] = [
  { fill: '#3b82f6', opacity: 1 },
  { fill: '#ef4444', opacity: 1 },
  { fill: '#10b981', opacity: 1 },
  { fill: '#f59e0b', opacity: 1 },
  { stroke: '#3b82f6', strokeWidth: 2, opacity: 1 },
  { stroke: '#ef4444', strokeWidth: 2, opacity: 1 },
  { fill: '#3b82f6', stroke: '#1e40af', strokeWidth: 2, opacity: 1 },
  { fill: '#ffffff', stroke: '#000000', strokeWidth: 1, opacity: 1 },
  { fill: '#000000', opacity: 0.5 },
  { fill: '#ffffff', opacity: 0.8 },
  { 
    fill: '#3b82f6', 
    shadow: { color: '#000000', blur: 10, offsetX: 2, offsetY: 2 },
    opacity: 1 
  },
  { 
    stroke: '#ef4444', 
    strokeWidth: 3, 
    strokeDashArray: [10, 5],
    opacity: 1 
  },
];
