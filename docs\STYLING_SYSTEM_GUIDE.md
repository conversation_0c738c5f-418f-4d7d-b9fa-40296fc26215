# Styling System Guide

## Overview

The application features a comprehensive styling system built on top of Tailwind CSS, enhanced with custom utilities, animations, and responsive patterns. This guide covers all aspects of the styling system and how to use it effectively.

## Architecture

### Layer Structure

The styling system is organized into layers for optimal performance and maintainability:

```css
@layer base {
  /* Design tokens and base styles */
}

@layer components {
  /* Reusable component styles */
}

@layer utilities {
  /* Utility classes and overrides */
}
```

### File Organization

```
src/styles/
├── animations.css      # Animation keyframes
├── utilities.css       # Custom utility classes
├── responsive.css      # Responsive design patterns
└── enterprise-historical-analysis.css  # Legacy styles
```

## Design Tokens Integration

### CSS Custom Properties

All design tokens are available as CSS custom properties:

```css
/* Colors */
--primary: 221 83% 53%;
--primary-hover: 221 83% 48%;
--success: 142 76% 36%;
--warning: 48 96% 53%;
--destructive: 0 84.2% 60.2%;

/* Spacing */
--spacing-1: 0.25rem;
--spacing-4: 1rem;
--spacing-8: 2rem;

/* Typography */
--font-size-base: 1rem;
--font-weight-medium: 500;
--line-height-normal: 1.5;
```

### Usage in Components

```css
.my-component {
  background-color: hsl(var(--primary));
  padding: var(--spacing-4);
  font-size: var(--font-size-base);
}
```

## Utility Classes

### Glass Morphism Effects

```html
<!-- Basic glass effect -->
<div class="glass">Content</div>

<!-- Glass card -->
<div class="glass-card">Card content</div>

<!-- Dark mode glass -->
<div class="glass-dark">Dark glass</div>
```

### Gradient Backgrounds

```html
<!-- Predefined gradients -->
<div class="gradient-primary">Primary gradient</div>
<div class="gradient-success">Success gradient</div>
<div class="gradient-warning">Warning gradient</div>

<!-- Text gradients -->
<h1 class="text-gradient-primary">Gradient text</h1>
<h2 class="text-gradient-rainbow">Rainbow text</h2>
```

### Interactive States

```html
<!-- Basic interactive element -->
<button class="interactive">Hover me</button>

<!-- Scale interaction -->
<div class="interactive-scale">Scale on hover</div>

<!-- Glow effect -->
<div class="interactive-glow">Glow on hover</div>
```

### Shadows and Elevation

```html
<!-- Soft shadow -->
<div class="shadow-soft">Soft shadow</div>

<!-- Glow effects -->
<div class="shadow-glow">Primary glow</div>
<div class="shadow-glow-success">Success glow</div>
<div class="shadow-glow-warning">Warning glow</div>
```

### Layout Utilities

```html
<!-- Centering -->
<div class="center">Centered content</div>
<div class="center-x">Horizontally centered</div>
<div class="center-y">Vertically centered</div>

<!-- Container with responsive padding -->
<div class="container-padding">Responsive container</div>

<!-- Section spacing -->
<section class="section-spacing">Section content</section>
```

### Grid Utilities

```html
<!-- Auto-fit grid -->
<div class="grid-auto-fit">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</div>

<!-- Responsive grid -->
<div class="grid-responsive">
  <div>Card 1</div>
  <div>Card 2</div>
  <div>Card 3</div>
</div>
```

## Animation System

### Fade Animations

```html
<!-- Fade in directions -->
<div class="animate-fade-in-up">Fade in from bottom</div>
<div class="animate-fade-in-down">Fade in from top</div>
<div class="animate-fade-in-left">Fade in from left</div>
<div class="animate-fade-in-right">Fade in from right</div>
```

### Scale and Bounce

```html
<!-- Scale animations -->
<div class="animate-scale-in">Scale in</div>

<!-- Bounce animations -->
<div class="animate-bounce-in">Bounce in</div>
```

### Slide Animations

```html
<!-- Slide animations -->
<div class="animate-slide-up">Slide up</div>
<div class="animate-slide-down">Slide down</div>
```

### Continuous Animations

```html
<!-- Float effect -->
<div class="animate-float">Floating element</div>

<!-- Pulse glow -->
<div class="animate-pulse-glow">Pulsing glow</div>
```

### Loading States

```html
<!-- Skeleton loader -->
<div class="loading-skeleton h-4 w-32"></div>

<!-- Loading dots -->
<span class="loading-dots">Loading</span>

<!-- Spinner -->
<div class="loading-spinner w-6 h-6"></div>
```

## Responsive Design

### Responsive Typography

```html
<!-- Responsive text sizes -->
<h1 class="text-responsive-3xl">Large heading</h1>
<p class="text-responsive-base">Body text</p>
<small class="text-responsive-xs">Small text</small>
```

### Responsive Spacing

```html
<!-- Responsive padding -->
<div class="spacing-responsive-md">Responsive padding</div>

<!-- Responsive margins -->
<div class="margin-responsive-lg">Responsive margins</div>
```

### Responsive Grids

```html
<!-- Responsive card grid -->
<div class="grid-responsive-cards">
  <div>Card 1</div>
  <div>Card 2</div>
  <div>Card 3</div>
</div>

<!-- Auto-responsive grid -->
<div class="grid-responsive-auto">
  <div>Item 1</div>
  <div>Item 2</div>
</div>
```

### Responsive Visibility

```html
<!-- Device-specific visibility -->
<div class="mobile-only">Mobile only</div>
<div class="tablet-only">Tablet only</div>
<div class="desktop-only">Desktop only</div>

<!-- Combined visibility -->
<div class="mobile-tablet">Mobile and tablet</div>
<div class="tablet-desktop">Tablet and desktop</div>
```

### Responsive Components

```html
<!-- Responsive card -->
<div class="card-responsive">
  Responsive card with adaptive padding and shadows
</div>

<!-- Responsive button -->
<button class="btn-responsive">Responsive button</button>

<!-- Responsive form -->
<form class="form-responsive">
  <div class="form-group-responsive">
    <label>Label</label>
    <input class="input-responsive" />
  </div>
</form>
```

## Component Styling Patterns

### Card Components

```html
<!-- Basic card -->
<div class="card-responsive shadow-soft">
  <h3 class="text-responsive-lg font-semibold mb-4">Card Title</h3>
  <p class="text-responsive-base text-muted-foreground">Card content</p>
</div>

<!-- Interactive card -->
<div class="card-responsive interactive shadow-soft hover:shadow-glow">
  <h3 class="text-gradient-primary">Interactive Card</h3>
</div>
```

### Button Components

```html
<!-- Primary button -->
<button class="btn-responsive gradient-primary text-white interactive">
  Primary Action
</button>

<!-- Secondary button -->
<button class="btn-responsive border border-border interactive">
  Secondary Action
</button>
```

### Form Components

```html
<!-- Form field -->
<div class="form-group-responsive">
  <label class="text-responsive-sm font-medium">Field Label</label>
  <input class="input-responsive focus-ring" placeholder="Enter value" />
</div>

<!-- Form with validation -->
<div class="form-group-responsive">
  <label class="text-responsive-sm font-medium">Email</label>
  <input 
    class="input-responsive focus-ring border-destructive" 
    type="email" 
    placeholder="Enter email"
  />
  <span class="text-xs text-destructive">Invalid email format</span>
</div>
```

## Accessibility Features

### Focus Management

```html
<!-- Focus ring -->
<button class="focus-ring">Accessible button</button>

<!-- Focus visible (keyboard only) -->
<button class="focus-visible-ring">Keyboard focus only</button>

<!-- Focus inset -->
<input class="focus-ring-inset" />
```

### Screen Reader Support

```html
<!-- Screen reader only -->
<span class="sr-only">Screen reader only text</span>

<!-- Screen reader focusable -->
<a href="#main" class="sr-only-focusable">Skip to main content</a>
```

### Reduced Motion

```html
<!-- Motion safe (respects user preferences) -->
<div class="motion-safe:animate-fade-in">
  Animates only if motion is preferred
</div>

<!-- Reduced motion fallback -->
<div class="reduce-motion">
  Minimal animation for reduced motion users
</div>
```

### High Contrast

```html
<!-- High contrast mode -->
<div class="high-contrast">
  Enhanced contrast for accessibility
</div>

<!-- Contrast safe -->
<div class="contrast-safe">
  Adapts to high contrast preferences
</div>
```

## Performance Considerations

### CSS Optimization

1. **Layer Organization**: Styles are organized in layers for optimal cascade
2. **Utility-First**: Reduces CSS bundle size through utility classes
3. **Purging**: Unused styles are automatically removed in production
4. **Critical CSS**: Base styles are inlined for faster rendering

### Animation Performance

1. **GPU Acceleration**: Animations use `transform` and `opacity` for better performance
2. **Reduced Motion**: Respects user preferences for reduced motion
3. **Efficient Keyframes**: Optimized animation keyframes for smooth performance

### Responsive Performance

1. **Mobile-First**: Styles are built mobile-first for better performance
2. **Container Queries**: Used where supported for more efficient responsive design
3. **Touch Optimization**: Touch-friendly sizes and interactions on mobile devices

## Best Practices

### 1. Use Design Tokens

```css
/* ✅ Good - Using design tokens */
.component {
  color: hsl(var(--primary));
  padding: var(--spacing-4);
}

/* ❌ Avoid - Hardcoded values */
.component {
  color: #3b82f6;
  padding: 16px;
}
```

### 2. Leverage Utility Classes

```html
<!-- ✅ Good - Using utility classes -->
<div class="card-responsive interactive shadow-soft">

<!-- ❌ Avoid - Custom CSS for common patterns -->
<div class="custom-card">
```

### 3. Follow Responsive Patterns

```html
<!-- ✅ Good - Mobile-first responsive -->
<div class="text-sm md:text-base lg:text-lg">

<!-- ❌ Avoid - Desktop-first -->
<div class="text-lg md:text-base sm:text-sm">
```

### 4. Use Semantic Classes

```html
<!-- ✅ Good - Semantic utility combinations -->
<button class="btn-responsive gradient-primary interactive">

<!-- ❌ Avoid - Non-semantic class names -->
<button class="blue-button-large">
```

### 5. Optimize for Performance

```css
/* ✅ Good - GPU-accelerated animations */
.animate-slide {
  transform: translateX(0);
  transition: transform 0.3s ease;
}

/* ❌ Avoid - Layout-triggering animations */
.animate-slide {
  left: 0;
  transition: left 0.3s ease;
}
```

## Debugging and Development

### CSS Custom Properties Inspector

```javascript
// View all CSS custom properties
const styles = getComputedStyle(document.documentElement);
const customProps = Array.from(styles).filter(prop => prop.startsWith('--'));
console.log(customProps);
```

### Responsive Testing

```javascript
// Test responsive breakpoints
const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

Object.entries(breakpoints).forEach(([name, size]) => {
  console.log(`${name}: ${window.matchMedia(`(min-width: ${size})`).matches}`);
});
```

### Animation Performance Monitoring

```javascript
// Monitor animation performance
const observer = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    if (entry.entryType === 'measure') {
      console.log(`Animation: ${entry.name}, Duration: ${entry.duration}ms`);
    }
  });
});

observer.observe({ entryTypes: ['measure'] });
```

## Migration Guide

### From Custom CSS

1. **Identify patterns** in existing CSS
2. **Map to utility classes** where possible
3. **Create custom utilities** for unique patterns
4. **Test responsive behavior** across devices
5. **Validate accessibility** features

### Example Migration

```css
/* Before - Custom CSS */
.old-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  padding: 24px;
  transition: all 0.3s ease;
}

.old-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}
```

```html
<!-- After - Utility classes -->
<div class="card-responsive interactive shadow-soft">
  <!-- Content -->
</div>
```

This styling system provides a comprehensive foundation for building consistent, accessible, and performant user interfaces across the entire application.
