/**
 * Performance profiler for the Living Data Canvas
 */
export class PerformanceProfiler {
  private static instance: PerformanceProfiler;
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled = true;
  private observers: PerformanceObserver[] = [];

  private constructor() {
    this.initializeObservers();
  }

  static getInstance(): PerformanceProfiler {
    if (!PerformanceProfiler.instance) {
      PerformanceProfiler.instance = new PerformanceProfiler();
    }
    return PerformanceProfiler.instance;
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers() {
    if (typeof window === 'undefined') return;

    try {
      // Long task observer
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('long-task', {
            duration: entry.duration,
            startTime: entry.startTime,
            name: entry.name
          });
        }
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.push(longTaskObserver);

      // Layout shift observer
      const layoutShiftObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('layout-shift', {
            value: (entry as any).value,
            startTime: entry.startTime
          });
        }
      });
      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(layoutShiftObserver);

      // Paint observer
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('paint', {
            name: entry.name,
            startTime: entry.startTime,
            duration: entry.duration
          });
        }
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);

    } catch (error) {
      console.warn('Performance observers not supported:', error);
    }
  }

  /**
   * Start timing a performance metric
   */
  startTiming(name: string): void {
    if (!this.isEnabled) return;
    
    const startTime = performance.now();
    this.metrics.set(name, {
      name,
      startTime,
      type: 'timing'
    });
  }

  /**
   * End timing a performance metric
   */
  endTiming(name: string): number {
    if (!this.isEnabled) return 0;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`No timing started for: ${name}`);
      return 0;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;
    
    this.metrics.set(name, {
      ...metric,
      endTime,
      duration,
      completed: true
    });

    return duration;
  }

  /**
   * Record a custom metric
   */
  recordMetric(name: string, data: any): void {
    if (!this.isEnabled) return;

    const timestamp = performance.now();
    const existingMetrics = this.getMetricHistory(name);
    
    this.metrics.set(`${name}_${timestamp}`, {
      name,
      timestamp,
      data,
      type: 'custom'
    });

    // Keep only last 100 entries per metric type
    if (existingMetrics.length > 100) {
      const oldestKey = `${name}_${existingMetrics[0].timestamp}`;
      this.metrics.delete(oldestKey);
    }
  }

  /**
   * Get metric history for a specific metric name
   */
  getMetricHistory(name: string): PerformanceMetric[] {
    return Array.from(this.metrics.values())
      .filter(metric => metric.name === name)
      .sort((a, b) => (a.timestamp || a.startTime) - (b.timestamp || b.startTime));
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): PerformanceSummary {
    const timingMetrics = Array.from(this.metrics.values())
      .filter(metric => metric.type === 'timing' && metric.completed);

    const customMetrics = Array.from(this.metrics.values())
      .filter(metric => metric.type === 'custom');

    const longTasks = customMetrics.filter(metric => metric.name === 'long-task');
    const layoutShifts = customMetrics.filter(metric => metric.name === 'layout-shift');

    return {
      timingMetrics: this.summarizeTimingMetrics(timingMetrics),
      longTaskCount: longTasks.length,
      totalBlockingTime: longTasks.reduce((sum, task) => sum + (task.data?.duration || 0), 0),
      cumulativeLayoutShift: layoutShifts.reduce((sum, shift) => sum + (shift.data?.value || 0), 0),
      memoryUsage: this.getMemoryUsage(),
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * Summarize timing metrics
   */
  private summarizeTimingMetrics(metrics: PerformanceMetric[]): Record<string, TimingMetricSummary> {
    const grouped = metrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = [];
      }
      acc[metric.name].push(metric.duration!);
      return acc;
    }, {} as Record<string, number[]>);

    const summary: Record<string, TimingMetricSummary> = {};
    
    Object.entries(grouped).forEach(([name, durations]) => {
      durations.sort((a, b) => a - b);
      const count = durations.length;
      const sum = durations.reduce((a, b) => a + b, 0);
      
      summary[name] = {
        count,
        average: sum / count,
        median: durations[Math.floor(count / 2)],
        p95: durations[Math.floor(count * 0.95)],
        min: durations[0],
        max: durations[count - 1]
      };
    });

    return summary;
  }

  /**
   * Get memory usage information
   */
  private getMemoryUsage(): MemoryUsage {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      };
    }
    
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
      usagePercentage: 0
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const summary = this.getPerformanceSummary();

    // Check for long tasks
    if (summary.longTaskCount > 5) {
      recommendations.push('Consider breaking down long-running operations into smaller chunks');
    }

    // Check for layout shifts
    if (summary.cumulativeLayoutShift > 0.1) {
      recommendations.push('Reduce layout shifts by reserving space for dynamic content');
    }

    // Check memory usage
    if (summary.memoryUsage.usagePercentage > 80) {
      recommendations.push('High memory usage detected - consider implementing object pooling');
    }

    // Check specific timing metrics
    const renderTime = summary.timingMetrics['canvas-render'];
    if (renderTime && renderTime.average > 16) {
      recommendations.push('Canvas rendering is slow - enable performance optimizations');
    }

    const dataProcessing = summary.timingMetrics['data-processing'];
    if (dataProcessing && dataProcessing.average > 100) {
      recommendations.push('Data processing is slow - consider using web workers');
    }

    return recommendations;
  }

  /**
   * Enable or disable profiling
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics.clear();
  }

  /**
   * Export metrics as JSON
   */
  exportMetrics(): string {
    const data = {
      timestamp: new Date().toISOString(),
      metrics: Array.from(this.metrics.entries()),
      summary: this.getPerformanceSummary()
    };
    return JSON.stringify(data, null, 2);
  }

  /**
   * Cleanup observers
   */
  dispose(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

/**
 * Performance metric interface
 */
interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  timestamp?: number;
  type: 'timing' | 'custom';
  data?: any;
  completed?: boolean;
}

/**
 * Timing metric summary interface
 */
interface TimingMetricSummary {
  count: number;
  average: number;
  median: number;
  p95: number;
  min: number;
  max: number;
}

/**
 * Memory usage interface
 */
interface MemoryUsage {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  usagePercentage: number;
}

/**
 * Performance summary interface
 */
interface PerformanceSummary {
  timingMetrics: Record<string, TimingMetricSummary>;
  longTaskCount: number;
  totalBlockingTime: number;
  cumulativeLayoutShift: number;
  memoryUsage: MemoryUsage;
  recommendations: string[];
}

/**
 * Performance decorator for automatic timing
 */
export function performanceTimer(metricName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const profiler = PerformanceProfiler.getInstance();
      const fullMetricName = `${target.constructor.name}.${propertyName}`;
      
      profiler.startTiming(fullMetricName);
      
      try {
        const result = method.apply(this, args);
        
        // Handle async methods
        if (result && typeof result.then === 'function') {
          return result.finally(() => {
            profiler.endTiming(fullMetricName);
          });
        }
        
        profiler.endTiming(fullMetricName);
        return result;
      } catch (error) {
        profiler.endTiming(fullMetricName);
        throw error;
      }
    };
  };
}

/**
 * Global performance profiler instance
 */
export const performanceProfiler = PerformanceProfiler.getInstance();
