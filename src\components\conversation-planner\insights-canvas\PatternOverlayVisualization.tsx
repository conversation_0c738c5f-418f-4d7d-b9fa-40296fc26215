import React from "react";
import { PatternOverlay } from "@/services/aiCanvasService";
import { TrendingUp, GitBranch, AlertTriangle, Star } from "lucide-react";

interface PatternOverlayVisualizationProps {
  patterns: PatternOverlay[];
  zoom: number;
  pan: { x: number; y: number };
  isVisible: boolean;
}

export const PatternOverlayVisualization: React.FC<PatternOverlayVisualizationProps> = ({
  patterns,
  zoom,
  pan,
  isVisible
}) => {
  if (!isVisible || patterns.length === 0) return null;

  // Safety checks for transform values
  const safeZoom = isFinite(zoom) && zoom > 0 ? zoom : 1;
  const safePanX = isFinite(pan.x) ? pan.x : 0;
  const safePanY = isFinite(pan.y) ? pan.y : 0;

  const getPatternIcon = (type: string) => {
    switch (type) {
      case 'flow': return TrendingUp;
      case 'cluster': return GitBranch;
      case 'gap': return AlertTriangle;
      case 'strength': return Star;
      default: return GitBranch;
    }
  };

  const getPatternColor = (type: string) => {
    switch (type) {
      case 'flow': return '#3B82F6';
      case 'cluster': return '#8B5CF6';
      case 'gap': return '#F59E0B';
      case 'strength': return '#10B981';
      default: return '#6B7280';
    }
  };
  return (
    <div
      className="absolute inset-0 pointer-events-none"
      style={{
        transform: `translate(${safePanX}px, ${safePanY}px) scale(${safeZoom})`,
        transformOrigin: '0 0',
      }}
    >
      {patterns.map((pattern) => {
        const Icon = getPatternIcon(pattern.type);
        const color = getPatternColor(pattern.type);
        
        return (          <div
            key={pattern.id}
            className="absolute border-dashed rounded-lg opacity-40"
            style={{
              left: isFinite(pattern.coordinates.x) ? pattern.coordinates.x : 0,
              top: isFinite(pattern.coordinates.y) ? pattern.coordinates.y : 0,
              width: isFinite(pattern.coordinates.width) ? pattern.coordinates.width : 0,
              height: isFinite(pattern.coordinates.height) ? pattern.coordinates.height : 0,
              borderColor: color,
              backgroundColor: `${color}10`,
              borderWidth: `${2 / safeZoom}px`,
            }}
          >
            <div
              className="absolute -top-8 left-2 flex items-center gap-1 px-2 py-1 rounded text-xs font-medium whitespace-nowrap"
              style={{
                backgroundColor: color,
                color: 'white',
                transform: `scale(${1 / safeZoom})`,
                transformOrigin: 'top left',
                top: `${-2.2 / safeZoom}rem`,
              }}
            >
              <Icon className="h-3 w-3" />
              {pattern.description}
            </div>
          </div>
        );
      })}
    </div>
  );
};
