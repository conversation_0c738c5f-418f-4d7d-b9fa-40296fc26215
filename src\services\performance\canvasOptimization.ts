import * as THREE from 'three';
import { SimpleNode, VisualConnection } from '@/components/visual-analysis/types';

/**
 * Performance optimization utilities for the canvas
 */
export class CanvasOptimization {
  private static readonly MAX_VISIBLE_NODES = 1000;
  private static readonly MAX_VISIBLE_CONNECTIONS = 500;
  private static readonly LOD_DISTANCE_THRESHOLD = 50;
  private static readonly FRUSTUM_CULLING_ENABLED = true;

  /**
   * Level of Detail (LOD) management for nodes
   */
  static applyNodeLOD(
    nodes: Map<string, SimpleNode>, 
    camera: THREE.Camera,
    viewportSize: { width: number; height: number }
  ): void {
    const cameraPosition = camera.position;
    const frustum = new THREE.Frustum();
    const matrix = new THREE.Matrix4().multiplyMatrices(
      camera.projectionMatrix, 
      camera.matrixWorldInverse
    );
    frustum.setFromProjectionMatrix(matrix);

    nodes.forEach((node) => {
      const distance = cameraPosition.distanceTo(
        new THREE.Vector3(node.position.x, node.position.y, 0)
      );

      // Frustum culling
      if (this.FRUSTUM_CULLING_ENABLED) {
        const nodePosition = new THREE.Vector3(node.position.x, node.position.y, 0);
        const isInFrustum = frustum.containsPoint(nodePosition);
        node.mesh.visible = isInFrustum;
        
        if (!isInFrustum) return;
      }

      // Distance-based LOD
      if (distance > this.LOD_DISTANCE_THRESHOLD) {
        // Far distance - simplified rendering
        this.applyLowDetailNode(node);
      } else {
        // Close distance - full detail rendering
        this.applyHighDetailNode(node);
      }

      // Scale based on distance for better visibility
      const scale = Math.max(0.5, Math.min(2.0, this.LOD_DISTANCE_THRESHOLD / distance));
      node.mesh.scale.setScalar(scale);
    });
  }

  /**
   * Apply low detail rendering for distant nodes
   */
  private static applyLowDetailNode(node: SimpleNode): void {
    // Reduce geometry complexity
    if (node.mesh.geometry instanceof THREE.SphereGeometry) {
      // Replace with simpler geometry if needed
      const material = node.mesh.material as THREE.MeshBasicMaterial;
      material.wireframe = false;
    }

    // Disable expensive material features
    const material = node.mesh.material as THREE.Material;
    if ('transparent' in material) {
      material.transparent = false;
    }
  }

  /**
   * Apply high detail rendering for close nodes
   */
  private static applyHighDetailNode(node: SimpleNode): void {
    // Restore full geometry detail
    const material = node.mesh.material as THREE.Material;
    if ('transparent' in material) {
      material.transparent = true;
    }
  }

  /**
   * Connection culling and optimization
   */
  static optimizeConnections(
    connections: Map<string, VisualConnection>,
    camera: THREE.Camera,
    nodes: Map<string, SimpleNode>
  ): void {
    const cameraPosition = camera.position;
    let visibleConnectionCount = 0;

    connections.forEach((connection) => {
      // Skip if we've reached the maximum visible connections
      if (visibleConnectionCount >= this.MAX_VISIBLE_CONNECTIONS) {
        connection.lineMesh.visible = false;
        return;
      }

      // Check if both nodes are visible
      const sourceNode = nodes.get(connection.sourceNode.id);
      const targetNode = nodes.get(connection.targetNode.id);
      
      if (!sourceNode?.mesh.visible || !targetNode?.mesh.visible) {
        connection.lineMesh.visible = false;
        return;
      }

      // Distance-based culling
      const sourceDistance = cameraPosition.distanceTo(
        new THREE.Vector3(sourceNode.position.x, sourceNode.position.y, 0)
      );
      const targetDistance = cameraPosition.distanceTo(
        new THREE.Vector3(targetNode.position.x, targetNode.position.y, 0)
      );
      const avgDistance = (sourceDistance + targetDistance) / 2;

      if (avgDistance > this.LOD_DISTANCE_THRESHOLD * 1.5) {
        connection.lineMesh.visible = false;
        return;
      }

      connection.lineMesh.visible = true;
      visibleConnectionCount++;

      // Simplify line rendering for distant connections
      if (avgDistance > this.LOD_DISTANCE_THRESHOLD) {
        const material = connection.lineMesh.material as THREE.LineBasicMaterial;
        material.linewidth = 1;
        material.opacity = 0.3;
      } else {
        const material = connection.lineMesh.material as THREE.LineBasicMaterial;
        material.linewidth = 2;
        material.opacity = 0.8;
      }
    });
  }

  /**
   * Implement object pooling for frequently created/destroyed objects
   */
  static createObjectPool<T>(
    createFn: () => T,
    resetFn: (obj: T) => void,
    initialSize: number = 50
  ): ObjectPool<T> {
    return new ObjectPool(createFn, resetFn, initialSize);
  }

  /**
   * Batch geometry updates to reduce draw calls
   */
  static batchGeometryUpdates(
    nodes: Map<string, SimpleNode>,
    updateFn: (node: SimpleNode) => void
  ): void {
    // Disable auto-update for all geometries
    nodes.forEach((node) => {
      if (node.mesh.geometry) {
        node.mesh.geometry.computeBoundingSphere();
      }
    });

    // Apply updates
    nodes.forEach(updateFn);

    // Re-enable auto-update and trigger updates
    nodes.forEach((node) => {
      if (node.mesh.geometry) {
        node.mesh.geometry.computeBoundingSphere();
      }
    });
  }

  /**
   * Memory management - dispose unused resources
   */
  static cleanupResources(
    scene: THREE.Scene,
    nodesToKeep: Set<string>,
    connectionsToKeep: Set<string>
  ): void {
    const objectsToRemove: THREE.Object3D[] = [];

    scene.traverse((object) => {
      if (object.userData.nodeId && !nodesToKeep.has(object.userData.nodeId)) {
        objectsToRemove.push(object);
      }
      if (object.userData.connectionId && !connectionsToKeep.has(object.userData.connectionId)) {
        objectsToRemove.push(object);
      }
    });

    objectsToRemove.forEach((object) => {
      scene.remove(object);
      
      // Dispose geometry and materials
      if ('geometry' in object && object.geometry) {
        object.geometry.dispose();
      }
      if ('material' in object && object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach(material => material.dispose());
        } else {
          object.material.dispose();
        }
      }
    });
  }

  /**
   * Optimize rendering for large datasets
   */
  static optimizeForLargeDataset(
    nodeCount: number,
    connectionCount: number
  ): {
    useInstancing: boolean;
    maxVisibleNodes: number;
    maxVisibleConnections: number;
    enableLOD: boolean;
    enableFrustumCulling: boolean;
  } {
    return {
      useInstancing: nodeCount > 500,
      maxVisibleNodes: Math.min(nodeCount, this.MAX_VISIBLE_NODES),
      maxVisibleConnections: Math.min(connectionCount, this.MAX_VISIBLE_CONNECTIONS),
      enableLOD: nodeCount > 200,
      enableFrustumCulling: nodeCount > 100
    };
  }

  /**
   * Performance monitoring
   */
  static createPerformanceMonitor(): PerformanceMonitor {
    return new PerformanceMonitor();
  }
}

/**
 * Object pool for reusing objects
 */
class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;

  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize: number) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(createFn());
    }
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }

  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }

  size(): number {
    return this.pool.length;
  }
}

/**
 * Performance monitoring utility
 */
class PerformanceMonitor {
  private frameCount = 0;
  private lastTime = performance.now();
  private fps = 0;
  private frameTime = 0;
  private memoryUsage = 0;

  update(): void {
    const currentTime = performance.now();
    this.frameTime = currentTime - this.lastTime;
    this.lastTime = currentTime;
    this.frameCount++;

    // Calculate FPS every second
    if (this.frameCount % 60 === 0) {
      this.fps = 1000 / this.frameTime;
    }

    // Monitor memory usage (if available)
    if ('memory' in performance) {
      this.memoryUsage = (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
  }

  getMetrics(): {
    fps: number;
    frameTime: number;
    memoryUsage: number;
    frameCount: number;
  } {
    return {
      fps: Math.round(this.fps),
      frameTime: Math.round(this.frameTime * 100) / 100,
      memoryUsage: Math.round(this.memoryUsage * 100) / 100,
      frameCount: this.frameCount
    };
  }

  shouldOptimize(): boolean {
    return this.fps < 30 || this.frameTime > 33; // 30 FPS threshold
  }
}
