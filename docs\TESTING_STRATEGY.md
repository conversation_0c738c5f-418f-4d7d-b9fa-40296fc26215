# Testing Strategy

## Overview

This document outlines the comprehensive testing strategy for the AI Question Analyzer application, covering unit tests, integration tests, performance tests, and end-to-end testing approaches.

## Testing Philosophy

### Testing Pyramid

1. **Unit Tests (70%)**: Fast, isolated tests for individual components and functions
2. **Integration Tests (20%)**: Tests for component interactions and data flow
3. **End-to-End Tests (10%)**: Full user journey testing

### Testing Principles

- **Test Behavior, Not Implementation**: Focus on what the component does, not how it does it
- **Arrange, Act, Assert**: Structure tests clearly with setup, execution, and verification
- **Fail Fast**: Tests should fail quickly and provide clear error messages
- **Maintainable**: Tests should be easy to read, write, and maintain

## Unit Testing

### Component Testing

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { Button } from '@/components/ui/button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('calls onClick handler when clicked', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state correctly', () => {
    render(<Button loading>Loading</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('applies correct variant styles', () => {
    render(<Button variant="destructive">Delete</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-destructive');
  });
});
```

### Hook Testing

```typescript
import { renderHook, act } from '@testing-library/react';
import { useDebounce } from '@/utils/performance';

describe('useDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('debounces value updates', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    );

    expect(result.current).toBe('initial');

    rerender({ value: 'updated', delay: 500 });
    expect(result.current).toBe('initial');

    act(() => {
      vi.advanceTimersByTime(500);
    });

    expect(result.current).toBe('updated');
  });
});
```

### Store Testing

```typescript
import { renderHook, act } from '@testing-library/react';
import { useSettingsStore } from '@/stores/useSettingsStore';

describe('useSettingsStore', () => {
  beforeEach(() => {
    useSettingsStore.getState().reset();
  });

  it('updates settings correctly', async () => {
    const { result } = renderHook(() => useSettingsStore());

    await act(async () => {
      await result.current.updateSettings({ numberOfAnswers: 5 });
    });

    expect(result.current.settings.numberOfAnswers).toBe(5);
    expect(result.current.error).toBeNull();
  });

  it('handles validation errors', async () => {
    const { result } = renderHook(() => useSettingsStore());

    await act(async () => {
      await result.current.updateSettings({ numberOfAnswers: 15 });
    });

    expect(result.current.error).toContain('Number of answers must be between 1 and 10');
  });
});
```

## Integration Testing

### Component Integration

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConversationPlanner } from '@/components/ConversationPlanner';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('ConversationPlanner Integration', () => {
  it('completes full analysis workflow', async () => {
    renderWithProviders(<ConversationPlanner />);

    // Enter question
    const questionInput = screen.getByPlaceholderText(/enter your question/i);
    fireEvent.change(questionInput, { target: { value: 'Test question' } });

    // Submit analysis
    const analyzeButton = screen.getByRole('button', { name: /analyze/i });
    fireEvent.click(analyzeButton);

    // Wait for results
    await waitFor(() => {
      expect(screen.getByText(/analysis results/i)).toBeInTheDocument();
    });

    // Verify results are displayed
    expect(screen.getByText(/test question/i)).toBeInTheDocument();
  });
});
```

### API Integration

```typescript
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { analyzeQuestion } from '@/services/api';

const server = setupServer(
  rest.post('/api/analyze', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          analysis: 'Test analysis result',
          type: 'multiple',
          rating: 8,
        },
      })
    );
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('API Integration', () => {
  it('analyzes question successfully', async () => {
    const result = await analyzeQuestion('Test question', {
      apiKey: 'test-key',
      model: 'gpt-4',
    });

    expect(result.success).toBe(true);
    expect(result.data.analysis).toBe('Test analysis result');
  });

  it('handles API errors gracefully', async () => {
    server.use(
      rest.post('/api/analyze', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ error: 'Server error' }));
      })
    );

    await expect(analyzeQuestion('Test question', {})).rejects.toThrow('Server error');
  });
});
```

## Performance Testing

### Render Performance

```typescript
import { render } from '@testing-library/react';
import { performance } from 'perf_hooks';
import { LargeDataComponent } from '@/components/LargeDataComponent';

describe('Performance Tests', () => {
  it('renders large dataset within performance budget', () => {
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      value: Math.random(),
    }));

    const startTime = performance.now();
    render(<LargeDataComponent data={largeDataset} />);
    const endTime = performance.now();

    const renderTime = endTime - startTime;
    expect(renderTime).toBeLessThan(100); // 100ms budget
  });

  it('handles rapid state updates efficiently', async () => {
    const { rerender } = render(<LargeDataComponent data={[]} />);

    const startTime = performance.now();
    
    for (let i = 0; i < 100; i++) {
      rerender(<LargeDataComponent data={[{ id: i, name: `Item ${i}` }]} />);
    }

    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    expect(totalTime).toBeLessThan(500); // 500ms for 100 updates
  });
});
```

### Memory Leak Testing

```typescript
describe('Memory Leak Tests', () => {
  it('cleans up subscriptions on unmount', () => {
    const mockSubscription = { unsubscribe: vi.fn() };
    const mockService = { subscribe: vi.fn(() => mockSubscription) };

    const { unmount } = render(
      <ComponentWithSubscription service={mockService} />
    );

    expect(mockService.subscribe).toHaveBeenCalled();

    unmount();

    expect(mockSubscription.unsubscribe).toHaveBeenCalled();
  });
});
```

## End-to-End Testing

### User Journey Testing

```typescript
// Using Playwright
import { test, expect } from '@playwright/test';

test.describe('Question Analysis Flow', () => {
  test('user can analyze a question and view results', async ({ page }) => {
    await page.goto('/');

    // Enter API key
    await page.fill('[data-testid="api-key-input"]', 'test-api-key');

    // Enter question
    await page.fill('[data-testid="question-input"]', 'What is the meaning of life?');

    // Select analysis type
    await page.selectOption('[data-testid="analysis-type"]', 'deep');

    // Submit analysis
    await page.click('[data-testid="analyze-button"]');

    // Wait for results
    await expect(page.locator('[data-testid="analysis-results"]')).toBeVisible();

    // Verify results content
    await expect(page.locator('[data-testid="analysis-text"]')).toContainText('meaning of life');
  });

  test('user can save and load analysis', async ({ page }) => {
    await page.goto('/');

    // Perform analysis (abbreviated)
    await page.fill('[data-testid="api-key-input"]', 'test-api-key');
    await page.fill('[data-testid="question-input"]', 'Test question');
    await page.click('[data-testid="analyze-button"]');

    // Save analysis
    await page.click('[data-testid="save-analysis-button"]');
    await page.fill('[data-testid="analysis-title-input"]', 'My Test Analysis');
    await page.click('[data-testid="confirm-save-button"]');

    // Verify saved
    await expect(page.locator('[data-testid="save-success-message"]')).toBeVisible();

    // Load saved analysis
    await page.click('[data-testid="saved-analyses-button"]');
    await page.click('[data-testid="analysis-item"]:has-text("My Test Analysis")');

    // Verify loaded
    await expect(page.locator('[data-testid="question-input"]')).toHaveValue('Test question');
  });
});
```

### Accessibility Testing

```typescript
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Accessibility Tests', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<ConversationPlanner />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('supports keyboard navigation', async () => {
    render(<ConversationPlanner />);
    
    const questionInput = screen.getByRole('textbox', { name: /question/i });
    const analyzeButton = screen.getByRole('button', { name: /analyze/i });

    // Tab navigation
    questionInput.focus();
    fireEvent.keyDown(questionInput, { key: 'Tab' });
    expect(analyzeButton).toHaveFocus();

    // Enter key activation
    fireEvent.keyDown(analyzeButton, { key: 'Enter' });
    // Verify analysis starts
  });
});
```

## Test Configuration

### Vitest Configuration

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
```

### Test Setup

```typescript
// src/test/setup.ts
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});
```

### Test Utilities

```typescript
// src/test/utils.tsx
import { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="light">
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };
```

## Testing Best Practices

### 1. Test Organization

```typescript
describe('Component Name', () => {
  describe('rendering', () => {
    it('renders correctly with default props', () => {});
    it('renders correctly with custom props', () => {});
  });

  describe('interactions', () => {
    it('handles click events', () => {});
    it('handles keyboard events', () => {});
  });

  describe('edge cases', () => {
    it('handles empty data', () => {});
    it('handles error states', () => {});
  });
});
```

### 2. Test Data Management

```typescript
// src/test/fixtures.ts
export const mockUser = {
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
};

export const mockAnalysisResult = {
  id: '1',
  question: 'Test question',
  analysis: 'Test analysis',
  type: 'multiple',
  rating: 8,
  timestamp: Date.now(),
};

export const createMockAnalysisResult = (overrides = {}) => ({
  ...mockAnalysisResult,
  ...overrides,
});
```

### 3. Async Testing

```typescript
it('handles async operations', async () => {
  const mockFetch = vi.fn().mockResolvedValue({
    ok: true,
    json: () => Promise.resolve({ data: 'test' }),
  });

  global.fetch = mockFetch;

  render(<AsyncComponent />);

  await waitFor(() => {
    expect(screen.getByText('test')).toBeInTheDocument();
  });

  expect(mockFetch).toHaveBeenCalledWith('/api/data');
});
```

## Continuous Integration

### GitHub Actions

```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:coverage
      - run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

This comprehensive testing strategy ensures high code quality, reliability, and maintainability across the entire application.
