// Core visualization components
export { ChainNode } from './ChainNode';
export { ChainConnector, StraightConnector } from './ChainConnector';
export { VisualizationChain } from './VisualizationChain';

// Layout components
export { AnalysisColumnsView } from './AnalysisColumnsView';
export { ViewToggleManager } from './ViewToggleManager';

// Flow diagram components
export { AnalysisFlowDiagram } from './AnalysisFlowDiagram';

// Interactive features
export { InteractiveOverlay } from './InteractiveOverlay';

// Theme system
export { ThemeProvider, useTheme, useThemedStyles } from './ThemeProvider';
export * from './themes';

// Performance optimization
export * from './PerformanceOptimizer';

// Responsive design
export { ResponsiveVisualizationWrapper } from './ResponsiveVisualizationWrapper';

// Main wrapper
export { AnalysisVisualizationWrapper } from './AnalysisVisualizationWrapper';

// Types (re-exported for convenience)
export type {
  ChainNode as ChainNodeType,
  ChainConnection,
  Visualization<PERSON>hain as VisualizationChainType,
  VisualizationConfig,
  ViewMode,
  AnalysisVisualizationData
} from '@/types/visualization';

export { createVisualizationChain } from '@/types/visualization';
