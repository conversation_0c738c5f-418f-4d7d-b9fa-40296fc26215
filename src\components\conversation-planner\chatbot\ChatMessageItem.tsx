
import React, { memo } from "react";
import { ChatMessage } from "./types";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { ShieldAlert } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface ChatMessageItemProps {
  message: ChatMessage;
  isLiveFeedbackEnabled: boolean;
  isSelected?: boolean;
  onSelectMessage?: (messageId: string) => void;
  // onRateMessage removed (no manual rating)
}

const FeedbackBar = ({ score }: { score: number }) => (
  <div className="flex items-center gap-3 mt-3 min-w-[140px]">
    <div className="flex-1 bg-amber-100 rounded-full h-3 relative overflow-hidden shadow-inner">
      <div
        className="h-3 rounded-full transition-all duration-700 ease-out"
        style={{
          width: `${score}%`,
          backgroundColor:
            score > 85
              ? "#10b981" // green-500
              : score > 65
              ? "#f59e0b" // amber-500
              : "#ef4444", // red-500
        }}
      />
    </div>
    <span
      className={`text-sm font-bold px-2 py-1 rounded-lg`}
      style={{
        backgroundColor:
          score > 85
            ? "#dcfce7" // green-100
            : score > 65
            ? "#fef3c7" // amber-100
            : "#fee2e2", // red-100
        color:
          score > 85
            ? "#166534" // green-800
            : score > 65
            ? "#92400e" // amber-800
            : "#991b1b", // red-800
      }}
    >
      {score}%
    </span>
  </div>
);

const FeedbackDetail = ({
  detail,
}: {
  detail?: {
    communication: number;
    social: number;
    psychological: number;
    gender_cultural: number;
  };
}) => {
  if (!detail) return null;
  return (
    <div className="grid grid-cols-2 gap-3 mt-3 mb-3 p-3 bg-amber-100/50 rounded-lg border border-amber-200">
      <div className="flex items-center justify-between">
        <span className="text-xs text-amber-700 font-medium">Communication:</span>
        <span className="text-xs font-bold text-amber-800 bg-white px-2 py-1 rounded">{detail.communication}%</span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-xs text-amber-700 font-medium">Social:</span>
        <span className="text-xs font-bold text-amber-800 bg-white px-2 py-1 rounded">{detail.social}%</span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-xs text-amber-700 font-medium">Psychological:</span>
        <span className="text-xs font-bold text-amber-800 bg-white px-2 py-1 rounded">{detail.psychological}%</span>
      </div>
      <div className="flex items-center justify-between">
        <span className="text-xs text-amber-700 font-medium">Cultural:</span>
        <span className="text-xs font-bold text-amber-800 bg-white px-2 py-1 rounded">{detail.gender_cultural}%</span>
      </div>
    </div>
  );
};

const BiasAnalysisFeedback = ({
  bias,
}: {
  bias: { name: string; explanation: string };
}) => {
  return (
    <>
      <Separator className="my-3 bg-amber-200" />
      <div className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
        <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
          <ShieldAlert className="h-4 w-4 text-red-600" />
        </div>
        <div>
          <h4 className="text-sm font-bold text-red-800 mb-1">
            Bias Analysis: <span className="font-bold">{bias.name}</span>
          </h4>
          <p className="text-sm text-red-700 whitespace-pre-wrap leading-relaxed">
            {bias.explanation}
          </p>
        </div>
      </div>
    </>
  );
};

const ChatMessageItem: React.FC<ChatMessageItemProps> = ({
  message,
  isLiveFeedbackEnabled,
  isSelected,
  onSelectMessage,
}) => {
  // Only show auto-feedback for user messages if enabled
  const showFeedback =
    isLiveFeedbackEnabled &&
    message.role === "user" &&
    message.autoFeedback &&
    (message.autoFeedback.isLoading ||
      message.performanceScore !== undefined ||
      (message.autoFeedback.text && message.autoFeedback.text !== ""));

  return (
    <div
      className={`flex items-start gap-4 ${
        message.role === "user" ? "justify-end" : "justify-start"
      }`}
    >
      {onSelectMessage && message.role === "assistant" && (
        <div className="pt-3">
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onSelectMessage(message.id)}
            className="border-slate-300"
          />
        </div>
      )}
      <div
        className={`flex flex-col max-w-[85%] ${
          message.role === "user" ? "items-end" : "items-start"
        }`}
      >
        <div
          className={`w-fit max-w-full p-4 rounded-2xl shadow-sm ${
            message.role === "user"
              ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-br-lg"
              : "bg-white border border-slate-200 text-slate-800 rounded-bl-lg"
          }`}
        >
          <div className="whitespace-pre-wrap leading-relaxed">{message.content}</div>
          <div
            className={`text-xs mt-2 ${
              message.role === "user" ? "text-blue-100" : "text-slate-500"
            }`}
          >
            {message.timestamp.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
        </div>
        {showFeedback && (
          <Card className="mt-3 w-full max-w-sm bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-200 shadow-sm rounded-xl">
            <CardContent className="p-4">
              {message.autoFeedback?.isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-amber-500 border-t-transparent rounded-full animate-spin"></div>
                  <p className="text-sm text-amber-700 font-medium">
                    Analyzing your message...
                  </p>
                </div>
              ) : (
                <>
                  {typeof message.performanceScore === "number" && (
                    <FeedbackBar score={message.performanceScore} />
                  )}
                  <FeedbackDetail detail={message.autoFeedback?.detail} />
                  {message.autoFeedback?.text && (
                    <>
                      <h4 className="text-sm font-bold text-amber-800 mb-2 mt-2 flex items-center gap-2">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        Coaching Feedback
                      </h4>
                      <p className="text-sm text-amber-700 whitespace-pre-wrap leading-relaxed">
                        {message.autoFeedback.text}
                      </p>
                    </>
                  )}
                  {message.autoFeedback?.biasAnalysis && (
                    <BiasAnalysisFeedback bias={message.autoFeedback.biasAnalysis} />
                  )}
                </>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default memo(ChatMessageItem);
