
import { AIModel } from "@/data/aiModels";

export class ModelFetchService {
  static async fetchModels(apiKey: string): Promise<AIModel[]> {
    if (!apiKey?.trim()) {
      throw new Error("API key is required to fetch models");
    }

    try {
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.href,
          'X-Title': 'ChatCraft Trainer Pro - Models'
        },
      });

      if (!response.ok) {
        let errorMessage = `Failed to fetch models (${response.status}): ${response.statusText}`;
        
        // Provide more specific error messages
        switch (response.status) {
          case 401:
            errorMessage = 'Invalid API key. Cannot fetch available models.';
            break;
          case 403:
            errorMessage = 'Access forbidden. Check API key permissions for model access.';
            break;
          case 429:
            errorMessage = 'Rate limit exceeded while fetching models. Please try again later.';
            break;
          case 500:
          case 502:
          case 503:
          case 504:
            errorMessage = 'OpenRouter service temporarily unavailable. Cannot fetch models.';
            break;
        }

        console.error('Model fetch error:', {
          status: response.status,
          statusText: response.statusText
        });

        throw new Error(errorMessage);
      }

      const data = await response.json();
      
      if (!data || !data.data || !Array.isArray(data.data)) {
        throw new Error('Invalid response format from models API');
      }

      if (data.data.length === 0) {
        console.warn('No models available from OpenRouter API');
        return [];
      }
      
      // Transform OpenRouter API response to our AIModel format with error handling
      return data.data
        .map((model: any) => {
          try {
            if (!model || !model.id) {
              console.warn('Skipping model with missing ID:', model);
              return null;
            }

            return {
              value: model.id,
              label: model.name || model.id,
              provider: this.extractProvider(model.id),
              type: model.pricing?.prompt === "0" ? "free" : "premium"
            };
          } catch (modelError) {
            console.warn('Error processing model:', model, modelError);
            return null;
          }
        })
        .filter((model): model is AIModel => model !== null);
    } catch (error) {
      console.error('Failed to fetch models from OpenRouter:', error);
      
      if (error instanceof Error && error.message.includes('fetch')) {
        throw new Error('Network error while fetching models. Please check your connection.');
      }
      
      throw error;
    }
  }

  private static extractProvider(modelId: string): string {
    if (modelId.includes('openai') || modelId.includes('gpt')) return 'OpenAI';
    if (modelId.includes('anthropic') || modelId.includes('claude')) return 'Anthropic';
    if (modelId.includes('meta') || modelId.includes('llama')) return 'Meta';
    if (modelId.includes('microsoft') || modelId.includes('phi')) return 'Microsoft';
    if (modelId.includes('google') || modelId.includes('gemini')) return 'Google';
    if (modelId.includes('mistral')) return 'Mistral';
    if (modelId.includes('cohere')) return 'Cohere';
    
    // Extract provider from model ID format (provider/model-name)
    const parts = modelId.split('/');
    if (parts.length > 1) {
      return parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    }
    
    return 'Other';
  }
}
