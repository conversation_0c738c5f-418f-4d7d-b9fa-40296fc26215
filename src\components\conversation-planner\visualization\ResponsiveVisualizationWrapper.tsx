import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Eye, 
  EyeOff,
  Maximize2,
  Minimize2,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ResponsiveVisualizationWrapperProps {
  children: React.ReactNode;
  className?: string;
  enableMobileOptimization?: boolean;
}

type ScreenSize = 'mobile' | 'tablet' | 'desktop';
type ViewportOrientation = 'portrait' | 'landscape';

interface ResponsiveConfig {
  screenSize: ScreenSize;
  orientation: ViewportOrientation;
  width: number;
  height: number;
  isTouchDevice: boolean;
}

const useResponsiveConfig = (): ResponsiveConfig => {
  const [config, setConfig] = useState<ResponsiveConfig>({
    screenSize: 'desktop',
    orientation: 'landscape',
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
    isTouchDevice: false
  });

  useEffect(() => {
    const updateConfig = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      let screenSize: ScreenSize = 'desktop';
      if (width < 768) {
        screenSize = 'mobile';
      } else if (width < 1024) {
        screenSize = 'tablet';
      }

      const orientation: ViewportOrientation = width > height ? 'landscape' : 'portrait';
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

      setConfig({
        screenSize,
        orientation,
        width,
        height,
        isTouchDevice
      });
    };

    updateConfig();
    window.addEventListener('resize', updateConfig);
    window.addEventListener('orientationchange', updateConfig);

    return () => {
      window.removeEventListener('resize', updateConfig);
      window.removeEventListener('orientationchange', updateConfig);
    };
  }, []);

  return config;
};

interface MobileOptimizationControlsProps {
  config: ResponsiveConfig;
  compactMode: boolean;
  onToggleCompact: () => void;
  simplifiedView: boolean;
  onToggleSimplified: () => void;
  onRotateHint: () => void;
}

const MobileOptimizationControls: React.FC<MobileOptimizationControlsProps> = ({
  config,
  compactMode,
  onToggleCompact,
  simplifiedView,
  onToggleSimplified,
  onRotateHint
}) => {
  if (config.screenSize === 'desktop') return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-4"
    >
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {config.screenSize === 'mobile' && <Smartphone className="w-4 h-4 text-blue-600" />}
              {config.screenSize === 'tablet' && <Tablet className="w-4 h-4 text-blue-600" />}
              <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                {config.screenSize} • {config.orientation}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant={compactMode ? "default" : "outline"}
                size="sm"
                onClick={onToggleCompact}
                className="text-xs h-7"
              >
                {compactMode ? <Minimize2 className="w-3 h-3" /> : <Maximize2 className="w-3 h-3" />}
                {compactMode ? 'Compact' : 'Normal'}
              </Button>
              
              <Button
                variant={simplifiedView ? "default" : "outline"}
                size="sm"
                onClick={onToggleSimplified}
                className="text-xs h-7"
              >
                {simplifiedView ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                {simplifiedView ? 'Simple' : 'Full'}
              </Button>
              
              {config.screenSize === 'mobile' && config.orientation === 'portrait' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRotateHint}
                  className="text-xs h-7"
                >
                  <RotateCcw className="w-3 h-3" />
                  Rotate
                </Button>
              )}
            </div>
          </div>
          
          {config.screenSize === 'mobile' && config.orientation === 'portrait' && (
            <div className="mt-2 text-xs text-blue-700 bg-blue-100 p-2 rounded">
              💡 Tip: Rotate to landscape for better visualization experience
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

interface ResponsiveLayoutProps {
  config: ResponsiveConfig;
  compactMode: boolean;
  simplifiedView: boolean;
  children: React.ReactNode;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  config,
  compactMode,
  simplifiedView,
  children
}) => {
  const getLayoutClasses = () => {
    const baseClasses = "w-full";
    
    switch (config.screenSize) {
      case 'mobile':
        return cn(
          baseClasses,
          compactMode ? "text-xs" : "text-sm",
          "px-2 py-1"
        );
      case 'tablet':
        return cn(
          baseClasses,
          compactMode ? "text-sm" : "text-base",
          "px-4 py-2"
        );
      default:
        return cn(baseClasses, "text-base px-6 py-4");
    }
  };

  const getContainerStyle = () => {
    if (config.screenSize === 'mobile') {
      return {
        maxHeight: config.orientation === 'portrait' ? '70vh' : '60vh',
        overflowY: 'auto' as const,
        overflowX: 'hidden' as const
      };
    }
    return {};
  };

  return (
    <div 
      className={getLayoutClasses()}
      style={getContainerStyle()}
    >
      {/* Mobile-specific optimizations */}
      {config.screenSize === 'mobile' && (
        <style jsx>{`
          /* Optimize touch targets */
          button {
            min-height: 44px;
            min-width: 44px;
          }
          
          /* Improve text readability */
          .visualization-text {
            line-height: 1.6;
            font-size: ${compactMode ? '14px' : '16px'};
          }
          
          /* Optimize spacing */
          .visualization-spacing {
            margin: ${compactMode ? '8px' : '12px'};
            padding: ${compactMode ? '8px' : '12px'};
          }
        `}</style>
      )}
      
      {children}
    </div>
  );
};

export const ResponsiveVisualizationWrapper: React.FC<ResponsiveVisualizationWrapperProps> = ({
  children,
  className,
  enableMobileOptimization = true
}) => {
  const config = useResponsiveConfig();
  const [compactMode, setCompactMode] = useState(config.screenSize === 'mobile');
  const [simplifiedView, setSimplifiedView] = useState(config.screenSize === 'mobile');
  const [showRotateHint, setShowRotateHint] = useState(false);

  // Auto-adjust settings based on screen size
  useEffect(() => {
    if (config.screenSize === 'mobile') {
      setCompactMode(true);
      setSimplifiedView(true);
    } else if (config.screenSize === 'tablet') {
      setCompactMode(false);
      setSimplifiedView(false);
    } else {
      setCompactMode(false);
      setSimplifiedView(false);
    }
  }, [config.screenSize]);

  const handleRotateHint = () => {
    setShowRotateHint(true);
    setTimeout(() => setShowRotateHint(false), 3000);
  };

  // Clone children with responsive props
  const enhancedChildren = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        ...child.props,
        compact: compactMode,
        simplified: simplifiedView,
        responsive: true,
        screenSize: config.screenSize,
        isTouchDevice: config.isTouchDevice
      } as any);
    }
    return child;
  });

  return (
    <div className={cn("relative", className)}>
      {enableMobileOptimization && (
        <MobileOptimizationControls
          config={config}
          compactMode={compactMode}
          onToggleCompact={() => setCompactMode(!compactMode)}
          simplifiedView={simplifiedView}
          onToggleSimplified={() => setSimplifiedView(!simplifiedView)}
          onRotateHint={handleRotateHint}
        />
      )}
      
      <ResponsiveLayout
        config={config}
        compactMode={compactMode}
        simplifiedView={simplifiedView}
      >
        {enhancedChildren}
      </ResponsiveLayout>
      
      {/* Rotate hint overlay */}
      {showRotateHint && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
        >
          <Card className="bg-white p-6 m-4 text-center">
            <CardContent>
              <RotateCcw className="w-12 h-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-lg font-semibold mb-2">Rotate Your Device</h3>
              <p className="text-sm text-gray-600">
                For the best visualization experience, please rotate your device to landscape mode.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
};
