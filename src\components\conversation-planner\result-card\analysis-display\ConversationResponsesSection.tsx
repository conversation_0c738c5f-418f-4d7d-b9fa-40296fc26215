
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare } from "lucide-react";
import { QuestionContext } from "@/components/conversation-planner/QuestionContextSelector";
import { formatInlineContent } from "./ContentFormatter";
import { Checkbox } from "@/components/ui/checkbox";

interface ConversationResponsesSectionProps {
  responses: Array<{ number: number; text: string }>;
  questionContext: QuestionContext;
  onAnswerSelect: (answer: string, answerNumber: number) => void;
  onItemToggleSelect?: (answer: string) => void;
  isItemSelected?: (answer: string) => boolean;
}

export const ConversationResponsesSection: React.FC<ConversationResponsesSectionProps> = ({
  responses,
  questionContext,
  onAnswerSelect,
  onItemToggleSelect,
  isItemSelected,
}) => {
  const getMainSectionTitle = () => {
    return questionContext === 'asking' ? "Possible Responses You Might Hear" : "Ways You Could Respond";
  };
  
  const getMainSectionDescription = () => {
    return questionContext === 'asking' 
      ? "Here are realistic responses the other person might give:"
      : "Here are natural ways you could respond to this question:";
  };
  
  return (
    <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 p-8 rounded-xl shadow-sm">
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
          <MessageSquare className="h-5 w-5 text-green-600" />
        </div>
        <div>
          <h4 className="text-xl font-bold text-slate-800">
            {getMainSectionTitle()}
          </h4>
          <p className="text-sm text-slate-600 mt-1">
            {getMainSectionDescription()}
          </p>
        </div>
      </div>
      <div className="space-y-4">
        {responses.map((response) => (
          <div key={response.number} className="flex items-start gap-4">
            {onItemToggleSelect && isItemSelected && (
              <Checkbox
                checked={isItemSelected(response.text)}
                onCheckedChange={() => onItemToggleSelect(response.text)}
                className="mt-2"
              />
            )}
            <Button
              variant="outline"
              onClick={() => onAnswerSelect(response.text, response.number)}
              className="flex-1 text-left p-5 h-auto bg-white border-slate-200 hover:bg-slate-50 hover:border-green-300 justify-start group transition-all duration-200 shadow-sm"
            >
              <div className="flex items-start w-full">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 mt-0.5 group-hover:bg-green-200 transition-colors">
                  <span className="text-green-700 font-bold text-sm">{response.number}</span>
                </div>
                <span
                  className="text-sm text-slate-700 whitespace-normal text-left break-words leading-relaxed font-medium"
                  dangerouslySetInnerHTML={{ __html: formatInlineContent(response.text) }}
                />
              </div>
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
