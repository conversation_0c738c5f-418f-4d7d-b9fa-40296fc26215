
import React, { useMemo } from "react";
import { ChatMessage } from "./types";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { ChartContainer } from "@/components/ui/chart";
import {
  LineChart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
} from "recharts";
import { TrendingUp } from "lucide-react";

interface ChatPerformanceChartProps {
  messages: ChatMessage[];
}

const chartConfig = {
  performance: {
    label: "Performance",
    color: "hsl(var(--chart-1))",
  },
};

export const ChatPerformanceChart: React.FC<ChatPerformanceChartProps> = ({
  messages,
}) => {
  // Collect only user messages with performanceScore
  const chartData = useMemo(
    () =>
      messages
        .filter(
          (msg) =>
            msg.role === "user" &&
            typeof msg.performanceScore === "number"
        )
        .map((msg, index) => ({
          name: `Input ${index + 1}`,
          performance: Math.round(msg.performanceScore!),
        })),
    [messages]
  );

  if (chartData.length < 1) {
    return null;
  }

  return (
    <Card className="shadow-xl border-0 rounded-2xl overflow-hidden bg-gradient-to-br from-green-50 to-emerald-50">
      <CardHeader className="bg-gradient-to-r from-green-600 to-emerald-600 text-white">
        <CardTitle className="flex items-center text-xl font-semibold">
          <TrendingUp className="h-6 w-6 mr-3" />
          Your Conversation Performance
        </CardTitle>
        <p className="text-green-100 text-sm mt-1">
          Track your communication effectiveness over time
        </p>
      </CardHeader>
      <CardContent className="p-6">
        <ChartContainer config={chartConfig} className="h-[300px] w-full">
          <LineChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 0, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
            <XAxis
              dataKey="name"
              stroke="#64748b"
              fontSize={12}
              fontWeight={500}
            />
            <YAxis
              domain={[0, 100]}
              ticks={[0, 20, 40, 60, 80, 100]}
              allowDecimals={false}
              stroke="#64748b"
              fontSize={12}
              fontWeight={500}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Line
              type="monotone"
              dataKey="performance"
              stroke="#10b981"
              strokeWidth={3}
              dot={{
                fill: "#10b981",
                strokeWidth: 2,
                r: 5
              }}
              activeDot={{
                r: 8,
                fill: "#059669",
                stroke: "#ffffff",
                strokeWidth: 2
              }}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};
