import React from 'react';
import { FigmaCanvas } from './FigmaCanvas';
import { FigmaToolbar, useFigmaKeyboardShortcuts } from './FigmaToolbar';
import { FigmaLayersPanel } from './FigmaLayersPanel';
import { FigmaPropertiesPanel } from './FigmaPropertiesPanel';
import { TransformControls, useTransformKeyboardShortcuts } from './selection/TransformControls';
import { StylePanel } from './styles/StylePanel';
import { CanvasManagerDialog } from './storage/CanvasManagerDialog';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';
import { useAutoSave } from '@/hooks/useAutoSave';
import { cn } from '@/lib/utils';

interface FigmaCanvasContainerProps {
  className?: string;
  showLayersPanel?: boolean;
  showPropertiesPanel?: boolean;
  showStylePanel?: boolean;
  showTransformControls?: boolean;
  onSelectionChange?: (selectedIds: string[]) => void;
  onObjectCreated?: (objectId: string) => void;
  onObjectUpdated?: (objectId: string) => void;
  onObjectDeleted?: (objectId: string) => void;
}

export const FigmaCanvasContainer: React.FC<FigmaCanvasContainerProps> = ({
  className,
  showLayersPanel = true,
  showPropertiesPanel = true,
  showStylePanel = false,
  showTransformControls = true,
  onSelectionChange,
  onObjectCreated,
  onObjectUpdated,
  onObjectDeleted,
}) => {
  // Enable keyboard shortcuts
  useFigmaKeyboardShortcuts();
  useTransformKeyboardShortcuts();

  // Enable auto-save
  useAutoSave({
    enabled: true,
    interval: 30000, // 30 seconds
    onSave: () => console.log('Canvas auto-saved'),
    onError: (error) => console.error('Auto-save failed:', error),
  });

  const { selectedObjectIds, getSelectedObjects, updateObject } = useFigmaCanvasStore();

  const handleSelectionChange = (selectedIds: string[]) => {
    onSelectionChange?.(selectedIds);
  };

  const handleObjectCreated = (objectId: string) => {
    onObjectCreated?.(objectId);
  };

  const handleObjectUpdated = (objectId: string) => {
    onObjectUpdated?.(objectId);
  };

  const handleObjectDeleted = (objectId: string) => {
    onObjectDeleted?.(objectId);
  };

  const handleStyleChange = (style: any) => {
    selectedObjectIds.forEach(id => {
      updateObject(id, { style });
    });
  };

  return (
    <div className={cn("flex flex-col h-full bg-gray-50", className)}>
      {/* Toolbar */}
      <div className="flex-shrink-0 p-4 bg-white border-b border-gray-200">
        <div className="flex items-center justify-between">
          <FigmaToolbar />
          <CanvasManagerDialog />
        </div>
      </div>

      {/* Transform Controls */}
      {showTransformControls && selectedObjectIds.length > 0 && (
        <div className="flex-shrink-0 p-2 bg-white border-b border-gray-200">
          <TransformControls />
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Layers Panel */}
        {showLayersPanel && (
          <div className="flex-shrink-0">
            <FigmaLayersPanel />
          </div>
        )}

        {/* Canvas Area */}
        <div className="flex-1 relative">
          <FigmaCanvas
            className="w-full h-full"
            onSelectionChange={handleSelectionChange}
            onObjectCreated={handleObjectCreated}
            onObjectUpdated={handleObjectUpdated}
            onObjectDeleted={handleObjectDeleted}
          />
        </div>

        {/* Properties Panel */}
        {showPropertiesPanel && (
          <div className="flex-shrink-0">
            <FigmaPropertiesPanel />
          </div>
        )}

        {/* Style Panel */}
        {showStylePanel && selectedObjectIds.length > 0 && (
          <div className="flex-shrink-0 p-4">
            <StylePanel
              value={getSelectedObjects()[0]?.style}
              onChange={handleStyleChange}
            />
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="flex-shrink-0 px-4 py-2 bg-white border-t border-gray-200">
        <FigmaStatusBar />
      </div>
    </div>
  );
};

// Status Bar Component
const FigmaStatusBar: React.FC = () => {
  const { 
    zoom, 
    selectedObjectIds, 
    objects, 
    canvasSize,
    activeTool 
  } = useFigmaCanvasStore();

  const selectedObjects = selectedObjectIds.map(id => objects[id]).filter(Boolean);
  const totalObjects = Object.keys(objects).length;

  const formatZoom = (zoom: number) => {
    return `${Math.round(zoom * 100)}%`;
  };

  const getSelectionInfo = () => {
    if (selectedObjects.length === 0) {
      return `${totalObjects} objects`;
    } else if (selectedObjects.length === 1) {
      const obj = selectedObjects[0];
      return `${obj.type} selected`;
    } else {
      return `${selectedObjects.length} objects selected`;
    }
  };

  const getCanvasInfo = () => {
    return `${canvasSize.width} × ${canvasSize.height}`;
  };

  return (
    <div className="flex items-center justify-between text-xs text-gray-500">
      <div className="flex items-center gap-4">
        <span>Tool: {activeTool}</span>
        <span>{getSelectionInfo()}</span>
        <span>Canvas: {getCanvasInfo()}</span>
      </div>
      
      <div className="flex items-center gap-4">
        <span>Zoom: {formatZoom(zoom)}</span>
      </div>
    </div>
  );
};

// Export the main container as default
export default FigmaCanvasContainer;
