
import React from 'react';
import { Brain, Lightbulb, Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useNavigationStore } from '@/stores/useNavigationStore';

export const EmptyHistoricalAnalysis: React.FC = () => {
  const { setMainTab } = useNavigationStore();

  return (
    <div className="text-center py-12 px-4 bg-slate-50 rounded-lg">
      <Brain className="mx-auto h-16 w-16 text-slate-400 mb-4" />
      <h3 className="text-xl font-semibold text-slate-800 mb-2">Your Historical Analysis is Empty</h3>
      <p className="text-muted-foreground max-w-md mx-auto mb-6">
        Start analyzing questions and save results to automatically build your collection of insights.
      </p>
      
      <Button 
        onClick={() => setMainTab('analyze')}
        size="lg"
        className="bg-blue-600 hover:bg-blue-700 text-white"
      >
        <Plus className="mr-2 h-4 w-4" />
        Analyze Your First Question
      </Button>

      <div className="mt-8 p-4 bg-white border border-slate-200 rounded-lg max-w-md mx-auto text-left">
        <div className="flex items-start gap-3">
          <Lightbulb className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
          <div>            <h4 className="font-medium text-slate-800">How it works:</h4>
            <p className="text-sm text-slate-600 mt-1">
              Each saved analysis from the 'Conversation Analysis' tab will create a historical record that you can explore here.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
