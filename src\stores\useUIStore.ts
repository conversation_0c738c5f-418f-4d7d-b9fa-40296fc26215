
/**
 * UI Store - Refactored with Standardized Pattern
 *
 * Manages global UI state including modals, loading states, and navigation modes.
 * Uses the standardized store pattern for consistency.
 */

import { AnalysisResult } from "@/types/conversation";
import { createStandardStore, BaseStore } from "./base/createStandardStore";

export type ModalType = "CHARACTER_PERSONA" | "NOTE_EDITOR" | "SAVED_ANALYSIS_VIEWER";

interface ModalState<T = any> {
  type: ModalType | null;
  props: T;
}

interface ChatbotModeState {
  result: AnalysisResult;
  focusedAnswer?: string;
}

// UI-specific state
interface UIData {
  // Modal management
  modal: ModalState;

  // Loading states
  isFetchingModels: boolean;
  isAnalyzing: boolean;

  // UI visibility
  showApiManager: boolean;

  // Navigation modes
  chatbotMode: ChatbotModeState | null;
}

// UI-specific actions
interface UIActions {
  // Modal management
  openModal: <T = any>(type: ModalType, props?: T) => void;
  closeModal: () => void;

  // Loading state management
  setIsFetchingModels: (isFetching: boolean) => void;
  setIsAnalyzing: (analyzing: boolean) => void;

  // UI visibility
  setShowApiManager: (show: boolean) => void;

  // Navigation modes
  enterChatMode: (result: AnalysisResult, focusedAnswer?: string) => void;
  exitChatMode: () => void;

  // Utility methods
  isAnyLoading: () => boolean;
  hasActiveModal: () => boolean;
}

// Combined UI store interface
interface UIStore extends BaseStore, UIData, UIActions {}

// Create the UI store using the standardized pattern
export const useUIStore = createStandardStore<UIStore>(
  {
    name: "ui_state",
    version: "2.0.0",
    persist: false, // UI state is typically not persisted
    initialState: {
      modal: { type: null, props: {} },
      isFetchingModels: false,
      isAnalyzing: false,
      showApiManager: false,
      chatbotMode: null,
    },
  },
  (set, get, api, errorHandler, loadingHandler) => ({
    // Modal management
    openModal: <T = any>(type: ModalType, props: T = {} as T) => {
      try {
        set((state) => {
          state.modal = { type, props };
          state.lastUpdated = Date.now();
        });
      } catch (error) {
        errorHandler.handleError(error, 'Open Modal');
      }
    },

    closeModal: () => {
      try {
        set((state) => {
          state.modal = { type: null, props: {} };
          state.lastUpdated = Date.now();
        });
      } catch (error) {
        errorHandler.handleError(error, 'Close Modal');
      }
    },

    // Loading state management
    setIsFetchingModels: (isFetching: boolean) => {
      set((state) => {
        state.isFetchingModels = isFetching;
        state.lastUpdated = Date.now();
      });
    },

    setIsAnalyzing: (analyzing: boolean) => {
      set((state) => {
        state.isAnalyzing = analyzing;
        state.lastUpdated = Date.now();
      });
    },

    // UI visibility
    setShowApiManager: (show: boolean) => {
      set((state) => {
        state.showApiManager = show;
        state.lastUpdated = Date.now();
      });
    },

    // Navigation modes
    enterChatMode: (result: AnalysisResult, focusedAnswer?: string) => {
      try {
        const chatResult = focusedAnswer ? { ...result, analysis: focusedAnswer } : result;
        set((state) => {
          state.chatbotMode = { result: chatResult, focusedAnswer };
          state.lastUpdated = Date.now();
        });
      } catch (error) {
        errorHandler.handleError(error, 'Enter Chat Mode');
      }
    },

    exitChatMode: () => {
      try {
        set((state) => {
          state.chatbotMode = null;
          state.lastUpdated = Date.now();
        });
      } catch (error) {
        errorHandler.handleError(error, 'Exit Chat Mode');
      }
    },

    // Utility methods
    isAnyLoading: () => {
      const state = get();
      return state.loading || state.isFetchingModels || state.isAnalyzing;
    },

    hasActiveModal: () => {
      const state = get();
      return state.modal.type !== null;
    },
  })
);
