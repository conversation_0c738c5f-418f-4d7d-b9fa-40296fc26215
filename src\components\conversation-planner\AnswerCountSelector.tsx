
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Zap } from "lucide-react";

interface AnswerCountSelectorProps {
  numberOfAnswers: number;
  onNumberOfAnswersChange: (count: number) => void;
}

export const AnswerCountSelector: React.FC<AnswerCountSelectorProps> = ({
  numberOfAnswers,
  onNumberOfAnswersChange
}) => {
  return (    <Card className="border border-slate-600/50 bg-slate-800/80 shadow-sm">
      <CardContent className="p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 rounded-lg bg-emerald-600/20">
            <Zap className="h-5 w-5 text-emerald-400" />
          </div>
          <Label htmlFor="answers-count" className="text-lg font-semibold text-white">
            Number of Answers
          </Label>
        </div>
        <Input 
          id="answers-count" 
          type="number" 
          min="1" 
          max="30" 
          value={numberOfAnswers} 
          onChange={e => onNumberOfAnswersChange(Math.max(1, Math.min(30, parseInt(e.target.value) || 1)))} 
          className="h-12 text-base border-2 border-slate-600 focus:border-emerald-400 bg-slate-700 text-white placeholder:text-slate-400" 
        />
        <p className="text-sm text-slate-300 mt-3 font-medium">
          Range: 1-30 answers
        </p>
      </CardContent>
    </Card>
  );
};
