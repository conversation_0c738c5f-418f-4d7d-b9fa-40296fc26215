import * as THREE from 'three';
import { AnalysisResult } from '@/types/conversation';

// Represents the raw data for a node
export interface NodeData {
  id: string;
  label?: string;
  value?: number; // Example data property
  category?: string; // Example data property
  // position is now primarily 2D, z can be used for layering if needed
  position?: { x: number; y: number; z?: number };
  color?: THREE.ColorRepresentation;
  // mass is no longer directly used by physics, but can be a data property
  mass?: number;
  tags?: string[]; // Added for analysis
  clusterId?: string; // Added for clustering
  // Chat Analysis Record Integration
  analysisRecord?: AnalysisResult; // Link to the actual chat analysis record
  nodeType?: 'generic' | 'analysis-record' | 'cluster' | 'simulation'; // Type of node
  status?: 'active' | 'processing' | 'completed' | 'error' | 'archived'; // Status for analysis records
}

// Represents the raw data for a connection/edge
export interface ConnectionData {
  id: string;
  source: string; // ID of the source node
  target: string; // ID of the target node
  // strength is now conceptual for layout, not physics spring stiffness
  strength?: number; // Example: for spring stiffness
  label?: string; // Added for analysis
  tags?: string[]; // Added for analysis
}

// Combined data for the canvas
export interface CanvasData {
  nodes: NodeData[];
  connections: ConnectionData[];
}

// Simplified Node representation for 2D (used internally by LivingDataCanvas)
// This replaces PhysicsNode
export interface SimpleNode {
  id: string;
  mesh: THREE.Mesh | THREE.Sprite; 
  position: THREE.Vector2; // Current 2D position for layout and rendering
  data: NodeData; // Original data
  setHighlight: (highlight: boolean) => void;
  dispose: () => void;
  // Properties for force-directed layout
  force?: THREE.Vector2; 
  velocity?: THREE.Vector2;
  isDragged?: boolean; // Added for layout interaction
}

// Visual representation of connections (mostly unchanged, but source/targetNode types will be SimpleNode)
export interface VisualConnection {
  id: string;
  lineMesh: THREE.LineSegments | THREE.Line; // THREE.Line for Line2
  sourceNode: SimpleNode; // CHANGED from PhysicsNode
  targetNode: SimpleNode; // CHANGED from PhysicsNode
  update: () => void; 
  dispose: () => void;
}

// Chat Analysis Record Cluster Definition
export interface AnalysisCluster {
  id: string;
  name: string;
  description?: string;
  nodeIds: string[]; // IDs of nodes in this cluster
  color?: THREE.ColorRepresentation;
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
}

// Chat Simulation Configuration
export interface ChatSimulation {
  id: string;
  name: string;
  description?: string;
  sourceNodeIds: string[]; // Nodes involved in the simulation
  prompts: SimulationPrompt[];
  results?: SimulationResult[];
  status: 'pending' | 'running' | 'completed' | 'error';
  createdAt: Date;
  updatedAt: Date;
}

export interface SimulationPrompt {
  id: string;
  name: string;
  content: string;
  sourceType: 'predefined' | 'node-derived' | 'custom';
  sourceNodeId?: string; // If derived from a node
}

export interface SimulationResult {
  id: string;
  promptId: string;
  response: string;
  timestamp: Date;
  metadata?: {
    model?: string;
    tokens?: number;
    duration?: number;
  };
}

// Enhanced Canvas Data with Chat Analysis Records
export interface ChatAnalysisCanvasData extends CanvasData {
  clusters?: AnalysisCluster[];
  simulations?: ChatSimulation[];
  metadata?: {
    lastUpdated: Date;
    version: string;
  };
}

// PhysicsSpring is removed
// export interface PhysicsSpring { ... }
