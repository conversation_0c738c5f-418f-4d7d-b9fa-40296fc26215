import React, { useEffect, useRef } from 'react';
import { Canvas } from 'fabric';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';
import { useVisualCanvas } from '@/components/conversation-planner/insights-canvas/hooks/useVisualCanvas';
import { DrawingToolsFactory } from '../tools/DrawingToolsFactory';

interface VisualCanvasIntegrationProps {
  canvas: Canvas | null;
  enableNoteIntegration?: boolean;
  enableConnectionIntegration?: boolean;
}

export const VisualCanvasIntegration: React.FC<VisualCanvasIntegrationProps> = ({
  canvas,
  enableNoteIntegration = true,
  enableConnectionIntegration = true,
}) => {
  const visualCanvas = useVisualCanvas();
  const figmaStore = useFigmaCanvasStore();
  const syncedRef = useRef(false);

  // Sync existing notes to Figma canvas
  useEffect(() => {
    if (!canvas || !enableNoteIntegration || syncedRef.current) return;

    const syncNotesToFigma = () => {
      // Convert existing notes to Figma text objects
      visualCanvas.notes.forEach(note => {
        if (note.position) {
          const textObject = DrawingToolsFactory.createText(
            { x: note.position.x, y: note.position.y },
            note.content || 'Note',
            figmaStore.activeLayerId,
            { fill: '#1f2937' }
          );

          figmaStore.addObject({
            ...textObject,
            name: `Note: ${note.content?.substring(0, 20) || 'Untitled'}...`,
            metadata: {
              originalNoteId: note.id,
              type: 'visual-canvas-note',
            },
          });
        }
      });

      syncedRef.current = true;
    };

    // Delay sync to ensure canvas is ready
    setTimeout(syncNotesToFigma, 100);
  }, [canvas, visualCanvas.notes, enableNoteIntegration, figmaStore]);

  // Sync connections as lines
  useEffect(() => {
    if (!canvas || !enableConnectionIntegration) return;

    const syncConnectionsToFigma = () => {
      visualCanvas.connections.forEach(connection => {
        if (connection.startPosition && connection.endPosition) {
          const lineObject = DrawingToolsFactory.createLine(
            [connection.startPosition, connection.endPosition],
            figmaStore.activeLayerId,
            { stroke: '#6b7280', strokeWidth: 2 }
          );

          figmaStore.addObject({
            ...lineObject,
            name: `Connection: ${connection.id}`,
            metadata: {
              originalConnectionId: connection.id,
              type: 'visual-canvas-connection',
            },
          });
        }
      });
    };

    setTimeout(syncConnectionsToFigma, 200);
  }, [canvas, visualCanvas.connections, enableConnectionIntegration, figmaStore]);

  // Listen for Figma object changes and sync back to Visual Canvas
  useEffect(() => {
    const unsubscribe = figmaStore.addEventListener((event) => {
      if (event.type === 'object-updated') {
        const object = figmaStore.getObjectById(event.data.objectId);
        if (!object || !object.metadata) return;

        // Sync text objects back to notes
        if (object.metadata.type === 'visual-canvas-note' && object.type === 'text') {
          const noteId = object.metadata.originalNoteId;
          const note = visualCanvas.notes.find(n => n.id === noteId);
          
          if (note) {
            // Update note content and position
            visualCanvas.updateNote(noteId, {
              content: object.content,
              position: {
                x: object.transform.x,
                y: object.transform.y,
              },
            });
          }
        }

        // Sync line objects back to connections
        if (object.metadata.type === 'visual-canvas-connection' && object.type === 'line') {
          const connectionId = object.metadata.originalConnectionId;
          const connection = visualCanvas.connections.find(c => c.id === connectionId);
          
          if (connection && object.points && object.points.length >= 2) {
            visualCanvas.updateConnection(connectionId, {
              startPosition: {
                x: object.transform.x + object.points[0].x,
                y: object.transform.y + object.points[0].y,
              },
              endPosition: {
                x: object.transform.x + object.points[object.points.length - 1].x,
                y: object.transform.y + object.points[object.points.length - 1].y,
              },
            });
          }
        }
      }

      if (event.type === 'object-deleted') {
        const objectId = event.data.objectId;
        // Handle deletion of synced objects
        // This would require storing the mapping between Figma objects and Visual Canvas objects
      }
    });

    return unsubscribe;
  }, [figmaStore, visualCanvas]);

  return null; // This component doesn't render anything
};

// Hook for managing the integration
export const useVisualCanvasIntegration = (canvas: Canvas | null) => {
  const visualCanvas = useVisualCanvas();
  const figmaStore = useFigmaCanvasStore();

  const exportToVisualCanvas = () => {
    // Export all Figma objects to Visual Canvas format
    const figmaObjects = Object.values(figmaStore.objects);
    
    figmaObjects.forEach(obj => {
      if (obj.type === 'text') {
        // Create a note from text object
        visualCanvas.addNote({
          content: obj.content,
          position: {
            x: obj.transform.x,
            y: obj.transform.y,
          },
          style: {
            color: obj.style.fill || '#000000',
            fontSize: obj.textStyle?.fontSize || 16,
            fontFamily: obj.textStyle?.fontFamily || 'Inter, sans-serif',
          },
        });
      }
    });
  };

  const importFromVisualCanvas = () => {
    // Import all Visual Canvas notes and connections to Figma
    visualCanvas.notes.forEach(note => {
      if (note.position) {
        const textObject = DrawingToolsFactory.createText(
          note.position,
          note.content || 'Note',
          figmaStore.activeLayerId,
          { fill: note.style?.color || '#1f2937' }
        );

        figmaStore.addObject({
          ...textObject,
          name: `Imported Note: ${note.content?.substring(0, 20) || 'Untitled'}`,
          textStyle: {
            ...textObject.textStyle,
            fontSize: note.style?.fontSize || 16,
            fontFamily: note.style?.fontFamily || 'Inter, sans-serif',
          },
        });
      }
    });

    visualCanvas.connections.forEach(connection => {
      if (connection.startPosition && connection.endPosition) {
        const lineObject = DrawingToolsFactory.createLine(
          [connection.startPosition, connection.endPosition],
          figmaStore.activeLayerId,
          { stroke: '#6b7280', strokeWidth: 2 }
        );

        figmaStore.addObject({
          ...lineObject,
          name: `Imported Connection`,
        });
      }
    });
  };

  const clearFigmaCanvas = () => {
    // Clear all Figma objects
    Object.keys(figmaStore.objects).forEach(id => {
      figmaStore.deleteObject(id);
    });
  };

  const clearVisualCanvas = () => {
    // Clear all Visual Canvas notes and connections
    visualCanvas.notes.forEach(note => {
      visualCanvas.deleteNote(note.id);
    });
    
    visualCanvas.connections.forEach(connection => {
      visualCanvas.deleteConnection(connection.id);
    });
  };

  return {
    exportToVisualCanvas,
    importFromVisualCanvas,
    clearFigmaCanvas,
    clearVisualCanvas,
    hasVisualCanvasData: visualCanvas.notes.length > 0 || visualCanvas.connections.length > 0,
    hasFigmaData: Object.keys(figmaStore.objects).length > 0,
  };
};

// Integration toolbar component
export const IntegrationToolbar: React.FC<{
  canvas: Canvas | null;
  className?: string;
}> = ({ canvas, className }) => {
  const integration = useVisualCanvasIntegration(canvas);

  return (
    <div className={`flex items-center gap-2 p-2 bg-white border border-gray-200 rounded ${className}`}>
      <span className="text-xs text-gray-600">Integration:</span>
      
      <button
        onClick={integration.importFromVisualCanvas}
        disabled={!integration.hasVisualCanvasData}
        className="px-2 py-1 text-xs bg-blue-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-blue-600"
        title="Import from Analysis Canvas"
      >
        Import Notes
      </button>
      
      <button
        onClick={integration.exportToVisualCanvas}
        disabled={!integration.hasFigmaData}
        className="px-2 py-1 text-xs bg-green-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-green-600"
        title="Export to Analysis Canvas"
      >
        Export to Notes
      </button>
      
      <div className="w-px h-4 bg-gray-300" />
      
      <button
        onClick={integration.clearFigmaCanvas}
        disabled={!integration.hasFigmaData}
        className="px-2 py-1 text-xs bg-red-500 text-white rounded disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-red-600"
        title="Clear Design Canvas"
      >
        Clear Design
      </button>
    </div>
  );
};
