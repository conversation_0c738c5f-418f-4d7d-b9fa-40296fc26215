
/**
 * ConversationPlanner Component
 *
 * Refactored to use the new design system patterns.
 * Improved layout, consistent spacing, and better component organization.
 */

import React, { Suspense, lazy } from "react";
import { ApiKeyManager } from "./conversation-planner/ApiKeyManager";
import { QuestionAnalyzer } from "./conversation-planner/QuestionAnalyzer";
import { PlannerHeader } from "./conversation-planner/PlannerHeader";
import { useAnalysisResults } from "@/hooks/useAnalysisResults";
import { useUIStore } from "@/stores/useUIStore";
import { ComponentLoader } from "@/components/ui/loading-fallback";
import { useAppTheme } from "@/design-system";
import { cn } from "@/lib/utils";

// Lazy load the heavy AnalysisResults component
const AnalysisResults = lazy(() =>
  import("./conversation-planner/AnalysisResults").then(module => ({ default: module.AnalysisResults }))
);

export const ConversationPlanner: React.FC = () => {
  const { handleAnalyze } = useAnalysisResults();
  const chatbotMode = useUIStore((state) => state.chatbotMode);
  const { getSpacing } = useAppTheme();

  // Container classes using design system patterns
  const containerClasses = cn(
    "max-w-7xl mx-auto",
    "space-y-8 p-4 md:p-6 lg:p-8", // Responsive padding using design system
    "transition-all duration-normal" // Smooth transitions
  );

  const sectionClasses = cn(
    "space-y-6",
    "animate-fade-in" // Smooth entrance animation
  );

  return (
    <div id="conversation-planner-container" className={containerClasses}>
      {/* Header Section */}
      <header className="animate-fade-in">
        <PlannerHeader />
      </header>

      {/* Main Content - Only show when not in chatbot mode */}
      {!chatbotMode && (
        <section className={sectionClasses}>
          {/* API Key Manager */}
          <div
            id="api-key-manager-wrapper"
            className="order-1 transition-all duration-normal"
          >
            <ApiKeyManager />
          </div>

          {/* Question Analyzer */}
          <div className="order-2 transition-all duration-normal">
            <QuestionAnalyzer onAnalyze={handleAnalyze} />
          </div>
        </section>
      )}

      {/* Analysis Results Section */}
      <section
        className={cn(
          "mt-8 transition-all duration-normal",
          "animate-fade-in"
        )}
      >
        <Suspense fallback={
          <ComponentLoader
            message="Loading analysis results..."
            className="min-h-[200px] flex items-center justify-center"
          />
        }>
          <AnalysisResults />
        </Suspense>
      </section>
    </div>
  );
};
