import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  MessageSquare,
  Cog,
  CheckCircle,
  AlertCircle,
  Clock,
  ChevronDown,
  ChevronUp,
  Zap,
  GitBranch,
  Target
} from 'lucide-react';
import { ChainNode as ChainNodeType } from '@/types/visualization';
import { useThemedStyles } from './ThemeProvider';
import { cn } from '@/lib/utils';

interface ChainNodeProps {
  node: ChainNodeType;
  isSelected?: boolean;
  isHovered?: boolean;
  onSelect?: (nodeId: string) => void;
  onHover?: (nodeId: string | null) => void;
  onClick?: (nodeId: string) => void;
  className?: string;
  compact?: boolean;
}

const nodeTypeConfig = {
  input: {
    icon: MessageSquare,
    color: 'from-blue-500 to-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800'
  },
  process: {
    icon: Cog,
    color: 'from-purple-500 to-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    textColor: 'text-purple-800'
  },
  output: {
    icon: Target,
    color: 'from-green-500 to-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    textColor: 'text-green-800'
  },
  branch: {
    icon: GitBranch,
    color: 'from-orange-500 to-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    textColor: 'text-orange-800'
  },
  merge: {
    icon: Target,
    color: 'from-indigo-500 to-indigo-600',
    bgColor: 'bg-indigo-50',
    borderColor: 'border-indigo-200',
    textColor: 'text-indigo-800'
  }
};

const statusConfig = {
  pending: {
    icon: Clock,
    color: 'text-gray-500',
    bgColor: 'bg-gray-100'
  },
  processing: {
    icon: Zap,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-100'
  },
  completed: {
    icon: CheckCircle,
    color: 'text-green-500',
    bgColor: 'bg-green-100'
  },
  error: {
    icon: AlertCircle,
    color: 'text-red-500',
    bgColor: 'bg-red-100'
  }
};

export const ChainNode: React.FC<ChainNodeProps> = ({
  node,
  isSelected = false,
  isHovered = false,
  onSelect,
  onHover,
  onClick,
  className,
  compact = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const themedStyles = useThemedStyles();

  const typeConfig = nodeTypeConfig[node.type];
  const statusInfo = statusConfig[node.status];
  const IconComponent = typeConfig.icon;
  const StatusIcon = statusInfo.icon;

  const handleClick = () => {
    onClick?.(node.id);
    onSelect?.(node.id);
  };

  const handleMouseEnter = () => {
    onHover?.(node.id);
  };

  const handleMouseLeave = () => {
    onHover?.(null);
  };

  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ 
        opacity: 1, 
        scale: isHovered ? 1.05 : 1,
        y: isHovered ? -2 : 0
      }}
      transition={{ 
        duration: 0.2,
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
      className={cn(
        "relative cursor-pointer",
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
    >
      <Card className={cn(
        "transition-all duration-200 shadow-md hover:shadow-lg",
        typeConfig.borderColor,
        isSelected && "ring-2 ring-blue-400 ring-offset-2",
        isHovered && "shadow-xl",
        compact ? "min-w-[200px]" : "min-w-[280px]"
      )}>
        <CardHeader className={cn(
          "pb-2",
          compact ? "p-3" : "p-4"
        )}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className={cn(
                "p-2 rounded-lg bg-gradient-to-r",
                typeConfig.color
              )}>
                <IconComponent className="w-4 h-4 text-white" />
              </div>
              <div>
                <h4 className={cn(
                  "font-semibold",
                  compact ? "text-sm" : "text-base"
                )}>
                  {node.title}
                </h4>
                {node.metadata?.analysisType && (
                  <Badge variant="outline" className="text-xs mt-1">
                    {node.metadata.analysisType}
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className={cn(
                "p-1 rounded-full",
                statusInfo.bgColor
              )}>
                <StatusIcon className={cn("w-3 h-3", statusInfo.color)} />
              </div>
              
              {node.content.length > 100 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleExpanded}
                  className="p-1 h-6 w-6"
                >
                  {isExpanded ? (
                    <ChevronUp className="w-3 h-3" />
                  ) : (
                    <ChevronDown className="w-3 h-3" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className={cn(
          "pt-0",
          compact ? "p-3 pt-0" : "p-4 pt-0"
        )}>
          <AnimatePresence>
            <motion.div
              initial={{ height: compact ? 40 : 60 }}
              animate={{ 
                height: isExpanded ? 'auto' : (compact ? 40 : 60)
              }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <p className={cn(
                "text-gray-600 leading-relaxed",
                compact ? "text-xs" : "text-sm"
              )}>
                {node.content}
              </p>
            </motion.div>
          </AnimatePresence>
          
          {node.metadata && (
            <div className="flex items-center gap-2 mt-2 pt-2 border-t border-gray-100">
              {node.metadata.timestamp && (
                <Badge variant="secondary" className="text-xs">
                  {node.metadata.timestamp.toLocaleTimeString()}
                </Badge>
              )}
              {node.metadata.duration && (
                <Badge variant="secondary" className="text-xs">
                  {node.metadata.duration}ms
                </Badge>
              )}
              {node.metadata.tokens && (
                <Badge variant="secondary" className="text-xs">
                  {node.metadata.tokens} tokens
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Pulse animation for processing status */}
      {node.status === 'processing' && (
        <motion.div
          className="absolute inset-0 rounded-lg border-2 border-yellow-400"
          animate={{ 
            opacity: [0.5, 1, 0.5],
            scale: [1, 1.02, 1]
          }}
          transition={{ 
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}
    </motion.div>
  );
};
