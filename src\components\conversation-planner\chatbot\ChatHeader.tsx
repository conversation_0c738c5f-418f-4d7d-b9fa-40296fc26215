
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Save, Users, Bot } from "lucide-react";
import { CharacterPersona } from "@/types/conversation";

interface ChatHeaderProps {
  onBack: () => void;
  onSaveChat: () => void;
  onSelectPersona: () => void;
  selectedPersona: CharacterPersona | null;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  onBack,
  onSaveChat,
  onSelectPersona,
  selectedPersona,
}) => {
  return (
    <div className="flex items-center justify-between bg-white p-6 rounded-xl border border-slate-200 shadow-sm">
      <Button
        variant="outline"
        onClick={onBack}
        className="flex items-center bg-slate-50 hover:bg-slate-100 border-slate-300 text-slate-700 font-medium"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Analysis
      </Button>
      <div className="flex items-center gap-3">
        <Badge
          variant="secondary"
          className="flex items-center bg-blue-100 text-blue-800 border-blue-200 px-3 py-1.5 font-medium"
        >
          <Bot className="h-4 w-4 mr-2" />
          Chatbot Mode
        </Badge>
        <Button
          variant="outline"
          onClick={onSelectPersona}
          className="bg-purple-50 hover:bg-purple-100 border-purple-200 text-purple-700 font-medium"
        >
          <Users className="h-4 w-4 mr-2" />
          {selectedPersona ? selectedPersona.name : "Add Character"}
        </Button>
        <Button
          variant="outline"
          onClick={onSaveChat}
          className="bg-green-50 hover:bg-green-100 border-green-200 text-green-700 font-medium"
        >
          <Save className="h-4 w-4 mr-2" />
          Save Chat
        </Button>
      </div>
    </div>
  );
};
