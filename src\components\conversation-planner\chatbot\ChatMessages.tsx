import React from 'react';
import { ChatMessage } from './types';
import { ScrollArea } from '@/components/ui/scroll-area';
import ChatMessageItem from './ChatMessageItem';

interface ChatMessagesProps {
  messages: ChatMessage[];
  isLoading: boolean;
  isLiveFeedbackEnabled: boolean;
  isSelected?: (messageId: string) => boolean;
  onSelectMessage?: (messageId: string) => void;
}

export const ChatMessages: React.FC<ChatMessagesProps> = React.memo(({ messages, isLoading, isLiveFeedbackEnabled, isSelected, onSelectMessage }) => {
  return (
    <div className="bg-gradient-to-b from-slate-50 to-white rounded-xl border border-slate-200 p-4">
      <ScrollArea className="h-[55vh] w-full pr-4">
        <div className="space-y-6">
          {messages.map((message) => (
            <ChatMessageItem
              key={message.id}
              message={message}
              isLiveFeedbackEnabled={isLiveFeedbackEnabled}
              isSelected={isSelected ? isSelected(message.id) : false}
              onSelectMessage={onSelectMessage}
            />
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 p-4 rounded-2xl rounded-bl-lg shadow-sm">
                <div className="flex items-center gap-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                  <span className="text-sm font-medium">AI is thinking...</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
});
