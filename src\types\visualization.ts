import { AnalysisResult } from "./conversation";

export interface ChainNode {
  id: string;
  type: 'input' | 'process' | 'output' | 'branch' | 'merge';
  title: string;
  content: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  position: { x: number; y: number };
  metadata?: {
    analysisType?: string;
    timestamp?: Date;
    duration?: number;
    tokens?: number;
  };
  children?: string[]; // IDs of connected nodes
  parents?: string[]; // IDs of parent nodes
}

export interface ChainConnection {
  id: string;
  from: string; // Node ID
  to: string; // Node ID
  type: 'flow' | 'branch' | 'merge' | 'feedback';
  animated?: boolean;
  style?: {
    color?: string;
    width?: number;
    dashArray?: string;
  };
}

export interface VisualizationChain {
  id: string;
  title: string;
  description?: string;
  nodes: ChainNode[];
  connections: ChainConnection[];
  layout: 'horizontal' | 'vertical' | 'tree' | 'circular';
  theme: 'default' | 'dark' | 'colorful' | 'minimal';
}

export interface VisualizationConfig {
  showAnimations: boolean;
  showMetadata: boolean;
  compactMode: boolean;
  theme: string;
  layout: string;
}

export type ViewMode = 'cards' | 'columns' | 'chain' | 'hybrid';

export interface AnalysisVisualizationData {
  result: AnalysisResult;
  chain: VisualizationChain;
  viewMode: ViewMode;
  config: VisualizationConfig;
}

// Helper function to convert AnalysisResult to VisualizationChain
export const createVisualizationChain = (result: AnalysisResult): VisualizationChain => {
  const nodes: ChainNode[] = [];
  const connections: ChainConnection[] = [];

  // Input node
  const inputNode: ChainNode = {
    id: `input-${result.id}`,
    type: 'input',
    title: 'Question',
    content: result.question,
    status: 'completed',
    position: { x: 0, y: 0 },
    metadata: {
      timestamp: result.timestamp,
    },
    children: [`process-${result.id}`]
  };
  nodes.push(inputNode);

  // Process node
  const processNode: ChainNode = {
    id: `process-${result.id}`,
    type: 'process',
    title: `${result.analysisType.charAt(0).toUpperCase() + result.analysisType.slice(1)} Analysis`,
    content: `Processing with ${result.model}...`,
    status: 'completed',
    position: { x: 1, y: 0 },
    metadata: {
      analysisType: result.analysisType,
      timestamp: result.timestamp,
    },
    parents: [`input-${result.id}`],
    children: [`output-${result.id}`]
  };
  nodes.push(processNode);

  // Output node
  const outputNode: ChainNode = {
    id: `output-${result.id}`,
    type: 'output',
    title: 'Analysis Result',
    content: result.analysis.substring(0, 200) + (result.analysis.length > 200 ? '...' : ''),
    status: 'completed',
    position: { x: 2, y: 0 },
    metadata: {
      timestamp: result.timestamp,
    },
    parents: [`process-${result.id}`]
  };
  nodes.push(outputNode);

  // Create connections
  connections.push({
    id: `conn-input-process-${result.id}`,
    from: inputNode.id,
    to: processNode.id,
    type: 'flow',
    animated: true
  });

  connections.push({
    id: `conn-process-output-${result.id}`,
    from: processNode.id,
    to: outputNode.id,
    type: 'flow',
    animated: true
  });

  // Add branch nodes for multiple answers or follow-up questions
  if (result.parsedOutput?.type === 'multiple' && result.parsedOutput.answers) {
    result.parsedOutput.answers.forEach((answer, index) => {
      const branchNode: ChainNode = {
        id: `branch-${result.id}-${index}`,
        type: 'branch',
        title: `Option ${index + 1}`,
        content: answer.text.substring(0, 150) + (answer.text.length > 150 ? '...' : ''),
        status: 'completed',
        position: { x: 3, y: index - (result.parsedOutput!.answers!.length - 1) / 2 },
        parents: [`output-${result.id}`]
      };
      nodes.push(branchNode);

      connections.push({
        id: `conn-output-branch-${result.id}-${index}`,
        from: outputNode.id,
        to: branchNode.id,
        type: 'branch',
        animated: false
      });
    });
  }

  return {
    id: `chain-${result.id}`,
    title: `Analysis Chain: ${result.question.substring(0, 50)}...`,
    description: `${result.analysisType} analysis using ${result.model}`,
    nodes,
    connections,
    layout: 'horizontal',
    theme: 'default'
  };
};
