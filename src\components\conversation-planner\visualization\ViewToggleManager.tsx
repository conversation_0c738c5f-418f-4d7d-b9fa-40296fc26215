import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  LayoutGrid, 
  Columns3, 
  GitBranch, 
  Settings,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { ViewMode, VisualizationConfig } from '@/types/visualization';
import { cn } from '@/lib/utils';

interface ViewToggleManagerProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  config: VisualizationConfig;
  onConfigChange: (config: Partial<VisualizationConfig>) => void;
  className?: string;
  compact?: boolean;
}

interface ViewOption {
  id: ViewMode;
  label: string;
  icon: React.ComponentType<any>;
  description: string;
  color: string;
}

const viewOptions: ViewOption[] = [
  {
    id: 'cards',
    label: 'Cards',
    icon: LayoutGrid,
    description: 'Traditional card-based layout',
    color: 'text-blue-600'
  },
  {
    id: 'columns',
    label: '3-Column',
    icon: Columns3,
    description: 'Input, Process, Output columns',
    color: 'text-green-600'
  },
  {
    id: 'chain',
    label: 'Chain',
    icon: GitBranch,
    description: 'Interactive visualization chain',
    color: 'text-purple-600'
  },
  {
    id: 'hybrid',
    label: 'Hybrid',
    icon: Maximize2,
    description: 'Combined view with multiple perspectives',
    color: 'text-orange-600'
  }
];

export const ViewToggleManager: React.FC<ViewToggleManagerProps> = ({
  currentView,
  onViewChange,
  config,
  onConfigChange,
  className,
  compact = false
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Persist view preference to localStorage
  useEffect(() => {
    const savedView = localStorage.getItem('analysis-view-mode');
    if (savedView && viewOptions.find(v => v.id === savedView)) {
      onViewChange(savedView as ViewMode);
    }
  }, [onViewChange]);

  const handleViewChange = (view: ViewMode) => {
    if (view === currentView || isAnimating) return;
    
    setIsAnimating(true);
    localStorage.setItem('analysis-view-mode', view);
    onViewChange(view);
    
    // Reset animation flag after transition
    setTimeout(() => setIsAnimating(false), 300);
  };

  const handleConfigChange = (key: keyof VisualizationConfig, value: any) => {
    const newConfig = { [key]: value };
    onConfigChange(newConfig);
    
    // Persist config to localStorage
    const savedConfig = JSON.parse(localStorage.getItem('visualization-config') || '{}');
    localStorage.setItem('visualization-config', JSON.stringify({
      ...savedConfig,
      ...newConfig
    }));
  };

  const currentViewOption = viewOptions.find(v => v.id === currentView);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main View Toggle */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-slate-50 to-slate-100">
        <CardContent className={cn("p-4", compact && "p-3")}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-gray-600" />
                <span className={cn(
                  "font-medium text-gray-700",
                  compact ? "text-sm" : "text-base"
                )}>
                  View Mode
                </span>
              </div>
              
              {currentViewOption && (
                <Badge 
                  variant="secondary" 
                  className={cn(
                    "flex items-center gap-1",
                    compact ? "text-xs" : "text-sm"
                  )}
                >
                  <currentViewOption.icon className="w-3 h-3" />
                  {currentViewOption.label}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
                className={cn(
                  "p-2",
                  showSettings && "bg-gray-100"
                )}
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* View Options */}
          <div className="mt-4">
            <Tabs value={currentView} onValueChange={handleViewChange}>
              <TabsList className="grid w-full grid-cols-4 bg-white">
                {viewOptions.map((option) => (
                  <TabsTrigger
                    key={option.id}
                    value={option.id}
                    className={cn(
                      "flex items-center gap-2 transition-all duration-200",
                      compact ? "text-xs p-2" : "text-sm p-3"
                    )}
                    disabled={isAnimating}
                  >
                    <option.icon className={cn(
                      "transition-colors",
                      currentView === option.id ? option.color : "text-gray-400",
                      compact ? "w-3 h-3" : "w-4 h-4"
                    )} />
                    {!compact && (
                      <span className="hidden sm:inline">{option.label}</span>
                    )}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>

          {/* View Description */}
          {currentViewOption && !compact && (
            <motion.p
              key={currentView}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-xs text-gray-600 mt-2 text-center"
            >
              {currentViewOption.description}
            </motion.p>
          )}
        </CardContent>
      </Card>

      {/* Advanced Settings Panel */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="border border-gray-200">
              <CardContent className="p-4 space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <Settings className="w-4 h-4 text-gray-600" />
                  <span className="font-medium text-gray-700">Visualization Settings</span>
                </div>

                {/* Animation Toggle */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      Animations
                    </label>
                    <p className="text-xs text-gray-500">
                      Enable smooth transitions and effects
                    </p>
                  </div>
                  <Button
                    variant={config.showAnimations ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleConfigChange('showAnimations', !config.showAnimations)}
                    className="flex items-center gap-2"
                  >
                    {config.showAnimations ? (
                      <Eye className="w-3 h-3" />
                    ) : (
                      <EyeOff className="w-3 h-3" />
                    )}
                    {config.showAnimations ? 'On' : 'Off'}
                  </Button>
                </div>

                {/* Metadata Toggle */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      Show Metadata
                    </label>
                    <p className="text-xs text-gray-500">
                      Display timestamps, tokens, and other details
                    </p>
                  </div>
                  <Button
                    variant={config.showMetadata ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleConfigChange('showMetadata', !config.showMetadata)}
                    className="flex items-center gap-2"
                  >
                    {config.showMetadata ? (
                      <Eye className="w-3 h-3" />
                    ) : (
                      <EyeOff className="w-3 h-3" />
                    )}
                    {config.showMetadata ? 'On' : 'Off'}
                  </Button>
                </div>

                {/* Compact Mode Toggle */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      Compact Mode
                    </label>
                    <p className="text-xs text-gray-500">
                      Reduce spacing and use smaller components
                    </p>
                  </div>
                  <Button
                    variant={config.compactMode ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleConfigChange('compactMode', !config.compactMode)}
                    className="flex items-center gap-2"
                  >
                    {config.compactMode ? (
                      <Minimize2 className="w-3 h-3" />
                    ) : (
                      <Maximize2 className="w-3 h-3" />
                    )}
                    {config.compactMode ? 'Compact' : 'Normal'}
                  </Button>
                </div>

                {/* Theme Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Theme
                  </label>
                  <div className="grid grid-cols-4 gap-2">
                    {['default', 'dark', 'colorful', 'minimal'].map((theme) => (
                      <Button
                        key={theme}
                        variant={config.theme === theme ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleConfigChange('theme', theme)}
                        className="text-xs capitalize"
                      >
                        {theme}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Layout Selection (for chain view) */}
                {currentView === 'chain' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      Chain Layout
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {['horizontal', 'vertical', 'tree', 'circular'].map((layout) => (
                        <Button
                          key={layout}
                          variant={config.layout === layout ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleConfigChange('layout', layout)}
                          className="text-xs capitalize"
                        >
                          {layout}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Loading State */}
      {isAnimating && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center justify-center py-4"
        >
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Settings className="w-4 h-4" />
            </motion.div>
            Switching view...
          </div>
        </motion.div>
      )}
    </div>
  );
};
