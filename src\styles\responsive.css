/**
 * Responsive Design System
 * 
 * Comprehensive responsive utilities and patterns for consistent
 * cross-device experiences. Built on top of the design system breakpoints.
 */

@layer utilities {
  /* Container Queries (when supported) */
  @container (min-width: 320px) {
    .container-xs\:block {
      display: block;
    }
    .container-xs\:hidden {
      display: none;
    }
  }
  
  @container (min-width: 640px) {
    .container-sm\:block {
      display: block;
    }
    .container-sm\:hidden {
      display: none;
    }
  }

  /* Mobile-First Responsive Typography */
  .text-responsive-xs {
    @apply text-xs sm:text-sm md:text-base;
  }
  
  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg;
  }
  
  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl;
  }
  
  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }
  
  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }
  
  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }
  
  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl md:text-5xl;
  }

  /* Responsive Spacing */
  .spacing-responsive-xs {
    @apply p-2 sm:p-3 md:p-4;
  }
  
  .spacing-responsive-sm {
    @apply p-3 sm:p-4 md:p-6;
  }
  
  .spacing-responsive-md {
    @apply p-4 sm:p-6 md:p-8;
  }
  
  .spacing-responsive-lg {
    @apply p-6 sm:p-8 md:p-12;
  }
  
  .spacing-responsive-xl {
    @apply p-8 sm:p-12 md:p-16;
  }

  /* Responsive Margins */
  .margin-responsive-xs {
    @apply m-2 sm:m-3 md:m-4;
  }
  
  .margin-responsive-sm {
    @apply m-3 sm:m-4 md:m-6;
  }
  
  .margin-responsive-md {
    @apply m-4 sm:m-6 md:m-8;
  }
  
  .margin-responsive-lg {
    @apply m-6 sm:m-8 md:m-12;
  }

  /* Responsive Grid Systems */
  .grid-responsive-cards {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
    @apply gap-4 sm:gap-6 lg:gap-8;
  }
  
  .grid-responsive-2col {
    @apply grid grid-cols-1 md:grid-cols-2;
    @apply gap-4 md:gap-6 lg:gap-8;
  }
  
  .grid-responsive-3col {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
    @apply gap-4 sm:gap-6 lg:gap-8;
  }
  
  .grid-responsive-auto {
    @apply grid gap-4 sm:gap-6 lg:gap-8;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .grid-responsive-auto-sm {
    @apply grid gap-4 sm:gap-6;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .grid-responsive-auto-lg {
    @apply grid gap-6 lg:gap-8;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  /* Responsive Flexbox */
  .flex-responsive-col {
    @apply flex flex-col md:flex-row;
  }
  
  .flex-responsive-row {
    @apply flex flex-row md:flex-col;
  }
  
  .flex-responsive-wrap {
    @apply flex flex-wrap gap-4 sm:gap-6;
  }
  
  .flex-responsive-center {
    @apply flex flex-col items-center sm:flex-row sm:justify-between;
  }

  /* Responsive Visibility */
  .mobile-only {
    @apply block sm:hidden;
  }
  
  .tablet-only {
    @apply hidden sm:block lg:hidden;
  }
  
  .desktop-only {
    @apply hidden lg:block;
  }
  
  .mobile-tablet {
    @apply block lg:hidden;
  }
  
  .tablet-desktop {
    @apply hidden sm:block;
  }

  /* Responsive Navigation */
  .nav-responsive {
    @apply flex flex-col sm:flex-row;
    @apply space-y-2 sm:space-y-0 sm:space-x-4;
  }
  
  .nav-responsive-mobile {
    @apply flex flex-col space-y-2 sm:hidden;
  }
  
  .nav-responsive-desktop {
    @apply hidden sm:flex sm:space-x-4;
  }

  /* Responsive Cards */
  .card-responsive {
    @apply p-4 sm:p-6 lg:p-8;
    @apply rounded-lg sm:rounded-xl;
    @apply shadow-sm sm:shadow-md lg:shadow-lg;
  }
  
  .card-responsive-compact {
    @apply p-3 sm:p-4 md:p-6;
    @apply rounded-md sm:rounded-lg;
  }

  /* Responsive Images */
  .img-responsive {
    @apply w-full h-auto;
    @apply rounded-lg sm:rounded-xl;
  }
  
  .img-responsive-square {
    @apply w-full aspect-square object-cover;
    @apply rounded-lg sm:rounded-xl;
  }
  
  .img-responsive-video {
    @apply w-full aspect-video object-cover;
    @apply rounded-lg sm:rounded-xl;
  }

  /* Responsive Buttons */
  .btn-responsive {
    @apply px-4 py-2 sm:px-6 sm:py-3;
    @apply text-sm sm:text-base;
    @apply rounded-md sm:rounded-lg;
  }
  
  .btn-responsive-full {
    @apply w-full sm:w-auto;
    @apply px-4 py-2 sm:px-6 sm:py-3;
  }

  /* Responsive Forms */
  .form-responsive {
    @apply space-y-4 sm:space-y-6;
  }
  
  .form-group-responsive {
    @apply flex flex-col sm:flex-row sm:items-center;
    @apply space-y-2 sm:space-y-0 sm:space-x-4;
  }
  
  .input-responsive {
    @apply w-full px-3 py-2 sm:px-4 sm:py-3;
    @apply text-sm sm:text-base;
    @apply rounded-md sm:rounded-lg;
  }

  /* Responsive Modals */
  .modal-responsive {
    @apply w-full max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl;
    @apply mx-4 sm:mx-auto;
    @apply p-4 sm:p-6 lg:p-8;
  }
  
  .modal-responsive-full {
    @apply w-full h-full sm:w-auto sm:h-auto sm:max-w-2xl;
    @apply m-0 sm:m-4 sm:mx-auto;
    @apply rounded-none sm:rounded-xl;
  }

  /* Responsive Sidebars */
  .sidebar-responsive {
    @apply w-full sm:w-64 lg:w-72 xl:w-80;
    @apply h-auto sm:h-full;
    @apply border-b sm:border-b-0 sm:border-r;
  }
  
  .sidebar-responsive-collapsed {
    @apply w-full sm:w-16;
    @apply h-auto sm:h-full;
  }

  /* Responsive Tables */
  .table-responsive {
    @apply w-full overflow-x-auto;
  }
  
  .table-responsive table {
    @apply min-w-full;
  }
  
  .table-responsive-stack {
    @apply block sm:table;
  }
  
  .table-responsive-stack thead {
    @apply hidden sm:table-header-group;
  }
  
  .table-responsive-stack tbody {
    @apply block sm:table-row-group;
  }
  
  .table-responsive-stack tr {
    @apply block sm:table-row border-b sm:border-b-0;
    @apply mb-4 sm:mb-0;
  }
  
  .table-responsive-stack td {
    @apply block sm:table-cell;
    @apply text-right sm:text-left;
    @apply pl-0 sm:pl-4;
  }
  
  .table-responsive-stack td:before {
    content: attr(data-label) ": ";
    @apply font-semibold;
    @apply float-left sm:hidden;
  }

  /* Responsive Aspect Ratios */
  .aspect-responsive-square {
    @apply aspect-square sm:aspect-video lg:aspect-square;
  }
  
  .aspect-responsive-video {
    @apply aspect-square sm:aspect-video;
  }

  /* Responsive Positioning */
  .position-responsive-fixed {
    @apply fixed bottom-4 right-4 sm:bottom-6 sm:right-6 lg:bottom-8 lg:right-8;
  }
  
  .position-responsive-sticky {
    @apply sticky top-4 sm:top-6 lg:top-8;
  }

  /* Responsive Z-Index */
  .z-responsive-modal {
    @apply z-40 sm:z-50;
  }
  
  .z-responsive-dropdown {
    @apply z-30 sm:z-40;
  }

  /* Print Responsive */
  @media print {
    .print-responsive {
      @apply text-sm;
      @apply p-2;
      @apply shadow-none;
      @apply border border-gray-300;
    }
    
    .print-responsive-hide {
      @apply hidden;
    }
    
    .print-responsive-full {
      @apply w-full;
      @apply break-inside-avoid;
    }
  }

  /* High DPI / Retina Displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .retina-border {
      border-width: 0.5px;
    }
    
    .retina-text {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }

  /* Landscape/Portrait Orientation */
  @media (orientation: landscape) {
    .landscape-only {
      @apply block;
    }
    
    .portrait-only {
      @apply hidden;
    }
    
    .landscape-flex-row {
      @apply flex-row;
    }
  }
  
  @media (orientation: portrait) {
    .landscape-only {
      @apply hidden;
    }
    
    .portrait-only {
      @apply block;
    }
    
    .portrait-flex-col {
      @apply flex-col;
    }
  }

  /* Touch Device Optimizations */
  @media (hover: none) and (pointer: coarse) {
    .touch-friendly {
      @apply min-h-[44px] min-w-[44px];
      @apply p-3;
    }
    
    .touch-friendly-btn {
      @apply py-3 px-6;
      @apply text-base;
    }
    
    .touch-friendly-input {
      @apply py-3 px-4;
      @apply text-base;
    }
  }

  /* Reduced Motion Preferences */
  @media (prefers-reduced-motion: reduce) {
    .motion-reduce-safe {
      @apply transition-none;
      animation: none !important;
    }
  }

  /* High Contrast Preferences */
  @media (prefers-contrast: high) {
    .contrast-safe {
      @apply border-2 border-black;
      @apply bg-white text-black;
    }
  }
}
