/**
 * Web Worker for heavy data processing operations
 * This worker handles computationally intensive tasks to keep the main thread responsive
 */

import { AnalysisResult } from '../types/conversation';

// Worker message types
interface WorkerMessage {
  id: string;
  type: string;
  data: any;
}

interface WorkerResponse {
  id: string;
  type: string;
  data: any;
  error?: string;
}

// Processing functions
const processors = {
  /**
   * Process large datasets of analysis results
   */
  processAnalysisResults: (results: AnalysisResult[]) => {
    const startTime = performance.now();
    
    // Create indexes for fast lookup
    const byType = new Map<string, AnalysisResult[]>();
    const byModel = new Map<string, AnalysisResult[]>();
    const byRating = new Map<number, AnalysisResult[]>();
    const byDate = new Map<string, AnalysisResult[]>();
    
    // Process each result
    results.forEach(result => {
      // Index by type
      if (!byType.has(result.analysisType)) {
        byType.set(result.analysisType, []);
      }
      byType.get(result.analysisType)!.push(result);
      
      // Index by model
      if (!byModel.has(result.model)) {
        byModel.set(result.model, []);
      }
      byModel.get(result.model)!.push(result);
      
      // Index by rating
      if (result.rating) {
        if (!byRating.has(result.rating)) {
          byRating.set(result.rating, []);
        }
        byRating.get(result.rating)!.push(result);
      }
      
      // Index by date (day)
      const date = new Date(result.timestamp).toDateString();
      if (!byDate.has(date)) {
        byDate.set(date, []);
      }
      byDate.get(date)!.push(result);
    });
    
    const processingTime = performance.now() - startTime;
    
    return {
      indexes: {
        byType: Object.fromEntries(byType),
        byModel: Object.fromEntries(byModel),
        byRating: Object.fromEntries(byRating),
        byDate: Object.fromEntries(byDate)
      },
      statistics: {
        total: results.length,
        processingTime,
        typeDistribution: Object.fromEntries(
          Array.from(byType.entries()).map(([type, items]) => [type, items.length])
        ),
        modelDistribution: Object.fromEntries(
          Array.from(byModel.entries()).map(([model, items]) => [model, items.length])
        ),
        ratingDistribution: Object.fromEntries(
          Array.from(byRating.entries()).map(([rating, items]) => [rating, items.length])
        )
      }
    };
  },

  /**
   * Calculate similarity scores between analysis results
   */
  calculateSimilarities: (results: AnalysisResult[]) => {
    const startTime = performance.now();
    const similarities: Array<{
      sourceId: string;
      targetId: string;
      score: number;
      factors: string[];
    }> = [];
    
    // Simple similarity calculation based on multiple factors
    for (let i = 0; i < results.length; i++) {
      for (let j = i + 1; j < results.length; j++) {
        const source = results[i];
        const target = results[j];
        
        let score = 0;
        const factors: string[] = [];
        
        // Type similarity
        if (source.analysisType === target.analysisType) {
          score += 0.3;
          factors.push('same-type');
        }
        
        // Model similarity
        if (source.model === target.model) {
          score += 0.1;
          factors.push('same-model');
        }
        
        // Rating similarity
        if (source.rating && target.rating) {
          const ratingDiff = Math.abs(source.rating - target.rating);
          if (ratingDiff <= 1) {
            score += 0.2 * (1 - ratingDiff);
            factors.push('similar-rating');
          }
        }
        
        // Text similarity (simple word overlap)
        const sourceWords = new Set(
          source.question.toLowerCase().split(/\s+/).concat(
            source.analysis.toLowerCase().split(/\s+/)
          )
        );
        const targetWords = new Set(
          target.question.toLowerCase().split(/\s+/).concat(
            target.analysis.toLowerCase().split(/\s+/)
          )
        );
        
        const intersection = new Set([...sourceWords].filter(x => targetWords.has(x)));
        const union = new Set([...sourceWords, ...targetWords]);
        const textSimilarity = intersection.size / union.size;
        
        if (textSimilarity > 0.1) {
          score += textSimilarity * 0.4;
          factors.push('text-similarity');
        }
        
        // Only include meaningful similarities
        if (score > 0.2) {
          similarities.push({
            sourceId: source.id,
            targetId: target.id,
            score: Math.round(score * 100) / 100,
            factors
          });
        }
      }
    }
    
    const processingTime = performance.now() - startTime;
    
    return {
      similarities: similarities.sort((a, b) => b.score - a.score),
      statistics: {
        totalComparisons: (results.length * (results.length - 1)) / 2,
        meaningfulSimilarities: similarities.length,
        processingTime,
        averageScore: similarities.length > 0 
          ? similarities.reduce((sum, s) => sum + s.score, 0) / similarities.length 
          : 0
      }
    };
  },

  /**
   * Generate layout positions for nodes using force-directed algorithm
   */
  calculateForceDirectedLayout: (nodes: any[], connections: any[], parameters: any) => {
    const startTime = performance.now();
    const { width = 1000, height = 800, iterations = 100, strength = 0.5 } = parameters;
    
    // Initialize positions if not set
    nodes.forEach(node => {
      if (!node.position) {
        node.position = {
          x: Math.random() * width,
          y: Math.random() * height
        };
      }
      node.velocity = { x: 0, y: 0 };
    });
    
    // Force-directed layout simulation
    for (let iteration = 0; iteration < iterations; iteration++) {
      // Reset forces
      nodes.forEach(node => {
        node.force = { x: 0, y: 0 };
      });
      
      // Repulsive forces between all nodes
      for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
          const nodeA = nodes[i];
          const nodeB = nodes[j];
          
          const dx = nodeB.position.x - nodeA.position.x;
          const dy = nodeB.position.y - nodeA.position.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          
          const repulsiveForce = (strength * 1000) / (distance * distance);
          const fx = (dx / distance) * repulsiveForce;
          const fy = (dy / distance) * repulsiveForce;
          
          nodeA.force.x -= fx;
          nodeA.force.y -= fy;
          nodeB.force.x += fx;
          nodeB.force.y += fy;
        }
      }
      
      // Attractive forces for connected nodes
      connections.forEach(connection => {
        const sourceNode = nodes.find(n => n.id === connection.sourceId);
        const targetNode = nodes.find(n => n.id === connection.targetId);
        
        if (sourceNode && targetNode) {
          const dx = targetNode.position.x - sourceNode.position.x;
          const dy = targetNode.position.y - sourceNode.position.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          
          const attractiveForce = distance * strength * 0.01;
          const fx = (dx / distance) * attractiveForce;
          const fy = (dy / distance) * attractiveForce;
          
          sourceNode.force.x += fx;
          sourceNode.force.y += fy;
          targetNode.force.x -= fx;
          targetNode.force.y -= fy;
        }
      });
      
      // Apply forces and update positions
      nodes.forEach(node => {
        node.velocity.x = (node.velocity.x + node.force.x) * 0.9;
        node.velocity.y = (node.velocity.y + node.force.y) * 0.9;
        
        node.position.x += node.velocity.x;
        node.position.y += node.velocity.y;
        
        // Keep nodes within bounds
        node.position.x = Math.max(50, Math.min(width - 50, node.position.x));
        node.position.y = Math.max(50, Math.min(height - 50, node.position.y));
      });
    }
    
    const processingTime = performance.now() - startTime;
    
    return {
      nodes: nodes.map(node => ({
        id: node.id,
        position: node.position
      })),
      statistics: {
        iterations,
        processingTime,
        nodeCount: nodes.length,
        connectionCount: connections.length
      }
    };
  },

  /**
   * Process search queries with fuzzy matching
   */
  processSearch: (query: string, items: any[], fields: string[]) => {
    const startTime = performance.now();
    
    if (!query.trim()) {
      return {
        results: items,
        statistics: { processingTime: 0, totalItems: items.length, matchedItems: items.length }
      };
    }
    
    const queryWords = query.toLowerCase().split(/\s+/);
    const results = items.map(item => {
      let score = 0;
      const matches: string[] = [];
      
      fields.forEach(field => {
        const fieldValue = item[field]?.toString().toLowerCase() || '';
        
        queryWords.forEach(word => {
          if (fieldValue.includes(word)) {
            score += word.length / fieldValue.length;
            matches.push(field);
          }
        });
      });
      
      return { item, score, matches };
    })
    .filter(result => result.score > 0)
    .sort((a, b) => b.score - a.score)
    .map(result => result.item);
    
    const processingTime = performance.now() - startTime;
    
    return {
      results,
      statistics: {
        processingTime,
        totalItems: items.length,
        matchedItems: results.length,
        query
      }
    };
  }
};

// Message handler
self.onmessage = function(event: MessageEvent<WorkerMessage>) {
  const { id, type, data } = event.data;
  
  try {
    let result;
    
    switch (type) {
      case 'processAnalysisResults':
        result = processors.processAnalysisResults(data);
        break;
      case 'calculateSimilarities':
        result = processors.calculateSimilarities(data);
        break;
      case 'calculateForceDirectedLayout':
        result = processors.calculateForceDirectedLayout(data.nodes, data.connections, data.parameters);
        break;
      case 'processSearch':
        result = processors.processSearch(data.query, data.items, data.fields);
        break;
      default:
        throw new Error(`Unknown processing type: ${type}`);
    }
    
    const response: WorkerResponse = {
      id,
      type,
      data: result
    };
    
    self.postMessage(response);
  } catch (error) {
    const response: WorkerResponse = {
      id,
      type,
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    
    self.postMessage(response);
  }
};

// Export for TypeScript
export {};
