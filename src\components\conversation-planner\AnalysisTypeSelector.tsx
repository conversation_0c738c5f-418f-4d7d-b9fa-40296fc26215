
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Target, List, Zap, User, ThumbsUp, Palette, HeartCrack } from "lucide-react";

// Define the allowed types
type AnalysisType = 'multiple' | 'deep' | 'character' | 'pros-cons' | 'six-hats' | 'emotional-angles';

interface AnalysisTypeSelectorProps {
  analysisType: AnalysisType;
  onAnalysisTypeChange: (type: AnalysisType) => void;
}

// Map color to proper Tailwind classes for dark theme
const colorClassMap: Record<string, { border: string; bg: string; iconBg: string; focus: string }> = {
  blue: {
    border: "border-blue-400",
    bg: "bg-blue-900/40",
    iconBg: "bg-blue-800/60",
    focus: "focus-visible:ring-blue-400"
  },
  purple: {
    border: "border-purple-400",
    bg: "bg-purple-900/40",
    iconBg: "bg-purple-800/60",
    focus: "focus-visible:ring-purple-400"
  },
  green: {
    border: "border-green-400",
    bg: "bg-green-900/40",
    iconBg: "bg-green-800/60",
    focus: "focus-visible:ring-green-400"
  },
  orange: {
    border: "border-orange-400",
    bg: "bg-orange-900/40",
    iconBg: "bg-orange-800/60",
    focus: "focus-visible:ring-orange-400"
  },
  rose: {
    border: "border-rose-400",
    bg: "bg-rose-900/40",
    iconBg: "bg-rose-800/60",
    focus: "focus-visible:ring-rose-400"
  },
  teal: {
    border: "border-teal-400",
    bg: "bg-teal-900/40",
    iconBg: "bg-teal-800/60",
    focus: "focus-visible:ring-teal-400"
  }
};

export const AnalysisTypeSelector: React.FC<AnalysisTypeSelectorProps> = ({
  analysisType,
  onAnalysisTypeChange
}) => {  const analysisOptions = [
    {
      id: 'multiple',
      title: 'Conversation Scenarios',
      description: 'Explore various conversation paths and outcomes. Great for brainstorming and preparation.',
      icon: <List className="h-6 w-6 text-blue-400" />,
      color: 'blue'
    },
    {
      id: 'deep',
      title: 'Strategic Deep Dive',
      description: 'Get a comprehensive strategic breakdown of your question, including arguments and assumptions.',
      icon: <Zap className="h-6 w-6 text-purple-400" />,
      color: 'purple'
    },
    {
      id: 'character',
      title: 'Persona Simulation',
      description: 'Engage with a realistic AI character persona to practice your conversation skills.',
      icon: <User className="h-6 w-6 text-green-400" />,
      color: 'green'
    },
    {
      id: 'pros-cons',
      title: 'Pros & Cons',
      description: 'Weigh the positive and negative aspects of your question or statement.',
      icon: <ThumbsUp className="h-6 w-6 text-orange-400" />,
      color: 'orange'
    },
    {
        id: 'six-hats',
        title: 'Six Thinking Hats',
        description: 'Analyze the situation from six different perspectives: facts, feelings, cautions, benefits, creativity, and process.',
        icon: <Palette className="h-6 w-6 text-rose-400" />,
        color: 'rose'
    },
    {
        id: 'emotional-angles',
        title: 'Emotional Angles',
        description: 'Explore the topic from different emotional standpoints like joy, sadness, anger, and fear.',
        icon: <HeartCrack className="h-6 w-6 text-teal-400" />,
        color: 'teal'
    }
  ];

  return (
    <Card className="border-none bg-transparent shadow-none">
      <CardContent className="p-0">        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 rounded-lg bg-slate-700">
            <Target className="h-5 w-5 text-slate-300" />
          </div>
          <Label className="text-lg font-semibold text-white">Analysis Type</Label>
        </div>
        <RadioGroup 
          value={analysisType} 
          onValueChange={(value) => onAnalysisTypeChange(value as AnalysisType)} 
          className="grid md:grid-cols-3 gap-4"
        >
          {analysisOptions.map((option, index) => {
            const isSelected = analysisType === option.id;
            const colorClasses = colorClassMap[option.color];
            return (              <Label 
                key={option.id}
                htmlFor={option.id}                className={`flex flex-col items-start text-left space-y-3 p-4 rounded-xl border-2 transition-all cursor-pointer hover:shadow-lg hover:-translate-y-1 bg-slate-800/80 backdrop-blur-sm animate-fade-in
                  ${isSelected 
                    ? `${colorClasses.border} ${colorClasses.bg} shadow-md ring-2 ring-opacity-40 ${colorClasses.focus}` 
                    : 'border-slate-600/50 hover:border-primary/60 hover:bg-slate-700/60'
                  }
                `}
                tabIndex={0}
                style={{ animationDelay: `${index * 100}ms`, animationFillMode: 'backwards' }}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${colorClasses.iconBg}`}>
                      {option.icon}
                  </div>                  <p className="text-base font-semibold text-white">
                    {option.title}
                  </p>
                </div>
                <p className="text-sm text-white flex-grow">
                  {option.description}
                </p>
                <RadioGroupItem value={option.id} id={option.id} className="sr-only" />
              </Label>
            );
          })}
        </RadioGroup>
      </CardContent>
    </Card>
  );
};
