import React from "react";
import { Brain, Plus, Lightbulb, ArrowRight, <PERSON>rk<PERSON> } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigationStore } from "@/stores/useNavigationStore";
import { useCanvasStore } from "@/stores/useCanvasStore";
import { UserNote } from "@/types/conversation";

export const EmptyCanvasState: React.FC = () => {
  const { setMainTab, setHistoricalAnalysisTab } = useNavigationStore();
  const { addNote } = useCanvasStore();

  const addSampleData = async () => {
    const sampleNotes: UserNote[] = [
      {
        id: `sample-${Date.now()}-1`,
        questionId: "sample-question-1",
        noteText: "AI Ethics and Responsibility",
        linkedAnswerIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: ["ai", "ethics", "responsibility"],
        analysisType: "deep"
      },
      {
        id: `sample-${Date.now()}-2`,
        questionId: "sample-question-2",
        noteText: "Machine Learning Applications",
        linkedAnswerIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: ["ml", "applications", "technology"],
        analysisType: "multiple"
      },
      {
        id: `sample-${Date.now()}-3`,
        questionId: "sample-question-3",
        noteText: "Future of Work and Automation",
        linkedAnswerIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: ["automation", "future", "work"],
        analysisType: "character"
      }
    ];

    // Add sample notes to the canvas
    for (const note of sampleNotes) {
      await addNote(note);
    }
  };

  return (
    <div className="h-[600px] flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200 rounded-lg">
      <div className="text-center max-w-md mx-auto p-8">
        <div className="mb-6">
          <div className="relative">
            <Brain className="mx-auto h-20 w-20 text-slate-400 mb-4" />
            <div className="absolute -top-2 -right-2 bg-blue-100 rounded-full p-2">
              <Plus className="h-4 w-4 text-blue-600" />
            </div>
          </div>
        </div>
        
        <h3 className="text-xl font-semibold text-slate-900 mb-3">
          No Visual Analysis Canvas Yet
        </h3>
        
        <p className="text-slate-600 mb-6 leading-relaxed">
          Start analyzing questions and saving insights to see them visualized here. 
          The canvas will show connections between your questions, answers, and notes.
        </p>

        <Card className="bg-blue-50 border-blue-200 mb-6">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <h4 className="font-medium text-blue-900 mb-1">How it works:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li className="flex items-center gap-2">
                    <ArrowRight className="h-3 w-3" />
                    Analyze questions in the Question tab
                  </li>
                  <li className="flex items-center gap-2">
                    <ArrowRight className="h-3 w-3" />
                    Save analysis results to your library
                  </li>
                  <li className="flex items-center gap-2">
                    <ArrowRight className="h-3 w-3" />
                    Add notes and insights to connect ideas
                  </li>
                  <li className="flex items-center gap-2">
                    <ArrowRight className="h-3 w-3" />
                    Watch your analysis network grow visually
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            variant="default"
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => {
              setMainTab('analyze');
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Start Analyzing
          </Button>

          <Button
            variant="outline"
            className="border-purple-300 text-purple-700 hover:bg-purple-50"
            onClick={addSampleData}
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Try Sample Data
          </Button>

          <Button
            variant="outline"
            className="border-slate-300 text-slate-700 hover:bg-slate-50"
            onClick={() => {
              setHistoricalAnalysisTab('list');
            }}
          >
            View as List
          </Button>
        </div>

        <div className="mt-6 text-xs text-slate-500">
          Your insights will automatically appear here once you start analyzing
        </div>
      </div>
    </div>
  );
};
