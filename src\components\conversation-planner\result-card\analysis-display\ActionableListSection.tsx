
import React from 'react';
import { Button } from '@/components/ui/button';
import { formatInlineContent } from './ContentFormatter';
import { LucideIcon } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';

interface ActionableListSectionProps {
  title: string;
  description: string;
  items: string[];
  onItemSelect: (item: string) => void;
  IconComponent: LucideIcon;
  onItemToggleSelect?: (item: string) => void;
  isItemSelected?: (item: string) => boolean;
}

export const ActionableListSection: React.FC<ActionableListSectionProps> = ({
  title,
  description,
  items,
  onItemSelect,
  IconComponent,
  onItemToggleSelect,
  isItemSelected,
}) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 p-8 rounded-xl shadow-sm">
      <div className="flex items-center mb-6">
        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
          <IconComponent className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h4 className="text-xl font-bold text-slate-800">{title}</h4>
          <p className="text-sm text-slate-600 mt-1">{description}</p>
        </div>
      </div>
      <div className="space-y-4">
        {items.map((item, index) => (
          <div key={index} className="flex items-start gap-4">
            {onItemToggleSelect && isItemSelected && (
              <Checkbox
                checked={isItemSelected(item)}
                onCheckedChange={() => onItemToggleSelect(item)}
                className="mt-2"
              />
            )}
            <Button
              key={index}
              variant="outline"
              onClick={() => onItemSelect(item)}
              className="flex-1 text-left p-5 h-auto bg-white border-slate-200 hover:bg-slate-50 hover:border-blue-300 justify-start group transition-all duration-200 shadow-sm"
            >
              <div className="flex items-start w-full">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 mt-0.5 group-hover:bg-blue-200 transition-colors">
                  <span className="text-blue-700 font-bold text-sm">{index + 1}</span>
                </div>
                <span
                  className="text-sm text-slate-700 text-left break-words whitespace-normal leading-relaxed font-medium"
                  dangerouslySetInnerHTML={{ __html: formatInlineContent(item) }}
                />
              </div>
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};
