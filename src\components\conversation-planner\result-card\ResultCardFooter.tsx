
import React from "react";
import { ResultCardActions } from "./ResultCardActions";
import { AddNoteButton } from "../AddNoteButton";
import { AnalysisResult } from "@/types/conversation";
import { But<PERSON> } from "@/components/ui/button";
import { Save } from "lucide-react";

interface ResultCardFooterProps {
  result: AnalysisResult;
  onSave: () => void;
  onCopy: () => void;
  onToggleRefinement: () => void;
  onDelete: () => void;
  onAddNote?: (questionId: string, answerId: string, answerContent: string) => void;
  onSaveSelected: () => void;
  selectedItemsCount: number;
}

export const ResultCardFooter: React.FC<ResultCardFooterProps> = ({
  result,
  onSave,
  onCopy,
  onToggleRefinement,
  onDelete,
  onAddNote,
  onSaveSelected,
  selectedItemsCount,
}) => {
  return (
    <div className="flex items-center justify-between bg-slate-50 border-t border-slate-200 p-6 rounded-b-xl">
      <div className="flex items-center gap-3">
        {selectedItemsCount > 0 && (
          <Button
            onClick={onSaveSelected}
            size="sm"
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 font-medium"
          >
            <Save className="mr-2 h-4 w-4" />
            Save Selected ({selectedItemsCount})
          </Button>
        )}
        {onAddNote && (
          <AddNoteButton
            questionId={result.question}
            answerId={result.id}
            answerContent={result.analysis}
            onAddNote={onAddNote}
          />
        )}
      </div>
      <ResultCardActions
        onSave={onSave}
        onCopy={onCopy}
        onToggleRefinement={onToggleRefinement}
        onDelete={onDelete}
        showRefinement={result.analysisType !== 'character'}
      />
    </div>
  );
};
