import { AnalysisResult } from '@/types/conversation';

// Color palette for different analysis types
export const analysisTypeColors = {
  multiple: {
    primary: '#3b82f6', // blue-500
    secondary: '#dbeafe', // blue-100
    accent: '#1d4ed8', // blue-700
    background: 'bg-blue-50',
    border: 'border-blue-200',
    text: 'text-blue-800',
    gradient: 'from-blue-500 to-blue-600'
  },
  deep: {
    primary: '#8b5cf6', // violet-500
    secondary: '#ede9fe', // violet-100
    accent: '#7c3aed', // violet-600
    background: 'bg-violet-50',
    border: 'border-violet-200',
    text: 'text-violet-800',
    gradient: 'from-violet-500 to-violet-600'
  },
  character: {
    primary: '#10b981', // emerald-500
    secondary: '#d1fae5', // emerald-100
    accent: '#059669', // emerald-600
    background: 'bg-emerald-50',
    border: 'border-emerald-200',
    text: 'text-emerald-800',
    gradient: 'from-emerald-500 to-emerald-600'
  },
  'pros-cons': {
    primary: '#f59e0b', // amber-500
    secondary: '#fef3c7', // amber-100
    accent: '#d97706', // amber-600
    background: 'bg-amber-50',
    border: 'border-amber-200',
    text: 'text-amber-800',
    gradient: 'from-amber-500 to-amber-600'
  },
  'six-hats': {
    primary: '#6366f1', // indigo-500
    secondary: '#e0e7ff', // indigo-100
    accent: '#4f46e5', // indigo-600
    background: 'bg-indigo-50',
    border: 'border-indigo-200',
    text: 'text-indigo-800',
    gradient: 'from-indigo-500 to-indigo-600'
  },
  'emotional-angles': {
    primary: '#ec4899', // pink-500
    secondary: '#fce7f3', // pink-100
    accent: '#db2777', // pink-600
    background: 'bg-pink-50',
    border: 'border-pink-200',
    text: 'text-pink-800',
    gradient: 'from-pink-500 to-pink-600'
  }
};

// Theme configurations
export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
  };
  gradients: {
    primary: string;
    secondary: string;
    accent: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
}

export const themes: Record<string, ThemeConfig> = {
  default: {
    name: 'Default',
    colors: {
      primary: '#3b82f6',
      secondary: '#e2e8f0',
      accent: '#1e40af',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
      textSecondary: '#64748b',
      border: '#e2e8f0'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
      secondary: 'linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%)',
      accent: 'linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%)'
    },
    shadows: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
    },
    borderRadius: {
      sm: '0.375rem',
      md: '0.5rem',
      lg: '0.75rem'
    }
  },
  dark: {
    name: 'Dark',
    colors: {
      primary: '#60a5fa',
      secondary: '#374151',
      accent: '#3b82f6',
      background: '#111827',
      surface: '#1f2937',
      text: '#f9fafb',
      textSecondary: '#9ca3af',
      border: '#374151'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%)',
      secondary: 'linear-gradient(135deg, #374151 0%, #1f2937 100%)',
      accent: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
    },
    shadows: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)'
    },
    borderRadius: {
      sm: '0.375rem',
      md: '0.5rem',
      lg: '0.75rem'
    }
  },
  colorful: {
    name: 'Colorful',
    colors: {
      primary: '#8b5cf6',
      secondary: '#fbbf24',
      accent: '#ef4444',
      background: '#ffffff',
      surface: '#fef7ff',
      text: '#1e293b',
      textSecondary: '#64748b',
      border: '#e879f9'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #8b5cf6 0%, #ec4899 50%, #f59e0b 100%)',
      secondary: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
      accent: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
    },
    shadows: {
      sm: '0 1px 2px 0 rgb(139 92 246 / 0.1)',
      md: '0 4px 6px -1px rgb(139 92 246 / 0.2), 0 2px 4px -2px rgb(139 92 246 / 0.1)',
      lg: '0 10px 15px -3px rgb(139 92 246 / 0.2), 0 4px 6px -4px rgb(139 92 246 / 0.1)'
    },
    borderRadius: {
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem'
    }
  },
  minimal: {
    name: 'Minimal',
    colors: {
      primary: '#000000',
      secondary: '#f5f5f5',
      accent: '#404040',
      background: '#ffffff',
      surface: '#fafafa',
      text: '#000000',
      textSecondary: '#666666',
      border: '#e5e5e5'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #000000 0%, #404040 100%)',
      secondary: 'linear-gradient(135deg, #f5f5f5 0%, #e5e5e5 100%)',
      accent: 'linear-gradient(135deg, #404040 0%, #262626 100%)'
    },
    shadows: {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 2px 4px 0 rgb(0 0 0 / 0.1)',
      lg: '0 4px 8px 0 rgb(0 0 0 / 0.1)'
    },
    borderRadius: {
      sm: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem'
    }
  }
};

// Helper functions
export const getAnalysisTypeColor = (analysisType: AnalysisResult['analysisType']) => {
  return analysisTypeColors[analysisType] || analysisTypeColors.multiple;
};

export const getTheme = (themeName: string): ThemeConfig => {
  return themes[themeName] || themes.default;
};

export const applyThemeToElement = (element: HTMLElement, theme: ThemeConfig) => {
  element.style.setProperty('--theme-primary', theme.colors.primary);
  element.style.setProperty('--theme-secondary', theme.colors.secondary);
  element.style.setProperty('--theme-accent', theme.colors.accent);
  element.style.setProperty('--theme-background', theme.colors.background);
  element.style.setProperty('--theme-surface', theme.colors.surface);
  element.style.setProperty('--theme-text', theme.colors.text);
  element.style.setProperty('--theme-text-secondary', theme.colors.textSecondary);
  element.style.setProperty('--theme-border', theme.colors.border);
};

// CSS class generators
export const generateThemeClasses = (theme: ThemeConfig) => {
  return {
    background: `bg-[${theme.colors.background}]`,
    surface: `bg-[${theme.colors.surface}]`,
    text: `text-[${theme.colors.text}]`,
    textSecondary: `text-[${theme.colors.textSecondary}]`,
    border: `border-[${theme.colors.border}]`,
    primary: `bg-[${theme.colors.primary}]`,
    secondary: `bg-[${theme.colors.secondary}]`,
    accent: `bg-[${theme.colors.accent}]`
  };
};

// Animation presets
export const animationPresets = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.3 }
  },
  slideInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.4, ease: "easeOut" }
  },
  slideInLeft: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.4, ease: "easeOut" }
  },
  slideInRight: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.4, ease: "easeOut" }
  },
  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.3, ease: "easeOut" }
  },
  staggerChildren: {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }
};
