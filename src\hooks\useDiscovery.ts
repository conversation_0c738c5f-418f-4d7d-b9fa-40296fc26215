
import { useToast } from "@/hooks/use-toast";
import { useAnalysisStore } from "@/stores/useAnalysisStore";
import { useLibraryStore } from "@/stores/useLibraryStore";
import { useNavigationStore } from "@/stores/useNavigationStore";
import { UserNote, AnalysisResult } from "@/types/conversation";

export const useDiscovery = () => {
  const { toast } = useToast();

  const discoverContext = (note: UserNote) => {
    const { savedAnalyses } = useLibraryStore.getState();
    const allResults: AnalysisResult[] = savedAnalyses.flatMap(sa => sa.results);
    const foundResults = allResults.filter(r => note.linkedAnswerIds.includes(r.id));
    
    if (foundResults.length === 0) {
        toast({
            title: "Context not found",
            description: "Could not find the original analysis results in your library.",
            variant: "destructive"
        });
        return;
    }

    const { clearAnalysis, setQuestion, setAnalysisResults } = useAnalysisStore.getState();
    clearAnalysis();
    setQuestion(foundResults[0].question);
    setAnalysisResults(() => foundResults);

    useNavigationStore.getState().setMainTab('analyze');    toast({
      title: "Context Loaded",
      description: "Switched to Conversation Analysis with the selected context.",
    });
  };

  return { discoverContext };
};
