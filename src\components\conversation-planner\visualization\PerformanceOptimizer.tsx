import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AnalysisResult } from '@/types/conversation';
import { VisualizationChain } from '@/types/visualization';

// Virtual scrolling hook for large datasets
export const useVirtualScrolling = (
  items: any[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleRange = useMemo(() => {
    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight) + overscan,
      items.length
    );
    
    return {
      start: Math.max(0, start - overscan),
      end
    };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end).map((item, index) => ({
      ...item,
      index: visibleRange.start + index
    }));
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.start * itemHeight;

  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop
  };
};

// Lazy loading component for visualization chains
interface LazyVisualizationChainProps {
  result: AnalysisResult;
  isVisible: boolean;
  onLoad?: () => void;
  children: (chain: VisualizationChain) => React.ReactNode;
}

export const LazyVisualizationChain: React.FC<LazyVisualizationChainProps> = ({
  result,
  isVisible,
  onLoad,
  children
}) => {
  const [chain, setChain] = useState<VisualizationChain | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isVisible && !chain && !isLoading) {
      setIsLoading(true);
      
      // Simulate async loading with requestIdleCallback for better performance
      const loadChain = () => {
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => {
            import('@/types/visualization').then(({ createVisualizationChain }) => {
              const newChain = createVisualizationChain(result);
              setChain(newChain);
              setIsLoading(false);
              onLoad?.();
            });
          });
        } else {
          // Fallback for browsers without requestIdleCallback
          setTimeout(() => {
            import('@/types/visualization').then(({ createVisualizationChain }) => {
              const newChain = createVisualizationChain(result);
              setChain(newChain);
              setIsLoading(false);
              onLoad?.();
            });
          }, 0);
        }
      };

      loadChain();
    }
  }, [isVisible, chain, isLoading, result, onLoad]);

  if (!isVisible) {
    return <div className="h-64 bg-gray-50 rounded-lg animate-pulse" />;
  }

  if (isLoading || !chain) {
    return (
      <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
        <div className="flex items-center gap-2 text-gray-500">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
          <span className="text-sm">Loading visualization...</span>
        </div>
      </div>
    );
  }

  return <>{children(chain)}</>;
};

// Intersection Observer hook for lazy loading
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        setEntry(entry);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [options]);

  return { ref: elementRef, isIntersecting, entry };
};

// Memoized visualization component
interface MemoizedVisualizationProps {
  result: AnalysisResult;
  viewMode: string;
  config: any;
  children: React.ReactNode;
}

export const MemoizedVisualization = React.memo<MemoizedVisualizationProps>(
  ({ result, viewMode, config, children }) => {
    return <>{children}</>;
  },
  (prevProps, nextProps) => {
    // Custom comparison function for better memoization
    return (
      prevProps.result.id === nextProps.result.id &&
      prevProps.viewMode === nextProps.viewMode &&
      JSON.stringify(prevProps.config) === JSON.stringify(nextProps.config)
    );
  }
);

// Performance monitoring hook
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    memoryUsage: 0,
    frameRate: 0
  });

  const measureRenderTime = useCallback((callback: () => void) => {
    const start = performance.now();
    callback();
    const end = performance.now();
    
    setMetrics(prev => ({
      ...prev,
      renderTime: end - start
    }));
  }, []);

  const measureMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      setMetrics(prev => ({
        ...prev,
        memoryUsage: memory.usedJSHeapSize / 1024 / 1024 // MB
      }));
    }
  }, []);

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();

    const measureFrameRate = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setMetrics(prev => ({
          ...prev,
          frameRate: frameCount
        }));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFrameRate);
    };

    const animationId = requestAnimationFrame(measureFrameRate);
    const memoryInterval = setInterval(measureMemoryUsage, 5000);

    return () => {
      cancelAnimationFrame(animationId);
      clearInterval(memoryInterval);
    };
  }, [measureMemoryUsage]);

  return { metrics, measureRenderTime };
};

// Debounced state hook for performance
export const useDebouncedState = <T>(
  initialValue: T,
  delay: number = 300
): [T, T, (value: T) => void] => {
  const [value, setValue] = useState<T>(initialValue);
  const [debouncedValue, setDebouncedValue] = useState<T>(initialValue);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return [value, debouncedValue, setValue];
};

// Optimized list renderer for large datasets
interface OptimizedListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemHeight: number;
  containerHeight: number;
  className?: string;
  onScroll?: (scrollTop: number) => void;
}

export function OptimizedList<T>({
  items,
  renderItem,
  itemHeight,
  containerHeight,
  className,
  onScroll
}: OptimizedListProps<T>) {
  const { visibleItems, totalHeight, offsetY, setScrollTop } = useVirtualScrolling(
    items,
    itemHeight,
    containerHeight
  );

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    setScrollTop(scrollTop);
    onScroll?.(scrollTop);
  }, [setScrollTop, onScroll]);

  return (
    <div
      className={className}
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div
              key={item.index || index}
              style={{ height: itemHeight }}
            >
              {renderItem(item, item.index || index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Performance-optimized animation wrapper
interface OptimizedAnimationProps {
  children: React.ReactNode;
  enabled: boolean;
  reduceMotion?: boolean;
}

export const OptimizedAnimation: React.FC<OptimizedAnimationProps> = ({
  children,
  enabled,
  reduceMotion = false
}) => {
  const prefersReducedMotion = useMemo(() => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }, []);

  const shouldAnimate = enabled && !reduceMotion && !prefersReducedMotion;

  if (!shouldAnimate) {
    return <>{children}</>;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
    >
      {children}
    </motion.div>
  );
};
