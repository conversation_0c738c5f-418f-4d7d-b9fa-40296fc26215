import { useEffect, useRef } from 'react';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';

interface AutoSaveOptions {
  enabled?: boolean;
  interval?: number; // in milliseconds
  onSave?: () => void;
  onError?: (error: Error) => void;
}

export const useAutoSave = (options: AutoSaveOptions = {}) => {
  const {
    enabled = true,
    interval = 30000, // 30 seconds default
    onSave,
    onError,
  } = options;

  const { autoSave, objects, layers } = useFigmaCanvasStore();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSaveRef = useRef<string>('');

  // Create a hash of the current state to detect changes
  const createStateHash = () => {
    const state = { objects, layers };
    return JSON.stringify(state);
  };

  const performAutoSave = async () => {
    try {
      const currentHash = createStateHash();
      
      // Only save if state has changed
      if (currentHash !== lastSaveRef.current) {
        await autoSave();
        lastSaveRef.current = currentHash;
        onSave?.();
      }
    } catch (error) {
      onError?.(error as Error);
    }
  };

  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Set up auto-save interval
    intervalRef.current = setInterval(performAutoSave, interval);

    // Initial state hash
    lastSaveRef.current = createStateHash();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enabled, interval]);

  // Save when component unmounts or page unloads
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (enabled) {
        // Synchronous save on page unload
        const currentHash = createStateHash();
        if (currentHash !== lastSaveRef.current) {
          // Use synchronous storage for page unload
          try {
            const state = useFigmaCanvasStore.getState();
            const canvasState = {
              objects: state.objects,
              layers: state.layers,
              selectedObjectIds: state.selectedObjectIds,
              activeLayerId: state.activeLayerId,
              activeTool: state.activeTool,
              zoom: state.zoom,
              pan: state.pan,
              gridVisible: state.gridVisible,
              snapToGrid: state.snapToGrid,
              gridSize: state.gridSize,
              canvasSize: state.canvasSize,
              backgroundColor: state.backgroundColor,
            };
            
            // Use localStorage as fallback for immediate save
            localStorage.setItem('figma-canvas-emergency-save', JSON.stringify({
              canvasState,
              timestamp: new Date().toISOString(),
            }));
          } catch (error) {
            console.error('Emergency save failed:', error);
          }
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // Perform final save
      if (enabled) {
        performAutoSave();
      }
    };
  }, [enabled]);

  return {
    saveNow: performAutoSave,
    isEnabled: enabled,
  };
};

// Hook for recovering from emergency save
export const useEmergencyRecover = () => {
  const { loadFromIndexedDB } = useFigmaCanvasStore();

  const checkForEmergencySave = (): { hasEmergencySave: boolean; timestamp?: Date } => {
    try {
      const emergencySave = localStorage.getItem('figma-canvas-emergency-save');
      if (emergencySave) {
        const data = JSON.parse(emergencySave);
        return {
          hasEmergencySave: true,
          timestamp: new Date(data.timestamp),
        };
      }
    } catch (error) {
      console.error('Failed to check emergency save:', error);
    }
    
    return { hasEmergencySave: false };
  };

  const recoverFromEmergencySave = async (): Promise<boolean> => {
    try {
      const emergencySave = localStorage.getItem('figma-canvas-emergency-save');
      if (!emergencySave) return false;

      const data = JSON.parse(emergencySave);
      const { canvasState } = data;

      // Load the emergency save data
      const store = useFigmaCanvasStore.getState();
      store.objects = canvasState.objects;
      store.layers = canvasState.layers;
      store.selectedObjectIds = canvasState.selectedObjectIds;
      store.activeLayerId = canvasState.activeLayerId;
      store.activeTool = canvasState.activeTool;
      store.zoom = canvasState.zoom;
      store.pan = canvasState.pan;
      store.gridVisible = canvasState.gridVisible;
      store.snapToGrid = canvasState.snapToGrid;
      store.gridSize = canvasState.gridSize;
      store.canvasSize = canvasState.canvasSize;
      store.backgroundColor = canvasState.backgroundColor;

      // Clear the emergency save
      localStorage.removeItem('figma-canvas-emergency-save');
      
      return true;
    } catch (error) {
      console.error('Failed to recover from emergency save:', error);
      return false;
    }
  };

  const clearEmergencySave = () => {
    localStorage.removeItem('figma-canvas-emergency-save');
  };

  return {
    checkForEmergencySave,
    recoverFromEmergencySave,
    clearEmergencySave,
  };
};

// Hook for managing saved canvases
export const useSavedCanvases = () => {
  const { saveToIndexedDB, loadFromIndexedDB } = useFigmaCanvasStore();

  const saveCanvas = async (name?: string): Promise<string> => {
    return await saveToIndexedDB(name);
  };

  const loadCanvas = async (id: string): Promise<void> => {
    await loadFromIndexedDB(id);
  };

  const getSavedCanvases = async () => {
    try {
      const { indexedDBManager } = await import('@/lib/storage/IndexedDBManager');
      await indexedDBManager.init();
      return await indexedDBManager.getAllCanvases();
    } catch (error) {
      console.error('Failed to get saved canvases:', error);
      return [];
    }
  };

  const deleteCanvas = async (id: string): Promise<void> => {
    try {
      const { indexedDBManager } = await import('@/lib/storage/IndexedDBManager');
      await indexedDBManager.init();
      await indexedDBManager.deleteCanvas(id);
    } catch (error) {
      console.error('Failed to delete canvas:', error);
      throw error;
    }
  };

  const getStorageUsage = async () => {
    try {
      const { indexedDBManager } = await import('@/lib/storage/IndexedDBManager');
      await indexedDBManager.init();
      return await indexedDBManager.getStorageUsage();
    } catch (error) {
      console.error('Failed to get storage usage:', error);
      return { used: 0, quota: 0 };
    }
  };

  return {
    saveCanvas,
    loadCanvas,
    getSavedCanvases,
    deleteCanvas,
    getStorageUsage,
  };
};
