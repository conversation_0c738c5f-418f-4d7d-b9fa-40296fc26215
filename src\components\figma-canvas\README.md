# Figma Canvas Components

A comprehensive Figma clone implementation built with React, TypeScript, and Fabric.js, integrated into the Chat Craft Trainer Pro Visual Canvas.

## Quick Start

```tsx
import { FigmaCanvasContainer } from '@/components/figma-canvas/FigmaCanvasContainer';

function App() {
  return (
    <FigmaCanvasContainer
      className="h-screen"
      showLayersPanel={true}
      showPropertiesPanel={true}
      onSelectionChange={(ids) => console.log('Selected:', ids)}
    />
  );
}
```

## Architecture

### Core Components

- **FigmaCanvasContainer**: Main orchestrator component
- **FigmaCanvas**: Core canvas using Fabric.js
- **FigmaToolbar**: Tool selection and controls
- **FigmaLayersPanel**: Layer management
- **FigmaPropertiesPanel**: Object property editing

### State Management

Uses Zustand for state management with the following stores:
- `useFigmaCanvasStore`: Main canvas state
- Persistent storage with IndexedDB integration
- Auto-save functionality

### Drawing Tools

All tools are implemented in the `tools/` directory:
- `DrawingToolsFactory`: Factory for creating drawing objects
- Individual tool implementations for each shape type

## Component Structure

```
figma-canvas/
├── FigmaCanvas.tsx                 # Core canvas component
├── FigmaCanvasContainer.tsx        # Main container
├── FigmaToolbar.tsx               # Toolbar component
├── FigmaLayersPanel.tsx           # Layers panel
├── FigmaPropertiesPanel.tsx       # Properties panel
├── tools/
│   └── DrawingToolsFactory.ts     # Tool factory
├── selection/
│   ├── SelectionManager.ts        # Selection handling
│   └── TransformControls.tsx      # Transform UI
├── styles/
│   ├── ColorPicker.tsx            # Color picker
│   ├── GradientEditor.tsx         # Gradient editor
│   └── StylePanel.tsx             # Style management
├── text/
│   ├── TextEditor.tsx             # Text editing
│   └── useTextEditing.ts          # Text editing hook
├── export/
│   ├── ExportManager.ts           # Export functionality
│   └── ExportImportDialog.tsx     # Export/import UI
├── import/
│   └── ImportManager.ts           # Import functionality
├── storage/
│   └── CanvasManagerDialog.tsx    # Canvas management
├── compatibility/
│   └── VisualCanvasIntegration.tsx # Integration layer
└── __tests__/                     # Test files
```

## Features

### Drawing Tools
- Rectangle, Circle, Line, Arrow
- Pen tool with smoothing
- Text with rich formatting
- Polygon and Star shapes
- Image import and placement

### Selection & Transform
- Single and multi-selection
- Marquee selection
- Move, resize, rotate operations
- Grouping and ungrouping
- Layer order management

### Styling
- Advanced color picker
- Gradient editor
- Stroke and fill properties
- Shadow effects
- Opacity controls

### Export/Import
- PNG, JPEG, SVG, PDF export
- Canvas data backup/restore
- Image import (PNG, JPEG, SVG)
- Drag and drop support

### Offline Storage
- Auto-save every 30 seconds
- IndexedDB for persistent storage
- Emergency backup on page close
- Canvas library management

## Usage Examples

### Basic Setup

```tsx
import { FigmaCanvasContainer } from '@/components/figma-canvas/FigmaCanvasContainer';

<FigmaCanvasContainer
  className="w-full h-full"
  showLayersPanel={true}
  showPropertiesPanel={true}
  showTransformControls={true}
/>
```

### Custom Event Handling

```tsx
<FigmaCanvasContainer
  onSelectionChange={(selectedIds) => {
    console.log('Selection changed:', selectedIds);
  }}
  onObjectCreated={(objectId) => {
    console.log('Object created:', objectId);
  }}
  onObjectUpdated={(objectId) => {
    console.log('Object updated:', objectId);
  }}
  onObjectDeleted={(objectId) => {
    console.log('Object deleted:', objectId);
  }}
/>
```

### Using the Store

```tsx
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';

function CustomComponent() {
  const {
    objects,
    selectedObjectIds,
    activeTool,
    setActiveTool,
    addObject,
    updateObject,
  } = useFigmaCanvasStore();

  const createRectangle = () => {
    const rectangle = DrawingToolsFactory.createRectangle(
      { x: 0, y: 0 },
      { x: 100, y: 100 },
      'layer-id'
    );
    addObject(rectangle);
  };

  return (
    <button onClick={createRectangle}>
      Create Rectangle
    </button>
  );
}
```

### Auto-save Integration

```tsx
import { useAutoSave } from '@/hooks/useAutoSave';

function MyCanvas() {
  useAutoSave({
    enabled: true,
    interval: 30000, // 30 seconds
    onSave: () => console.log('Canvas saved'),
    onError: (error) => console.error('Save failed:', error),
  });

  return <FigmaCanvasContainer />;
}
```

## Keyboard Shortcuts

### Tools
- `V` - Select tool
- `R` - Rectangle
- `O` - Circle
- `L` - Line
- `A` - Arrow
- `T` - Text
- `P` - Pen

### Operations
- `Ctrl/Cmd + Z` - Undo
- `Ctrl/Cmd + Y` - Redo
- `Ctrl/Cmd + D` - Duplicate
- `Delete` - Delete selected
- `Ctrl/Cmd + A` - Select all

### View
- `Ctrl/Cmd + 0` - Reset zoom
- `Ctrl/Cmd + +/-` - Zoom in/out
- `Space + Drag` - Pan

## Customization

### Custom Tools

```tsx
// Create a custom tool
const customTool = {
  type: 'custom',
  layerId: 'layer-id',
  transform: { x: 0, y: 0, width: 100, height: 100 },
  style: { fill: '#ff0000' },
  // ... other properties
};

// Add to canvas
const { addObject } = useFigmaCanvasStore();
addObject(customTool);
```

### Custom Styles

```tsx
// Apply custom styles
const { updateObject } = useFigmaCanvasStore();

updateObject(objectId, {
  style: {
    fill: '#ff0000',
    stroke: '#000000',
    strokeWidth: 2,
    opacity: 0.8,
    shadow: {
      color: '#000000',
      blur: 10,
      offsetX: 5,
      offsetY: 5,
    },
  },
});
```

## Testing

### Running Tests

```bash
npm test                    # Run all tests
npm run test:watch         # Watch mode
npm run test:coverage      # Coverage report
```

### Test Structure

```
__tests__/
├── FigmaCanvasStore.test.ts      # Store tests
├── DrawingToolsFactory.test.ts   # Tool factory tests
├── SelectionManager.test.ts      # Selection tests
└── components/                   # Component tests
```

### Example Test

```tsx
import { render, screen } from '@testing-library/react';
import { FigmaCanvasContainer } from '../FigmaCanvasContainer';

test('renders canvas container', () => {
  render(<FigmaCanvasContainer />);
  expect(screen.getByRole('toolbar')).toBeInTheDocument();
});
```

## Performance

### Optimization Tips

1. **Limit Object Count**: Keep under 1000 objects for best performance
2. **Optimize Images**: Compress images before import
3. **Use Layers**: Organize objects into layers for better management
4. **Debounce Updates**: Batch rapid state changes

### Memory Management

```tsx
// Proper cleanup
useEffect(() => {
  return () => {
    // Canvas cleanup is handled automatically
    // Custom cleanup code here if needed
  };
}, []);
```

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Dependencies

- React 18+
- TypeScript 4.5+
- Fabric.js 5.3+
- Zustand 4.0+
- Lucide React (icons)

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure TypeScript compliance

## License

Part of Chat Craft Trainer Pro - see main project license.
