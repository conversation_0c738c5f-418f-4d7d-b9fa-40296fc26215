# State Management Guide

## Overview

The application uses a standardized state management pattern built on top of Zustand. This guide explains the patterns, best practices, and how to create and use stores consistently across the application.

## Architecture

### Standardized Store Pattern

All stores follow a consistent pattern that includes:

- **Base State**: Common properties like loading, error, lastUpdated, version
- **Base Actions**: Standard methods for error handling, loading states, and reset functionality
- **Type Safety**: Comprehensive TypeScript interfaces
- **Error Handling**: Centralized error management with context
- **Persistence**: Optional state persistence with validation
- **Development Tools**: Debug utilities and performance monitoring

### Store Structure

```typescript
interface MyStore extends BaseStore {
  // Data properties
  data: MyData[];
  
  // Store-specific actions
  fetchData: () => Promise<void>;
  updateData: (id: string, updates: Partial<MyData>) => Promise<void>;
  deleteData: (id: string) => Promise<void>;
}
```

## Creating a New Store

### 1. Define Your Interfaces

```typescript
// Data interface
interface TodoData {
  todos: Todo[];
  filter: 'all' | 'active' | 'completed';
}

// Actions interface
interface TodoActions {
  addTodo: (text: string) => Promise<void>;
  toggleTodo: (id: string) => Promise<void>;
  deleteTodo: (id: string) => Promise<void>;
  setFilter: (filter: 'all' | 'active' | 'completed') => void;
}

// Combined store interface
interface TodoStore extends BaseStore, TodoData, TodoActions {}
```

### 2. Create the Store

```typescript
import { createStandardStore } from './base/createStandardStore';

export const useTodoStore = createStandardStore<TodoStore>(
  {
    name: "todos",
    version: "1.0.0",
    persist: true,
    persistConfig: {
      partialize: (state) => ({
        todos: state.todos,
        filter: state.filter,
      }),
    },
    initialState: {
      todos: [],
      filter: 'all',
    },
  },
  (set, get, api, errorHandler, loadingHandler) => ({
    addTodo: async (text: string) => {
      return loadingHandler.withLoading(async () => {
        if (!text.trim()) {
          throw new Error('Todo text cannot be empty');
        }

        const newTodo: Todo = {
          id: generateId(),
          text: text.trim(),
          completed: false,
          createdAt: Date.now(),
        };

        set((state) => {
          state.todos.push(newTodo);
          state.lastUpdated = Date.now();
        });

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 100));
      }, 'Add Todo');
    },

    toggleTodo: async (id: string) => {
      return loadingHandler.withLoading(async () => {
        set((state) => {
          const todo = state.todos.find(t => t.id === id);
          if (todo) {
            todo.completed = !todo.completed;
            state.lastUpdated = Date.now();
          }
        });

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 50));
      }, 'Toggle Todo');
    },

    deleteTodo: async (id: string) => {
      return loadingHandler.withLoading(async () => {
        set((state) => {
          state.todos = state.todos.filter(t => t.id !== id);
          state.lastUpdated = Date.now();
        });

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 50));
      }, 'Delete Todo');
    },

    setFilter: (filter: 'all' | 'active' | 'completed') => {
      set((state) => {
        state.filter = filter;
        state.lastUpdated = Date.now();
      });
    },
  })
);
```

## Error Handling

### Global Error Store

The application includes a centralized error store for consistent error management:

```typescript
import { useErrorStore, useErrorHandler } from '@/stores/useErrorStore';

function MyComponent() {
  const { handleError, handleAsyncError } = useErrorHandler();
  
  const handleSubmit = async () => {
    const result = await handleAsyncError(
      () => submitData(),
      'Submit Form',
      'high',
      'validation'
    );
    
    if (result) {
      // Success
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* form content */}
    </form>
  );
}
```

### Store-Level Error Handling

Each store has built-in error handling:

```typescript
// In your store implementation
updateData: async (id: string, updates: Partial<MyData>) => {
  return loadingHandler.withLoading(async () => {
    // Validation
    if (!id) {
      throw new Error('ID is required');
    }

    // Optimistic update
    const originalState = get();
    set((state) => {
      const item = state.data.find(d => d.id === id);
      if (item) {
        Object.assign(item, updates);
        state.lastUpdated = Date.now();
      }
    });

    try {
      // API call
      await api.updateData(id, updates);
    } catch (error) {
      // Rollback on failure
      set(originalState);
      throw error;
    }
  }, 'Update Data');
},
```

## Best Practices

### 1. Use Immer for State Updates

All stores use Immer middleware for immutable updates:

```typescript
// ✅ Good - Direct mutation with Immer
set((state) => {
  state.todos.push(newTodo);
  state.lastUpdated = Date.now();
});

// ❌ Avoid - Manual immutable updates
set((state) => ({
  ...state,
  todos: [...state.todos, newTodo],
  lastUpdated: Date.now(),
}));
```

### 2. Handle Loading States

Use the loading handler for async operations:

```typescript
// ✅ Good - Using loading handler
const result = await loadingHandler.withLoading(
  () => fetchData(),
  'Fetch Data'
);

// ❌ Avoid - Manual loading state management
setLoading(true);
try {
  await fetchData();
} finally {
  setLoading(false);
}
```

### 3. Validate Input

Always validate input data:

```typescript
addUser: async (userData: CreateUserData) => {
  return loadingHandler.withLoading(async () => {
    // Validation
    if (!userData.email || !isValidEmail(userData.email)) {
      throw new Error('Valid email is required');
    }
    
    if (!userData.name || userData.name.trim().length < 2) {
      throw new Error('Name must be at least 2 characters');
    }

    // Proceed with creation
    // ...
  }, 'Add User');
},
```

### 4. Use Optimistic Updates

For better UX, apply optimistic updates:

```typescript
deleteItem: async (id: string) => {
  const originalState = get();
  
  // Optimistic update
  set((state) => {
    state.items = state.items.filter(item => item.id !== id);
    state.lastUpdated = Date.now();
  });

  try {
    await api.deleteItem(id);
  } catch (error) {
    // Rollback on failure
    set(originalState);
    throw error;
  }
},
```

### 5. Implement Proper Persistence

Configure persistence appropriately:

```typescript
export const useMyStore = createStandardStore<MyStore>(
  {
    name: "my_store",
    persist: true,
    persistConfig: {
      // Only persist necessary data
      partialize: (state) => ({
        data: state.data,
        settings: state.settings,
        // Don't persist loading states or errors
      }),
      
      // Handle rehydration
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Reset transient state
          state.loading = false;
          state.error = null;
        }
      },
    },
    // ...
  },
  // ...
);
```

## Store Composition

### Combining Multiple Stores

```typescript
// In a component
function MyComponent() {
  const { todos, addTodo, loading: todosLoading } = useTodoStore();
  const { user, loading: userLoading } = useUserStore();
  const { openModal } = useUIStore();
  
  const isLoading = todosLoading || userLoading;
  
  return (
    <div>
      {isLoading && <LoadingSpinner />}
      {/* component content */}
    </div>
  );
}
```

### Store Dependencies

```typescript
// In a custom hook
export const useAppData = () => {
  const todoStore = useTodoStore();
  const userStore = useUserStore();
  const uiStore = useUIStore();
  
  const isAnyLoading = todoStore.loading || userStore.loading || uiStore.loading;
  
  const refreshAll = async () => {
    await Promise.all([
      todoStore.fetchTodos(),
      userStore.fetchUser(),
    ]);
  };
  
  return {
    ...todoStore,
    ...userStore,
    ...uiStore,
    isAnyLoading,
    refreshAll,
  };
};
```

## Testing Stores

### Unit Testing

```typescript
import { renderHook, act } from '@testing-library/react';
import { useTodoStore } from './useTodoStore';

describe('useTodoStore', () => {
  beforeEach(() => {
    useTodoStore.getState().reset();
  });

  it('should add a todo', async () => {
    const { result } = renderHook(() => useTodoStore());
    
    await act(async () => {
      await result.current.addTodo('Test todo');
    });
    
    expect(result.current.todos).toHaveLength(1);
    expect(result.current.todos[0].text).toBe('Test todo');
  });

  it('should handle errors', async () => {
    const { result } = renderHook(() => useTodoStore());
    
    await act(async () => {
      await result.current.addTodo(''); // Invalid input
    });
    
    expect(result.current.error).toBeTruthy();
    expect(result.current.todos).toHaveLength(0);
  });
});
```

## Migration Guide

### From Old Stores

1. **Identify the current pattern**
2. **Extract data and actions**
3. **Create new interfaces**
4. **Implement using createStandardStore**
5. **Update components gradually**
6. **Test thoroughly**

### Example Migration

```typescript
// Old store
const useOldStore = create((set) => ({
  data: [],
  loading: false,
  fetchData: async () => {
    set({ loading: true });
    try {
      const data = await api.fetchData();
      set({ data, loading: false });
    } catch (error) {
      set({ loading: false });
      console.error(error);
    }
  },
}));

// New store
const useNewStore = createStandardStore<NewStore>(
  {
    name: "new_store",
    initialState: { data: [] },
  },
  (set, get, api, errorHandler, loadingHandler) => ({
    fetchData: async () => {
      return loadingHandler.withLoading(async () => {
        const data = await api.fetchData();
        set((state) => {
          state.data = data;
          state.lastUpdated = Date.now();
        });
      }, 'Fetch Data');
    },
  })
);
```

## Performance Considerations

1. **Use selectors for specific data**
2. **Avoid unnecessary re-renders**
3. **Implement proper memoization**
4. **Monitor store performance**
5. **Use lazy loading for large datasets**

## Debugging

### Development Tools

```typescript
// Enable debugging in development
if (process.env.NODE_ENV === 'development') {
  // Access store state
  console.log('Store state:', useMyStore.getState());
  
  // Subscribe to changes
  useMyStore.subscribe((state) => {
    console.log('State changed:', state);
  });
}
```

### Error Monitoring

```typescript
// Monitor errors globally
const { errors } = useErrorStore();

useEffect(() => {
  if (errors.length > 0) {
    console.log('Active errors:', errors);
  }
}, [errors]);
```
