# Figma Canvas Integration

This document describes the comprehensive Figma clone functionality integrated into the Chat Craft Trainer Pro Visual Canvas.

## Overview

The Figma Canvas integration provides a full-featured design tool within the existing Visual Canvas, allowing users to create, edit, and manage visual designs alongside their conversation analysis. The integration maintains compatibility with existing note and connection features while adding powerful design capabilities.

## Features

### Core Drawing Tools

#### Basic Shapes
- **Rectangle Tool**: Create rectangles and squares with customizable corner radius
- **Circle Tool**: Create circles and ellipses with precise control
- **Line Tool**: Draw straight lines and polylines
- **Arrow Tool**: Create arrows with customizable arrowhead size

#### Advanced Tools
- **Pen Tool**: Freehand drawing with smoothing and pressure sensitivity
- **Text Tool**: Rich text editing with font selection, sizing, and styling
- **Polygon Tool**: Create regular polygons with configurable sides (3-20)
- **Star Tool**: Create stars with customizable points and inner/outer radius

### Selection and Transform

#### Selection Tools
- **Single Selection**: Click to select individual objects
- **Multi-Selection**: Ctrl/Cmd+click to add to selection
- **Marquee Selection**: Drag to select multiple objects in a rectangle
- **Select All**: Ctrl/Cmd+A to select all objects

#### Transform Operations
- **Move**: Drag objects or use arrow keys for precise positioning
- **Resize**: Drag corner handles to resize objects
- **Rotate**: Use rotation handle or keyboard shortcuts
- **Scale**: Proportional scaling with Shift key
- **Flip**: Horizontal and vertical flipping

### Layer Management

#### Layer Operations
- **Create Layers**: Organize objects into separate layers
- **Layer Visibility**: Show/hide layers individually
- **Layer Locking**: Lock layers to prevent accidental edits
- **Layer Ordering**: Reorder layers with drag and drop
- **Layer Naming**: Custom names for better organization

#### Object Hierarchy
- **Grouping**: Group objects together for easier manipulation
- **Ungrouping**: Break apart groups when needed
- **Bring to Front/Back**: Control object stacking order
- **Send Forward/Backward**: Fine-tune object layering

### Styling and Colors

#### Color Management
- **Color Picker**: Advanced color picker with HSV controls
- **Gradient Editor**: Create linear and radial gradients
- **Color Palette**: Predefined color swatches
- **Recent Colors**: Quick access to recently used colors

#### Style Properties
- **Fill**: Solid colors, gradients, or transparent
- **Stroke**: Border colors, widths, and patterns
- **Opacity**: Global and per-object transparency
- **Shadow**: Drop shadows with blur and offset controls

### Text Editing

#### Text Features
- **Font Selection**: Choose from system fonts
- **Font Sizing**: Precise font size control
- **Text Styling**: Bold, italic, underline, strikethrough
- **Text Alignment**: Left, center, right, justify
- **Line Height**: Control line spacing
- **Letter Spacing**: Adjust character spacing

#### Inline Editing
- **Double-click Editing**: Quick text editing
- **Rich Text Editor**: Advanced text formatting
- **Auto-resize**: Automatic text box sizing
- **Text Wrapping**: Control text flow

### Export and Import

#### Export Formats
- **PNG**: High-quality raster images
- **JPEG**: Compressed images with quality control
- **SVG**: Scalable vector graphics
- **PDF**: Print-ready documents
- **JSON**: Canvas data for backup/sharing

#### Import Capabilities
- **Images**: PNG, JPEG, SVG, GIF support
- **Canvas Data**: Import previously saved canvases
- **Drag and Drop**: Easy file importing
- **Batch Import**: Multiple file support

### Offline Storage

#### Auto-save
- **Automatic Saving**: Every 30 seconds
- **Emergency Backup**: On page close/refresh
- **Version Control**: Multiple save states
- **Recovery**: Automatic recovery on restart

#### Storage Management
- **IndexedDB**: Robust offline storage
- **Canvas Library**: Save and load multiple canvases
- **Storage Usage**: Monitor storage consumption
- **Data Export**: Backup canvas data

## Integration with Visual Canvas

### Compatibility
- **Existing Features**: Full compatibility with notes and connections
- **Data Sync**: Bidirectional sync between analysis and design modes
- **Import/Export**: Convert between note formats and design objects
- **Unified Interface**: Seamless switching between modes

### Tab Interface
- **Analysis Canvas**: Original note and connection features
- **Design Canvas**: Full Figma clone functionality
- **Integration Toolbar**: Tools for data exchange between modes

## Keyboard Shortcuts

### Tool Selection
- `V` - Select tool
- `R` - Rectangle tool
- `O` - Circle tool
- `L` - Line tool
- `A` - Arrow tool
- `T` - Text tool
- `P` - Pen tool
- `Y` - Polygon tool
- `S` - Star tool

### Operations
- `Ctrl/Cmd + Z` - Undo
- `Ctrl/Cmd + Y` - Redo
- `Ctrl/Cmd + D` - Duplicate
- `Ctrl/Cmd + G` - Group
- `Ctrl/Cmd + Shift + G` - Ungroup
- `Delete/Backspace` - Delete selected
- `Ctrl/Cmd + A` - Select all

### View Controls
- `Ctrl/Cmd + 0` - Reset zoom
- `Ctrl/Cmd + +` - Zoom in
- `Ctrl/Cmd + -` - Zoom out
- `Ctrl/Cmd + 1` - Fit to screen
- `Space + Drag` - Pan canvas

## API Reference

### Core Components

#### FigmaCanvasContainer
Main container component that orchestrates all Figma functionality.

```tsx
<FigmaCanvasContainer
  showLayersPanel={true}
  showPropertiesPanel={true}
  showTransformControls={true}
  onSelectionChange={(ids) => console.log('Selected:', ids)}
  onObjectCreated={(id) => console.log('Created:', id)}
/>
```

#### FigmaCanvas
Core canvas component using Fabric.js for rendering.

#### FigmaToolbar
Toolbar with all drawing tools and controls.

#### FigmaLayersPanel
Layer management interface.

#### FigmaPropertiesPanel
Object property editing interface.

### Store Management

#### useFigmaCanvasStore
Zustand store managing all canvas state.

```typescript
const {
  objects,
  layers,
  selectedObjectIds,
  addObject,
  updateObject,
  deleteObject,
  selectObjects,
  // ... other methods
} = useFigmaCanvasStore();
```

### Hooks

#### useAutoSave
Automatic saving functionality.

```typescript
useAutoSave({
  enabled: true,
  interval: 30000,
  onSave: () => console.log('Saved'),
  onError: (error) => console.error('Save failed:', error),
});
```

#### useSavedCanvases
Canvas management operations.

```typescript
const {
  saveCanvas,
  loadCanvas,
  getSavedCanvases,
  deleteCanvas,
} = useSavedCanvases();
```

## Performance Considerations

### Optimization Strategies
- **Object Pooling**: Reuse Fabric.js objects
- **Viewport Culling**: Only render visible objects
- **Debounced Updates**: Batch state updates
- **Lazy Loading**: Load tools on demand

### Memory Management
- **Cleanup**: Proper disposal of Fabric.js objects
- **Image Optimization**: Compress imported images
- **History Limits**: Limit undo/redo history size

## Browser Compatibility

### Supported Browsers
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Required Features
- Canvas API
- IndexedDB
- File API
- Drag and Drop API

## Troubleshooting

### Common Issues

#### Canvas Not Rendering
- Check browser compatibility
- Verify Fabric.js initialization
- Check console for errors

#### Performance Issues
- Reduce object count
- Optimize image sizes
- Check memory usage

#### Storage Issues
- Check IndexedDB support
- Verify storage quota
- Clear browser data if needed

## Future Enhancements

### Planned Features
- **Collaborative Editing**: Real-time collaboration
- **Advanced Animations**: Keyframe animations
- **Plugin System**: Extensible tool architecture
- **Cloud Sync**: Optional cloud storage
- **Advanced Typography**: Better text handling
- **Vector Editing**: Bezier curve editing

### Performance Improvements
- **WebGL Rendering**: Hardware acceleration
- **Web Workers**: Background processing
- **Streaming**: Large file handling
- **Caching**: Intelligent asset caching

## Contributing

### Development Setup
1. Install dependencies: `npm install`
2. Run tests: `npm test`
3. Start development: `npm run dev`

### Code Structure
```
src/components/figma-canvas/
├── tools/              # Drawing tools
├── selection/          # Selection and transform
├── styles/            # Color and style management
├── export/            # Export functionality
├── import/            # Import functionality
├── storage/           # Offline storage
└── __tests__/         # Test files
```

### Testing
- Unit tests with Vitest
- Component tests with React Testing Library
- E2E tests with Playwright
- Visual regression tests

For more information, see the individual component documentation and API references.
