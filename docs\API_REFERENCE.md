# Living Data Canvas API Reference

## Table of Contents

1. [Components](#components)
2. [Hooks](#hooks)
3. [Services](#services)
4. [Types](#types)
5. [Utilities](#utilities)

## Components

### LivingDataCanvas

The main canvas component for visualizing chat analysis records.

```tsx
interface LivingDataCanvasProps {
  analysisResults: AnalysisResult[];
  enableChatAnalysisIntegration?: boolean;
  enableRealtimeUpdates?: boolean;
  initialData?: CanvasData;
  onNodeSelect?: (nodeId: string, analysisResult?: AnalysisResult) => void;
  onClusterCreate?: (cluster: ClusterData) => void;
  onSimulationComplete?: (results: SimulationResults) => void;
  debugMode?: boolean;
  showPerformanceMetrics?: boolean;
}
```

**Props:**
- `analysisResults`: Array of analysis results to visualize
- `enableChatAnalysisIntegration`: Enable chat analysis features
- `enableRealtimeUpdates`: Enable real-time data updates
- `initialData`: Initial canvas data (optional)
- `onNodeSelect`: Callback when a node is selected
- `onClusterCreate`: Callback when a cluster is created
- `onSimulationComplete`: Callback when simulation completes
- `debugMode`: Enable debug mode for development
- `showPerformanceMetrics`: Show performance metrics overlay

### AdvancedControlPanel

Advanced UI controls for canvas customization.

```tsx
interface AdvancedControlPanelProps {
  isVisible: boolean;
  onToggle: () => void;
  onLayoutChange: (algorithm: string, parameters: any) => void;
  onVisibilityToggle: (element: string, visible: boolean) => void;
  onThemeChange: (theme: string) => void;
  onExport: (format: string) => void;
  onImport: (file: File) => void;
  onReset: () => void;
  onAnimationToggle?: (enabled: boolean) => void;
  onSoundToggle?: (enabled: boolean) => void;
  onAccessibilityChange?: (setting: string, value: any) => void;
  currentSettings: CanvasSettings;
}
```

### KeyboardShortcutsManager

Keyboard shortcuts management and display.

```tsx
interface KeyboardShortcutsManagerProps {
  isVisible: boolean;
  onToggle: () => void;
  shortcuts: KeyboardShortcut[];
  onShortcutExecute?: (shortcutId: string) => void;
}

interface KeyboardShortcut {
  id: string;
  keys: string[];
  description: string;
  category: string;
  action: () => void;
  enabled?: boolean;
}
```

### ContextualHelpSystem

Built-in help system with searchable documentation.

```tsx
interface ContextualHelpSystemProps {
  isVisible: boolean;
  onToggle: () => void;
  currentContext?: string;
  onTopicSelect?: (topicId: string) => void;
}
```

## Hooks

### useChatAnalysisCanvas

Main hook for managing canvas data and operations.

```tsx
const {
  canvasData,
  isLoading,
  error,
  clusters,
  simulations,
  loadCanvasData,
  createCluster,
  updateCluster,
  deleteCluster,
  createSimulation,
  runSimulation
} = useChatAnalysisCanvas();
```

**Returns:**
- `canvasData`: Current canvas data
- `isLoading`: Loading state
- `error`: Error state
- `clusters`: Available clusters
- `simulations`: Available simulations
- `loadCanvasData`: Function to load canvas data
- `createCluster`: Function to create a new cluster
- `updateCluster`: Function to update a cluster
- `deleteCluster`: Function to delete a cluster
- `createSimulation`: Function to create a simulation
- `runSimulation`: Function to run a simulation

### useDataProcessingWorker

Hook for using web workers for heavy data processing.

```tsx
const {
  processAnalysisResults,
  calculateSimilarities,
  calculateForceDirectedLayout,
  processSearch,
  isWorkerAvailable,
  getPendingTaskCount
} = useDataProcessingWorker();
```

**Returns:**
- `processAnalysisResults`: Process analysis results in worker
- `calculateSimilarities`: Calculate similarities in worker
- `calculateForceDirectedLayout`: Calculate layout in worker
- `processSearch`: Process search queries in worker
- `isWorkerAvailable`: Whether web worker is available
- `getPendingTaskCount`: Number of pending worker tasks

### useKeyboardShortcuts

Hook for managing keyboard shortcuts.

```tsx
const {
  isVisible,
  executedShortcuts,
  toggleVisibility,
  handleShortcutExecute
} = useKeyboardShortcuts(shortcuts);
```

### useContextualHelp

Hook for managing contextual help system.

```tsx
const {
  isVisible,
  currentContext,
  viewedTopics,
  toggleVisibility,
  setContext,
  markTopicViewed
} = useContextualHelp();
```

## Services

### ChatAnalysisCanvasService

Service for converting analysis results to canvas data.

```tsx
class ChatAnalysisCanvasService {
  static analysisResultsToCanvasData(
    results: AnalysisResult[],
    connections: ConnectionData[],
    clusters: ClusterData[]
  ): CanvasData;

  static generateConnectionsFromAnalysisResults(
    results: AnalysisResult[]
  ): ConnectionData[];

  static calculateSimilarity(
    result1: AnalysisResult,
    result2: AnalysisResult
  ): number;
}
```

### PerformanceProfiler

Service for monitoring and profiling performance.

```tsx
class PerformanceProfiler {
  startTiming(name: string): void;
  endTiming(name: string): number;
  recordMetric(name: string, data: any): void;
  getPerformanceSummary(): PerformanceSummary;
  clearMetrics(): void;
  exportMetrics(): string;
}
```

### API Services

#### ChatAnalysisApi

```tsx
class ChatAnalysisApi {
  static async getAllAnalysisResults(): Promise<AnalysisResult[]>;
  static async createAnalysisResult(result: AnalysisResult): Promise<AnalysisResult>;
  static async updateAnalysisResult(id: string, updates: Partial<AnalysisResult>): Promise<AnalysisResult>;
  static async deleteAnalysisResult(id: string): Promise<void>;
}
```

#### ClusterApi

```tsx
class ClusterApi {
  static async getAllClusters(): Promise<ClusterData[]>;
  static async createCluster(cluster: ClusterData): Promise<ClusterData>;
  static async updateCluster(id: string, updates: Partial<ClusterData>): Promise<ClusterData>;
  static async deleteCluster(id: string): Promise<void>;
}
```

#### SimulationApi

```tsx
class SimulationApi {
  static async getAllSimulations(): Promise<SimulationData[]>;
  static async createSimulation(simulation: SimulationData): Promise<SimulationData>;
  static async runSimulation(id: string): Promise<SimulationResults>;
  static async getSimulationResults(id: string): Promise<SimulationResults>;
}
```

## Types

### Core Types

```tsx
interface AnalysisResult {
  id: string;
  question: string;
  analysis: string;
  analysisType: 'multiple' | 'deep' | 'character' | 'pros-cons';
  model: string;
  timestamp: string;
  rating?: number;
  metadata?: {
    tokens?: number;
    processingTime?: number;
    [key: string]: any;
  };
}

interface CanvasData {
  nodes: NodeData[];
  connections: ConnectionData[];
  clusters: ClusterData[];
  simulations: SimulationData[];
  metadata: {
    lastUpdated: Date;
    version: string;
  };
}

interface NodeData {
  id: string;
  label: string;
  category?: string;
  value?: number;
  color?: number;
  position?: { x: number; y: number };
  analysisResult?: AnalysisResult;
}

interface ConnectionData {
  id: string;
  sourceId: string;
  targetId: string;
  strength: number;
  type: string;
  metadata?: any;
}

interface ClusterData {
  id: string;
  name: string;
  description: string;
  nodeIds: string[];
  color: number;
  position?: { x: number; y: number };
  metadata?: any;
}

interface SimulationData {
  id: string;
  name: string;
  description: string;
  type: 'exploration' | 'validation' | 'comparison';
  sourceNodeIds: string[];
  prompts: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  results?: SimulationResults;
  metadata?: any;
}
```

### Performance Types

```tsx
interface PerformanceSummary {
  timingMetrics: Record<string, TimingMetricSummary>;
  longTaskCount: number;
  totalBlockingTime: number;
  cumulativeLayoutShift: number;
  memoryUsage: MemoryUsage;
  recommendations: string[];
}

interface TimingMetricSummary {
  count: number;
  average: number;
  median: number;
  p95: number;
  min: number;
  max: number;
}

interface MemoryUsage {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  usagePercentage: number;
}
```

## Utilities

### Performance Decorators

```tsx
// Automatic performance timing decorator
@performanceTimer('methodName')
methodName() {
  // Method implementation
}
```

### Event Handlers

```tsx
// Node selection handler
const handleNodeSelect = (nodeId: string, analysisResult?: AnalysisResult) => {
  console.log('Node selected:', nodeId, analysisResult);
};

// Cluster creation handler
const handleClusterCreate = (cluster: ClusterData) => {
  console.log('Cluster created:', cluster);
};

// Simulation completion handler
const handleSimulationComplete = (results: SimulationResults) => {
  console.log('Simulation completed:', results);
};
```

### Configuration Objects

```tsx
// Layout options
const layoutOptions: ForceDirectedLayoutOptions = {
  repulsionStrength: 1000,
  attractionStrength: 0.1,
  restLength: 100,
  dampingFactor: 0.9,
  maxSpeed: 10,
  centerForceStrength: 0.01,
  iterationsPerFrame: 1
};

// Canvas settings
const canvasSettings: CanvasSettings = {
  layout: { algorithm: 'force-directed', parameters: layoutOptions },
  visibility: {
    nodes: true,
    connections: true,
    clusters: true,
    labels: true,
    background: true,
    ui: true
  },
  theme: 'dark',
  performance: {
    enableLOD: true,
    enableWorkers: true,
    maxNodes: 1000,
    targetFPS: 60
  }
};
```

## Error Handling

### Common Error Types

```tsx
interface CanvasError {
  type: 'LOAD_ERROR' | 'RENDER_ERROR' | 'WORKER_ERROR' | 'API_ERROR';
  message: string;
  details?: any;
  timestamp: Date;
}

// Error handling example
try {
  await loadCanvasData();
} catch (error) {
  if (error instanceof CanvasError) {
    console.error(`Canvas error (${error.type}):`, error.message);
  } else {
    console.error('Unexpected error:', error);
  }
}
```

### Error Recovery

```tsx
// Automatic error recovery
const handleError = (error: CanvasError) => {
  switch (error.type) {
    case 'WORKER_ERROR':
      // Fallback to main thread processing
      processDataOnMainThread();
      break;
    case 'API_ERROR':
      // Use cached data
      loadCachedData();
      break;
    case 'RENDER_ERROR':
      // Reduce visual complexity
      enablePerformanceMode();
      break;
  }
};
```
