
import { FabricObject, ActiveSelection, ModifiedEvent, Canvas } from 'fabric';
import { CustomFabricObject, CustomLineObject, NODE_HEIGHT, NODE_WIDTH } from '../../fabric-helpers';

interface ObjectHandlerProps {
    canvas: Canvas | null;
    onNodeMove: (updates: { id: string; x: number; y: number }[]) => void;
}

export const useObjectHandler = ({ canvas, onNodeMove }: ObjectHandlerProps) => {    const handleObjectMoving = (e: { target?: FabricObject }) => {
        if (!e.target || !canvas) return;

        try {
            const nodeObjects: { [id: string]: FabricObject } = {};
            const lineObjects: CustomLineObject[] = [];
            canvas.getObjects().forEach(obj => {
                const customObj = obj as CustomFabricObject;
                if (customObj.data?.id && obj.type !== 'line') {
                    nodeObjects[customObj.data.id] = customObj;
                } else if ((obj as CustomLineObject).data?.type === 'connection') {
                    lineObjects.push(obj as CustomLineObject);
                }
            });

            const updateLinesForNode = (node: FabricObject) => {
                const nodeId = (node as CustomFabricObject).data?.id;
                if (!nodeId) return;

                lineObjects.forEach(line => {
                    if (line.data?.fromId === nodeId || line.data?.toId === nodeId) {
                        const fromNode = nodeObjects[line.data!.fromId];
                        const toNode = nodeObjects[line.data!.toId];
                        if (fromNode && toNode && 
                            fromNode.left !== undefined && fromNode.top !== undefined &&
                            toNode.left !== undefined && toNode.top !== undefined) {
                            line.set({
                                x1: fromNode.left + NODE_WIDTH / 2, 
                                y1: fromNode.top + NODE_HEIGHT / 2,
                                x2: toNode.left + NODE_WIDTH / 2, 
                                y2: toNode.top + NODE_HEIGHT / 2,
                            });
                            line.setCoords();
                        }
                    }
                });
            };

            if (e.target.type === 'activeSelection') {
                (e.target as ActiveSelection).forEachObject(obj => updateLinesForNode(obj));
            } else {
                updateLinesForNode(e.target);
            }
        } catch (error) {
            console.warn('Object moving handling failed:', error);
        }
    };const handleObjectModified = (e: ModifiedEvent) => {
        if (!e.target) return;
        const updates: { id: string; x: number; y: number }[] = [];

        try {
            if (e.target.type === 'activeSelection') {
                (e.target as ActiveSelection).forEachObject((obj: FabricObject) => {
                    const data = (obj as CustomFabricObject).data;
                    if (data?.id && obj.left !== undefined && obj.top !== undefined) {
                        const point = obj.getPointByOrigin('left', 'top');
                        updates.push({ id: data.id, x: point.x || 0, y: point.y || 0 });
                    }
                });
            } else {
                const data = (e.target as CustomFabricObject).data;
                if (data?.id && e.target.left !== undefined && e.target.top !== undefined) {
                    updates.push({ id: data.id, x: e.target.left, y: e.target.top });
                }
            }

            if (updates.length > 0) onNodeMove(updates);
        } catch (error) {
            console.warn('Object modification handling failed:', error);
        }
    };

    return { handleObjectMoving, handleObjectModified };
};
