/**
 * Global Error Store
 * 
 * Centralized error management for the entire application.
 * Provides consistent error handling, logging, and user feedback.
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// Error severity levels
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

// Error categories for better organization
export type ErrorCategory = 
  | 'network'
  | 'validation'
  | 'authentication'
  | 'authorization'
  | 'data'
  | 'ui'
  | 'system'
  | 'unknown';

// Error interface
export interface AppError {
  id: string;
  message: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  context?: string;
  timestamp: number;
  stack?: string;
  metadata?: Record<string, any>;
  dismissed?: boolean;
  retryable?: boolean;
}

// Error store state
interface ErrorState {
  errors: AppError[];
  globalError: AppError | null;
  isErrorBoundaryActive: boolean;
}

// Error store actions
interface ErrorActions {
  // Error management
  addError: (error: Omit<AppError, 'id' | 'timestamp'>) => string;
  removeError: (id: string) => void;
  dismissError: (id: string) => void;
  clearErrors: () => void;
  clearErrorsByCategory: (category: ErrorCategory) => void;
  
  // Global error handling
  setGlobalError: (error: AppError | null) => void;
  clearGlobalError: () => void;
  
  // Error boundary management
  setErrorBoundaryActive: (active: boolean) => void;
  
  // Utility methods
  getErrorsByCategory: (category: ErrorCategory) => AppError[];
  getErrorsBySeverity: (severity: ErrorSeverity) => AppError[];
  hasErrors: () => boolean;
  hasCriticalErrors: () => boolean;
}

// Combined store interface
interface ErrorStore extends ErrorState, ErrorActions {}

// Error creation utilities
export const createError = (
  message: string,
  severity: ErrorSeverity = 'medium',
  category: ErrorCategory = 'unknown',
  context?: string,
  metadata?: Record<string, any>
): Omit<AppError, 'id' | 'timestamp'> => ({
  message,
  severity,
  category,
  context,
  metadata,
  retryable: category === 'network' || category === 'system',
});

// Error severity helpers
export const isHighSeverity = (error: AppError): boolean => 
  error.severity === 'high' || error.severity === 'critical';

export const isCriticalError = (error: AppError): boolean => 
  error.severity === 'critical';

// Generate unique error ID
const generateErrorId = (): string => 
  `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Create the error store
export const useErrorStore = create<ErrorStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      errors: [],
      globalError: null,
      isErrorBoundaryActive: false,

      // Error management
      addError: (errorData) => {
        const id = generateErrorId();
        const error: AppError = {
          ...errorData,
          id,
          timestamp: Date.now(),
        };

        set((state) => ({
          errors: [...state.errors, error],
        }));

        // Set as global error if critical
        if (error.severity === 'critical') {
          set({ globalError: error });
        }

        // Log error for debugging
        console.error(`[${error.severity.toUpperCase()}] ${error.category}:`, {
          message: error.message,
          context: error.context,
          metadata: error.metadata,
          stack: error.stack,
        });

        return id;
      },

      removeError: (id) => {
        set((state) => ({
          errors: state.errors.filter(error => error.id !== id),
          globalError: state.globalError?.id === id ? null : state.globalError,
        }));
      },

      dismissError: (id) => {
        set((state) => ({
          errors: state.errors.map(error => 
            error.id === id ? { ...error, dismissed: true } : error
          ),
        }));
      },

      clearErrors: () => {
        set({
          errors: [],
          globalError: null,
        });
      },

      clearErrorsByCategory: (category) => {
        set((state) => ({
          errors: state.errors.filter(error => error.category !== category),
          globalError: state.globalError?.category === category ? null : state.globalError,
        }));
      },

      // Global error handling
      setGlobalError: (error) => {
        set({ globalError: error });
      },

      clearGlobalError: () => {
        set({ globalError: null });
      },

      // Error boundary management
      setErrorBoundaryActive: (active) => {
        set({ isErrorBoundaryActive: active });
      },

      // Utility methods
      getErrorsByCategory: (category) => {
        return get().errors.filter(error => error.category === category);
      },

      getErrorsBySeverity: (severity) => {
        return get().errors.filter(error => error.severity === severity);
      },

      hasErrors: () => {
        return get().errors.length > 0;
      },

      hasCriticalErrors: () => {
        return get().errors.some(error => error.severity === 'critical');
      },
    }),
    {
      name: 'error-store',
      serialize: true,
    }
  )
);

// Error handling hooks and utilities
export const useErrorHandler = () => {
  const { addError, removeError, clearErrors } = useErrorStore();

  const handleError = (
    error: unknown,
    context?: string,
    severity: ErrorSeverity = 'medium',
    category: ErrorCategory = 'unknown'
  ): string => {
    let message: string;
    let stack: string | undefined;

    if (error instanceof Error) {
      message = error.message;
      stack = error.stack;
    } else if (typeof error === 'string') {
      message = error;
    } else {
      message = 'An unknown error occurred';
    }

    return addError({
      message,
      severity,
      category,
      context,
      stack,
    });
  };

  const handleAsyncError = async <T>(
    operation: () => Promise<T>,
    context?: string,
    severity: ErrorSeverity = 'medium',
    category: ErrorCategory = 'unknown'
  ): Promise<T | null> => {
    try {
      return await operation();
    } catch (error) {
      handleError(error, context, severity, category);
      return null;
    }
  };

  return {
    handleError,
    handleAsyncError,
    removeError,
    clearErrors,
  };
};

// Network error helpers
export const createNetworkError = (
  message: string,
  context?: string,
  metadata?: Record<string, any>
) => createError(message, 'high', 'network', context, metadata);

// Validation error helpers
export const createValidationError = (
  message: string,
  context?: string,
  metadata?: Record<string, any>
) => createError(message, 'medium', 'validation', context, metadata);

// Authentication error helpers
export const createAuthError = (
  message: string,
  context?: string,
  metadata?: Record<string, any>
) => createError(message, 'high', 'authentication', context, metadata);

// System error helpers
export const createSystemError = (
  message: string,
  context?: string,
  metadata?: Record<string, any>
) => createError(message, 'critical', 'system', context, metadata);

// Error boundary integration
export const useErrorBoundary = () => {
  const { setErrorBoundaryActive, setGlobalError, clearGlobalError } = useErrorStore();

  const captureError = (error: Error, errorInfo?: any) => {
    const appError: AppError = {
      id: generateErrorId(),
      message: error.message,
      severity: 'critical',
      category: 'system',
      context: 'Error Boundary',
      timestamp: Date.now(),
      stack: error.stack,
      metadata: errorInfo,
    };

    setGlobalError(appError);
    setErrorBoundaryActive(true);
  };

  const resetErrorBoundary = () => {
    clearGlobalError();
    setErrorBoundaryActive(false);
  };

  return {
    captureError,
    resetErrorBoundary,
  };
};
