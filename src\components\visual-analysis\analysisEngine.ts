import { NodeData, ConnectionData } from './types';

interface ClusterSummary {
  clusterId: string;
  nodeCount: number;
  internalConnections: number;
  externalConnections: number;
  // Add other relevant summary metrics, e.g., aggregated values from nodes
}

interface ConnectionAnalysis {
  labelCounts: Record<string, number>;
  tagCounts: Record<string, number>;
}

export const analyzeConnections = (connections: ConnectionData[]): ConnectionAnalysis => {
  const labelCounts: Record<string, number> = {};
  const tagCounts: Record<string, number> = {};

  connections.forEach(connection => {
    if (connection.label) {
      labelCounts[connection.label] = (labelCounts[connection.label] || 0) + 1;
    }
    connection.tags?.forEach(tag => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1;
    });
  });

  return { labelCounts, tagCounts };
};

export const summarizeCluster = (nodes: NodeData[], connections: ConnectionData[], clusterId: string): ClusterSummary | null => {
  const clusterNodes = nodes.filter(node => node.clusterId === clusterId);
  if (clusterNodes.length === 0) {
    return null;
  }

  let internalConnections = 0;
  let externalConnections = 0;

  connections.forEach(connection => {
    const sourceInCluster = clusterNodes.some(node => node.id === connection.source);
    const targetInCluster = clusterNodes.some(node => node.id === connection.target);

    if (sourceInCluster && targetInCluster) {
      internalConnections++;
    } else if (sourceInCluster || targetInCluster) {
      externalConnections++;
    }
  });

  return {
    clusterId,
    nodeCount: clusterNodes.length,
    internalConnections,
    externalConnections,
  };
};

export const summarizeAllClusters = (nodes: NodeData[], connections: ConnectionData[]): ClusterSummary[] => {
  const clusterIds = [...new Set(nodes.map(node => node.clusterId).filter(id => id !== undefined))] as string[];
  return clusterIds.map(id => summarizeCluster(nodes, connections, id)).filter(summary => summary !== null) as ClusterSummary[];
};

export const findNodesByTag = (nodes: NodeData[], tag: string): NodeData[] => {
  return nodes.filter(node => node.tags?.includes(tag));
};

export const findConnectionsByTag = (connections: ConnectionData[], tag: string): ConnectionData[] => {
  return connections.filter(connection => connection.tags?.includes(tag));
};

// Future enhancements:
// - More sophisticated graph analysis algorithms (centrality, pathfinding, etc.)
// - Weighted connections and nodes
// - Time-series analysis if data has temporal aspects
