import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { FeedbackMode } from './types';

const feedbackOptions: { value: FeedbackMode; label: string }[] = [
  { value: "general", label: "General Communication" },
  { value: "style-specific", label: "Style-Specific Coaching" },
  { value: "cultural", label: "Cultural Sensitivity Focus" },
  { value: "psychological", label: "Psychological Intelligence" },
  { value: "social", label: "Social Awareness" },
  { value: "bias", label: "Bias Detection Focus" },
];

interface FeedbackModeSelectorProps {
  value: FeedbackMode;
  onValueChange: (value: FeedbackMode) => void;
}

export const FeedbackModeSelector: React.FC<FeedbackModeSelectorProps> = ({ value, onValueChange }) => {
  return (
    <div className="flex items-center">
      <Label htmlFor="feedback-mode" className="text-sm font-semibold text-slate-700 mr-3">
        Feedback Focus:
      </Label>
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger id="feedback-mode" className="w-[240px] bg-white border-slate-300 font-medium">
          <SelectValue placeholder="Select focus..." />
        </SelectTrigger>
        <SelectContent>
          {feedbackOptions.map(option => (
            <SelectItem key={option.value} value={option.value} className="font-medium">
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
