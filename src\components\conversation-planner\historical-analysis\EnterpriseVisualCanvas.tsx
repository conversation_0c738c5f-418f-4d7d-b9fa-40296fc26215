import React, { useState, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  generateDataPoints, 
  generateSampleMetrics, 
  generateSampleTimeline,
  DataPoint,
  MetricCard,
  TimelineItem
} from '@/utils/chartUtils';
import { useEnterpriseCanvas } from '@/hooks/useEnterpriseCanvas';
import '../../styles/enterprise-historical-analysis.css';

interface EnterpriseVisualCanvasProps {
  data?: any;
  type?: 'dashboard' | 'chart' | 'timeline' | 'metrics';
  title?: string;
  subtitle?: string;
  loading?: boolean;
}

export const EnterpriseVisualCanvas: React.FC<EnterpriseVisualCanvasProps> = ({ 
  data, 
  type = 'dashboard',
  title = 'Historical Analysis',
  subtitle = 'Performance metrics and trends',
  loading = false 
}) => {
  const canvas = useEnterpriseCanvas();
  const canvasRef = useRef<HTMLDivElement>(null);

  // Sample enterprise data
  const sampleData = useMemo(() => ({
    metrics: generateSampleMetrics(),
    chartData: generateDataPoints(12, 'sine'),
    timeline: generateSampleTimeline()
  }), []);
  const handleElementHover = (element: any, event: React.MouseEvent) => {
    canvas.handleElementHover(element, event);
  };

  const handleElementLeave = () => {
    canvas.handleElementLeave();
  };

  if (loading) {
    return (
      <div className="enterprise-historical-analysis">
        <div className="enterprise-loading-container">
          <div className="enterprise-loading-spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      className="enterprise-historical-analysis"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Professional Header */}
      <div className="enterprise-analysis-header">
        <div>
          <h1 className="enterprise-analysis-title">{title}</h1>
          <p className="enterprise-analysis-subtitle">{subtitle}</p>
        </div>        <div className="enterprise-analysis-actions">
          <button 
            className="enterprise-action-button"
            onClick={() => canvas.exportData('pdf')}
            disabled={canvas.loading}
          >
            📊 Export
          </button>
          <button 
            className="enterprise-action-button"
            onClick={() => canvas.refreshData()}
            disabled={canvas.loading}
          >
            🔄 Refresh
          </button>
          <button 
            className="enterprise-action-button primary"
            onClick={() => canvas.configureSettings()}
          >
            ⚙️ Configure
          </button>
        </div>
      </div>

      {/* Enterprise Metrics Grid */}
      <motion.div 
        className="enterprise-data-cards-grid"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        {sampleData.metrics.map((metric: MetricCard, index: number) => (
          <motion.div
            key={metric.id}
            className="enterprise-data-card"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * index }}
            onMouseEnter={(e) => handleElementHover(metric, e)}
            onMouseLeave={handleElementLeave}
          >
            <div className="enterprise-card-header">
              <div className="enterprise-card-icon">{metric.icon}</div>
              <div className="enterprise-card-menu">⋯</div>
            </div>
            <div className="enterprise-card-value">{metric.value}</div>
            <div className="enterprise-card-label">{metric.label}</div>
            <div className={`enterprise-card-change ${metric.type}`}>
              {metric.type === 'positive' ? '↗' : '↘'} {metric.change}
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Professional Visual Canvas */}
      <div className="enterprise-visual-canvas" ref={canvasRef}>
        <div className="enterprise-canvas-grid">
          {/* Advanced Chart */}
          <motion.div 
            className="enterprise-chart-container"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="enterprise-chart-header">
              <h3 className="enterprise-chart-title">Performance Trends</h3>
              <div className="enterprise-chart-controls">                {['7d', '30d', '90d', '1y'].map(range => (
                  <button
                    key={range}
                    className={`enterprise-chart-control ${canvas.selectedTimeRange === range ? 'active' : ''}`}
                    onClick={() => canvas.setSelectedTimeRange(range)}
                  >
                    {range}
                  </button>
                ))}
              </div>
            </div>
            
            <svg className="enterprise-chart-svg" viewBox="0 0 800 350">
              {/* Gradient Definitions */}
              <defs>
                <linearGradient id="enterpriseAreaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" stopColor="var(--enterprise-primary)" stopOpacity="0.3"/>
                  <stop offset="100%" stopColor="var(--enterprise-primary)" stopOpacity="0.05"/>
                </linearGradient>
                <filter id="enterpriseGlow">
                  <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                  <feMerge>
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
              </defs>

              {/* Grid Lines */}
              {[...Array(6)].map((_, i) => (
                <line
                  key={i}
                  className="enterprise-chart-grid-line"
                  x1="60"
                  y1={50 + i * 50}
                  x2="740"
                  y2={50 + i * 50}
                />
              ))}

              {/* Y-Axis Labels */}
              {[...Array(6)].map((_, i) => (
                <text
                  key={i}
                  className="enterprise-chart-axis-label"
                  x="45"
                  y={55 + i * 50}
                  textAnchor="end"
                >
                  {1000 - i * 200}
                </text>
              ))}

              {/* Chart Area */}
              <path
                className="enterprise-chart-area"
                d={`M 60 ${sampleData.chartData[0]?.y || 200} ${sampleData.chartData.map(point => `L ${point.x} ${point.y}`).join(' ')} L 740 300 L 60 300 Z`}
              />

              {/* Chart Line */}
              <path
                className="enterprise-chart-line"
                d={`M 60 ${sampleData.chartData[0]?.y || 200} ${sampleData.chartData.map(point => `L ${point.x} ${point.y}`).join(' ')}`}
                filter="url(#enterpriseGlow)"
              />

              {/* Data Points */}
              {sampleData.chartData.map((point: DataPoint, index: number) => (
                <g key={index}>                  <circle
                    className={`enterprise-chart-point ${canvas.hoveredElement?.x === point.x ? 'active' : ''}`}
                    cx={point.x}
                    cy={point.y}
                    onMouseEnter={(e) => handleElementHover(point, e)}
                    onMouseLeave={handleElementLeave}
                  />
                  <text
                    className="enterprise-chart-axis-label"
                    x={point.x}
                    y="330"
                    textAnchor="middle"
                  >
                    {point.date}
                  </text>
                </g>
              ))}
            </svg>

            {/* Professional Legend */}
            <div className="enterprise-legend">
              <div className="enterprise-legend-title">Legend</div>
              <div className="enterprise-legend-items">
                <div className="enterprise-legend-item">
                  <div className="enterprise-legend-color" style={{ backgroundColor: 'var(--enterprise-primary)' }}></div>
                  <span>User Growth</span>
                </div>
                <div className="enterprise-legend-item">
                  <div className="enterprise-legend-color" style={{ backgroundColor: 'var(--enterprise-success)' }}></div>
                  <span>Revenue</span>
                </div>
                <div className="enterprise-legend-item">
                  <div className="enterprise-legend-color" style={{ backgroundColor: 'var(--enterprise-warning)' }}></div>
                  <span>Performance</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Enterprise Timeline */}
          <motion.div 
            className="enterprise-timeline-container"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
          >
            <div className="enterprise-timeline-header">
              <h3 className="enterprise-timeline-title">Recent Activity</h3>
              <p className="enterprise-timeline-subtitle">Key events and milestones</p>
            </div>
            
            <div className="enterprise-timeline-items">
              {sampleData.timeline.map((item: TimelineItem, index: number) => (
                <motion.div
                  key={index}
                  className={`enterprise-timeline-item ${item.important ? 'important' : ''}`}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  onMouseEnter={(e) => handleElementHover(item, e)}
                  onMouseLeave={handleElementLeave}
                >
                  <div className="enterprise-timeline-date">{item.date}</div>
                  <div className="enterprise-timeline-title-item">{item.title}</div>
                  <div className="enterprise-timeline-content">{item.content}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>      {/* Enhanced Tooltip */}
      <AnimatePresence>
        {canvas.tooltip.show && (
          <motion.div
            className="enterprise-tooltip"
            style={{
              left: canvas.tooltip.x,
              top: canvas.tooltip.y,
            }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            {canvas.tooltip.content}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};
