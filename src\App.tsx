
import { Suspense, lazy, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { SavedAnalysisDialog } from "@/components/library/SavedAnalysisDialog";
import { PageLoader } from "@/components/ui/loading-fallback";
import { ThemeProvider as DesignSystemThemeProvider, injectGlobalStyles, debugDesignSystem } from "@/design-system";

// Lazy load heavy components
const Index = lazy(() => import("./pages/Index"));
const NotFound = lazy(() => import("./pages/NotFound"));
const LivingDataCanvasContainer = lazy(() => import("./components/visual-analysis/LivingDataCanvasWithFallback"));
const LivingDataCanvasSafeContainer = lazy(() => import("./components/visual-analysis/LivingDataCanvasSafe"));

const queryClient = new QueryClient();

const App = () => {
  // Initialize design system
  useEffect(() => {
    injectGlobalStyles();
    if (process.env.NODE_ENV === 'development') {
      debugDesignSystem();
    }
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
        <DesignSystemThemeProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <SavedAnalysisDialog />
            <BrowserRouter>
              <Suspense fallback={<PageLoader />}>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/canvas" element={<LivingDataCanvasContainer />} />
                  <Route path="/canvas-safe" element={<LivingDataCanvasSafeContainer />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
            </BrowserRouter>
          </TooltipProvider>
        </DesignSystemThemeProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
