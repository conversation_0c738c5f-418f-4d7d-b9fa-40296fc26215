/**
 * Bundle Optimization Utilities
 * 
 * Utilities for optimizing bundle size, code splitting, and improving
 * application loading performance.
 */

import { lazy, ComponentType } from 'react';

// Bundle analysis types
export interface BundleAnalysis {
  totalSize: number;
  gzippedSize: number;
  chunks: ChunkInfo[];
  dependencies: DependencyInfo[];
  recommendations: OptimizationRecommendation[];
}

export interface ChunkInfo {
  name: string;
  size: number;
  gzippedSize: number;
  modules: string[];
  isAsync: boolean;
  isEntry: boolean;
}

export interface DependencyInfo {
  name: string;
  version: string;
  size: number;
  gzippedSize: number;
  treeshakeable: boolean;
  sideEffects: boolean;
  usage: 'critical' | 'important' | 'optional' | 'unused';
}

export interface OptimizationRecommendation {
  type: 'code-split' | 'tree-shake' | 'lazy-load' | 'bundle-split' | 'dependency-replace';
  priority: 'high' | 'medium' | 'low';
  description: string;
  estimatedSavings: number;
  implementation: string;
}

// Lazy loading utilities
export class LazyLoadingManager {
  private static loadedModules = new Set<string>();
  private static loadingPromises = new Map<string, Promise<any>>();

  // Create a lazy component with error boundary
  static createLazyComponent<T extends ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    fallback?: ComponentType,
    errorFallback?: ComponentType<{ error: Error; retry: () => void }>
  ) {
    const LazyComponent = lazy(importFn);
    
    return (props: React.ComponentProps<T>) => (
      <React.Suspense 
        fallback={fallback ? React.createElement(fallback) : <div>Loading...</div>}
      >
        <ErrorBoundary fallback={errorFallback}>
          <LazyComponent {...props} />
        </ErrorBoundary>
      </React.Suspense>
    );
  }

  // Preload a module
  static preloadModule(importFn: () => Promise<any>): Promise<any> {
    const moduleKey = importFn.toString();
    
    if (this.loadedModules.has(moduleKey)) {
      return Promise.resolve();
    }

    if (this.loadingPromises.has(moduleKey)) {
      return this.loadingPromises.get(moduleKey)!;
    }

    const promise = importFn().then(module => {
      this.loadedModules.add(moduleKey);
      this.loadingPromises.delete(moduleKey);
      return module;
    });

    this.loadingPromises.set(moduleKey, promise);
    return promise;
  }

  // Preload multiple modules
  static preloadModules(importFns: (() => Promise<any>)[]): Promise<any[]> {
    return Promise.all(importFns.map(fn => this.preloadModule(fn)));
  }

  // Load module on interaction
  static loadOnInteraction(
    importFn: () => Promise<any>,
    trigger: 'hover' | 'click' | 'focus' | 'visible'
  ) {
    return (element: HTMLElement) => {
      const load = () => this.preloadModule(importFn);

      switch (trigger) {
        case 'hover':
          element.addEventListener('mouseenter', load, { once: true });
          break;
        case 'click':
          element.addEventListener('click', load, { once: true });
          break;
        case 'focus':
          element.addEventListener('focus', load, { once: true });
          break;
        case 'visible':
          const observer = new IntersectionObserver(
            (entries) => {
              if (entries[0].isIntersecting) {
                load();
                observer.disconnect();
              }
            },
            { threshold: 0.1 }
          );
          observer.observe(element);
          break;
      }
    };
  }
}

// Code splitting utilities
export class CodeSplittingManager {
  // Split by route
  static createRouteSplit(routes: Record<string, () => Promise<any>>) {
    const splitRoutes: Record<string, ComponentType> = {};

    Object.entries(routes).forEach(([path, importFn]) => {
      splitRoutes[path] = LazyLoadingManager.createLazyComponent(importFn);
    });

    return splitRoutes;
  }

  // Split by feature
  static createFeatureSplit(features: Record<string, () => Promise<any>>) {
    const splitFeatures: Record<string, ComponentType> = {};

    Object.entries(features).forEach(([feature, importFn]) => {
      splitFeatures[feature] = LazyLoadingManager.createLazyComponent(importFn);
    });

    return splitFeatures;
  }

  // Split by vendor
  static createVendorSplit(vendors: string[]) {
    return {
      cacheGroups: {
        vendor: {
          test: new RegExp(`[\\/]node_modules[\\/](${vendors.join('|')})[\\/]`),
          name: 'vendor',
          chunks: 'all',
        },
      },
    };
  }
}

// Tree shaking utilities
export class TreeShakingOptimizer {
  // Analyze unused exports
  static analyzeUnusedExports(moduleExports: string[], usedImports: string[]): string[] {
    return moduleExports.filter(exp => !usedImports.includes(exp));
  }

  // Create tree-shakeable imports
  static createTreeShakeableImport(moduleName: string, namedImports: string[]) {
    return `import { ${namedImports.join(', ')} } from '${moduleName}';`;
  }

  // Optimize lodash imports
  static optimizeLodashImports(code: string): string {
    return code.replace(
      /import\s+_\s+from\s+['"]lodash['"];/g,
      ''
    ).replace(
      /_.(\w+)/g,
      (match, method) => {
        return `import ${method} from 'lodash/${method}'; ${method}`;
      }
    );
  }

  // Optimize date-fns imports
  static optimizeDateFnsImports(code: string): string {
    return code.replace(
      /import\s+\*\s+as\s+dateFns\s+from\s+['"]date-fns['"];/g,
      ''
    ).replace(
      /dateFns\.(\w+)/g,
      (match, method) => {
        return `import { ${method} } from 'date-fns'; ${method}`;
      }
    );
  }
}

// Bundle analyzer
export class BundleAnalyzer {
  // Analyze bundle composition
  static analyzeBundleComposition(stats: any): BundleAnalysis {
    const chunks: ChunkInfo[] = stats.chunks.map((chunk: any) => ({
      name: chunk.name,
      size: chunk.size,
      gzippedSize: chunk.size * 0.3, // Estimated gzip ratio
      modules: chunk.modules.map((m: any) => m.name),
      isAsync: !chunk.initial,
      isEntry: chunk.entry,
    }));

    const dependencies: DependencyInfo[] = this.extractDependencies(stats);
    const recommendations = this.generateRecommendations(chunks, dependencies);

    return {
      totalSize: chunks.reduce((sum, chunk) => sum + chunk.size, 0),
      gzippedSize: chunks.reduce((sum, chunk) => sum + chunk.gzippedSize, 0),
      chunks,
      dependencies,
      recommendations,
    };
  }

  private static extractDependencies(stats: any): DependencyInfo[] {
    const dependencies: DependencyInfo[] = [];
    
    // Extract from package.json or webpack stats
    // This is a simplified implementation
    const commonDeps = [
      { name: 'react', size: 42000, treeshakeable: false, sideEffects: false },
      { name: 'react-dom', size: 130000, treeshakeable: false, sideEffects: false },
      { name: 'lodash', size: 70000, treeshakeable: true, sideEffects: false },
      { name: 'moment', size: 67000, treeshakeable: false, sideEffects: true },
      { name: 'date-fns', size: 78000, treeshakeable: true, sideEffects: false },
    ];

    return commonDeps.map(dep => ({
      ...dep,
      version: '1.0.0',
      gzippedSize: dep.size * 0.3,
      usage: 'important' as const,
    }));
  }

  private static generateRecommendations(
    chunks: ChunkInfo[],
    dependencies: DependencyInfo[]
  ): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];

    // Large chunks recommendation
    const largeChunks = chunks.filter(chunk => chunk.size > 250000);
    if (largeChunks.length > 0) {
      recommendations.push({
        type: 'code-split',
        priority: 'high',
        description: 'Large chunks detected. Consider code splitting.',
        estimatedSavings: largeChunks.reduce((sum, chunk) => sum + chunk.size * 0.3, 0),
        implementation: 'Use React.lazy() and dynamic imports',
      });
    }

    // Non-tree-shakeable dependencies
    const nonTreeShakeable = dependencies.filter(dep => !dep.treeshakeable && dep.size > 50000);
    if (nonTreeShakeable.length > 0) {
      recommendations.push({
        type: 'dependency-replace',
        priority: 'medium',
        description: 'Large non-tree-shakeable dependencies found.',
        estimatedSavings: nonTreeShakeable.reduce((sum, dep) => sum + dep.size * 0.5, 0),
        implementation: 'Replace with tree-shakeable alternatives',
      });
    }

    // Unused dependencies
    const unusedDeps = dependencies.filter(dep => dep.usage === 'unused');
    if (unusedDeps.length > 0) {
      recommendations.push({
        type: 'tree-shake',
        priority: 'high',
        description: 'Unused dependencies detected.',
        estimatedSavings: unusedDeps.reduce((sum, dep) => sum + dep.size, 0),
        implementation: 'Remove unused imports and dependencies',
      });
    }

    return recommendations;
  }
}

// Performance budgets
export class PerformanceBudget {
  private static budgets = {
    totalSize: 500000, // 500KB
    gzippedSize: 150000, // 150KB
    chunkSize: 250000, // 250KB per chunk
    dependencySize: 100000, // 100KB per dependency
  };

  static checkBudgets(analysis: BundleAnalysis): {
    passed: boolean;
    violations: string[];
  } {
    const violations: string[] = [];

    if (analysis.totalSize > this.budgets.totalSize) {
      violations.push(`Total bundle size (${analysis.totalSize}) exceeds budget (${this.budgets.totalSize})`);
    }

    if (analysis.gzippedSize > this.budgets.gzippedSize) {
      violations.push(`Gzipped size (${analysis.gzippedSize}) exceeds budget (${this.budgets.gzippedSize})`);
    }

    analysis.chunks.forEach(chunk => {
      if (chunk.size > this.budgets.chunkSize) {
        violations.push(`Chunk ${chunk.name} (${chunk.size}) exceeds budget (${this.budgets.chunkSize})`);
      }
    });

    analysis.dependencies.forEach(dep => {
      if (dep.size > this.budgets.dependencySize) {
        violations.push(`Dependency ${dep.name} (${dep.size}) exceeds budget (${this.budgets.dependencySize})`);
      }
    });

    return {
      passed: violations.length === 0,
      violations,
    };
  }

  static setBudgets(newBudgets: Partial<typeof PerformanceBudget.budgets>) {
    Object.assign(this.budgets, newBudgets);
  }
}

// Error boundary for lazy components
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: ComponentType<{ error: Error; retry: () => void }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      if (FallbackComponent) {
        return (
          <FallbackComponent
            error={this.state.error!}
            retry={() => this.setState({ hasError: false, error: null })}
          />
        );
      }
      return <div>Something went wrong loading this component.</div>;
    }

    return this.props.children;
  }
}

// Webpack optimization helpers
export const webpackOptimizations = {
  // Split chunks configuration
  splitChunks: {
    chunks: 'all',
    cacheGroups: {
      vendor: {
        test: /[\\/]node_modules[\\/]/,
        name: 'vendors',
        chunks: 'all',
      },
      common: {
        name: 'common',
        minChunks: 2,
        chunks: 'all',
        enforce: true,
      },
    },
  },

  // Minimize configuration
  minimize: true,
  minimizer: [
    // TerserPlugin configuration would go here
  ],

  // Module concatenation
  concatenateModules: true,

  // Side effects configuration
  sideEffects: false,

  // Tree shaking configuration
  usedExports: true,
  providedExports: true,
  optimization: {
    treeshake: true,
  },
};

// Export utilities
export {
  LazyLoadingManager,
  CodeSplittingManager,
  TreeShakingOptimizer,
  BundleAnalyzer,
  PerformanceBudget,
};
