import React from "react";

export const formatContent = (content: string): string => {
  // Convert markdown-style formatting to HTML
  let formatted = content
    // Convert **text** to <strong>text</strong>
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert *text* to <em>text</em>
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Convert numbered lists
    .replace(/^(\d+)\.\s+(.+)$/gm, '<div class="numbered-item"><span class="number">$1.</span> $2</div>')
    // Convert line breaks to proper spacing
    .replace(/\n\n/g, '</p><p>')
    // Wrap in paragraphs
    .replace(/^(.+)$/gm, (match, line) => {
      // Don't wrap if it's already wrapped or is a numbered item
      if (line.includes('<div class="numbered-item">') || line.includes('<strong>') || line.includes('</p>')) {
        return line;
      }
      return `<p>${line}</p>`;
    });

  // Clean up any double paragraph tags
  formatted = formatted.replace(/<p><\/p>/g, '');
  formatted = formatted.replace(/<p><p>/g, '<p>');
  formatted = formatted.replace(/<\/p><\/p>/g, '</p>');

  return formatted;
};

export const formatInlineContent = (content: string): string => {
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>');
};

export const parseConversationalAnswers = (content: string) => {
  const answers: { number: number; text: string }[] = [];
  const lines = content.split('\n');
  
  let currentAnswer: { number: number; text: string } | null = null;
  
  for (const line of lines) {
    const answerMatch = line.match(/^\*\*Answer\s+(\d+):\*\*\s*(.+)$/i);
    if (answerMatch) {
      if (currentAnswer) {
        answers.push(currentAnswer);
      }
      currentAnswer = {
        number: parseInt(answerMatch[1]),
        text: answerMatch[2]
      };
    } else if (currentAnswer && line.trim() && !line.includes('**')) {
      currentAnswer.text += ' ' + line.trim();
    }
  }
  
  if (currentAnswer) {
    answers.push(currentAnswer);
  }
  
  return answers;
};

interface FormattedContentDisplayProps {
  content: string;
}

export const FormattedContentDisplay: React.FC<FormattedContentDisplayProps> = ({ content }) => {
  const formattedContent = formatContent(content);

  return (
    <div className="bg-gradient-to-br from-slate-50 to-white border border-slate-200 rounded-xl p-8 shadow-sm">
      <div
        className="prose prose-slate max-w-none leading-relaxed"
        dangerouslySetInnerHTML={{ __html: formattedContent }}
      />
      <style>{`
        .numbered-item {
          display: flex;
          margin: 16px 0;
          align-items: flex-start;
          color: #334155;
          background: #f8fafc;
          padding: 12px 16px;
          border-radius: 8px;
          border-left: 3px solid #3b82f6;
        }
        .number {
          font-weight: 700;
          color: #3b82f6;
          margin-right: 12px;
          min-width: 28px;
          font-size: 0.95rem;
        }
        .prose p {
          margin: 16px 0;
          line-height: 1.7;
          color: #475569;
          font-size: 1rem;
        }
        .prose strong {
          color: #1e293b;
          font-weight: 600;
        }
        .prose em {
          color: #64748b;
          font-style: italic;
        }
        .prose h1, .prose h2, .prose h3, .prose h4 {
          color: #1e293b;
          font-weight: 600;
          margin-top: 24px;
          margin-bottom: 12px;
        }
        .prose ul, .prose ol {
          margin: 16px 0;
          padding-left: 24px;
        }
        .prose li {
          margin: 8px 0;
          color: #475569;
          line-height: 1.6;
        }
        .prose blockquote {
          border-left: 4px solid #e2e8f0;
          padding-left: 16px;
          margin: 20px 0;
          font-style: italic;
          color: #64748b;
        }
      `}</style>
    </div>
  );
};
