/**
 * Utility Classes
 * 
 * Comprehensive utility classes for common styling patterns.
 * These utilities complement Tailwind CSS with application-specific patterns.
 */

@layer utilities {
  /* Glass Morphism Effects */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }
  
  .glass-dark {
    @apply backdrop-blur-md bg-black/10 border border-white/10;
  }
  
  .glass-card {
    @apply glass rounded-xl shadow-lg;
  }
  
  .glass-card-dark {
    @apply glass-dark rounded-xl shadow-lg;
  }

  /* Gradient Backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary-hover;
  }
  
  .gradient-secondary {
    @apply bg-gradient-to-r from-secondary to-accent;
  }
  
  .gradient-success {
    @apply bg-gradient-to-r from-success to-success/80;
  }
  
  .gradient-warning {
    @apply bg-gradient-to-r from-warning to-warning/80;
  }
  
  .gradient-error {
    @apply bg-gradient-to-r from-destructive to-destructive/80;
  }
  
  .gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }
  
  .gradient-conic {
    background: conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops));
  }

  /* Text Gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary-hover bg-clip-text text-transparent;
  }
  
  .text-gradient-rainbow {
    @apply bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 bg-clip-text text-transparent;
  }

  /* Shadows and Elevation */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(var(--primary), 0.3);
  }
  
  .shadow-glow-success {
    box-shadow: 0 0 20px rgba(var(--success), 0.3);
  }
  
  .shadow-glow-warning {
    box-shadow: 0 0 20px rgba(var(--warning), 0.3);
  }
  
  .shadow-glow-error {
    box-shadow: 0 0 20px rgba(var(--destructive), 0.3);
  }
  
  .shadow-inner-soft {
    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  }

  /* Interactive States */
  .interactive {
    @apply transition-all duration-normal cursor-pointer;
    @apply hover:shadow-md hover:-translate-y-0.5;
    @apply active:translate-y-0 active:shadow-sm;
  }
  
  .interactive-scale {
    @apply transition-transform duration-normal cursor-pointer;
    @apply hover:scale-105 active:scale-95;
  }
  
  .interactive-glow {
    @apply interactive;
    @apply hover:shadow-glow;
  }

  /* Focus States */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }
  
  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-inset;
  }
  
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
  }

  /* Layout Utilities */
  .center {
    @apply flex items-center justify-center;
  }
  
  .center-x {
    @apply flex justify-center;
  }
  
  .center-y {
    @apply flex items-center;
  }
  
  .full-center {
    @apply fixed inset-0 center;
  }
  
  .container-padding {
    @apply px-4 md:px-6 lg:px-8;
  }
  
  .section-spacing {
    @apply py-8 md:py-12 lg:py-16;
  }

  /* Grid Utilities */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  /* Scrollbar Styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 20px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
  }
  
  .scrollbar-hidden {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }

  /* Animation Utilities */
  .animate-fade-in-up {
    animation: fadeInUp 0.5s ease-out forwards;
  }
  
  .animate-fade-in-down {
    animation: fadeInDown 0.5s ease-out forwards;
  }
  
  .animate-fade-in-left {
    animation: fadeInLeft 0.5s ease-out forwards;
  }
  
  .animate-fade-in-right {
    animation: fadeInRight 0.5s ease-out forwards;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.3s ease-out forwards;
  }
  
  .animate-slide-up {
    animation: slideUp 0.4s ease-out forwards;
  }
  
  .animate-slide-down {
    animation: slideDown 0.4s ease-out forwards;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out forwards;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  /* Loading States */
  .loading-skeleton {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted;
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  .loading-dots::after {
    content: '';
    animation: dots 1.5s infinite;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }

  /* Status Indicators */
  .status-online {
    @apply bg-green-500;
  }
  
  .status-offline {
    @apply bg-gray-400;
  }
  
  .status-busy {
    @apply bg-red-500;
  }
  
  .status-away {
    @apply bg-yellow-500;
  }
  
  .status-dot {
    @apply w-2 h-2 rounded-full;
  }
  
  .status-dot-lg {
    @apply w-3 h-3 rounded-full;
  }

  /* Truncation Utilities */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .truncate-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Aspect Ratio Utilities */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .aspect-photo {
    aspect-ratio: 4 / 3;
  }
  
  .aspect-golden {
    aspect-ratio: 1.618 / 1;
  }

  /* Border Utilities */
  .border-gradient {
    border: 1px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, var(--primary), var(--primary-hover)) border-box;
  }
  
  .border-dashed-animated {
    border: 2px dashed var(--border);
    animation: dash 20s linear infinite;
  }

  /* Print Utilities */
  @media print {
    .print-hidden {
      display: none !important;
    }
    
    .print-visible {
      display: block !important;
    }
    
    .print-break-before {
      page-break-before: always;
    }
    
    .print-break-after {
      page-break-after: always;
    }
    
    .print-break-inside-avoid {
      page-break-inside: avoid;
    }
  }

  /* Accessibility Utilities */
  .sr-only-focusable {
    @apply sr-only;
  }
  
  .sr-only-focusable:focus {
    @apply not-sr-only;
  }
  
  .reduce-motion {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  @media (prefers-reduced-motion: reduce) {
    .motion-safe {
      animation: none !important;
      transition: none !important;
    }
  }

  /* High Contrast Mode */
  @media (prefers-contrast: high) {
    .high-contrast {
      border: 2px solid;
      background: white;
      color: black;
    }
  }
}
