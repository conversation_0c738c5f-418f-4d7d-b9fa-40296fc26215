import * as THREE from 'three';
import { SimpleNode, VisualConnection } from './types';

export interface NeighborHighlightOptions {
  highlightDistance: number; // 1 = direct neighbors, 2 = neighbors of neighbors, etc.
  dimUnrelated: boolean;
  highlightEdges: boolean;
  highlightColor: THREE.ColorRepresentation;
  dimOpacity: number;
}

export function getConnectedNodes(
  selectedNodeId: string,
  nodes: Map<string, SimpleNode>,
  connections: Map<string, VisualConnection>,
  distance: number = 1
): Set<string> {
  const connected = new Set<string>();
  const queue: Array<{ id: string; dist: number }> = [{ id: selectedNodeId, dist: 0 }];
  const visited = new Set<string>();

  while (queue.length > 0) {
    const { id, dist } = queue.shift()!;
    
    if (visited.has(id) || dist > distance) continue;
    visited.add(id);
    connected.add(id);

    if (dist < distance) {
      // Find all nodes connected to this one
      connections.forEach((conn) => {
        if (conn.sourceNode.id === id && !visited.has(conn.targetNode.id)) {
          queue.push({ id: conn.targetNode.id, dist: dist + 1 });
        } else if (conn.targetNode.id === id && !visited.has(conn.sourceNode.id)) {
          queue.push({ id: conn.sourceNode.id, dist: dist + 1 });
        }
      });
    }
  }

  return connected;
}

export function getConnectedEdges(
  connectedNodeIds: Set<string>,
  connections: Map<string, VisualConnection>
): Set<string> {
  const connectedEdges = new Set<string>();

  connections.forEach((conn, id) => {
    if (connectedNodeIds.has(conn.sourceNode.id) && connectedNodeIds.has(conn.targetNode.id)) {
      connectedEdges.add(id);
    }
  });

  return connectedEdges;
}

export function highlightSubgraph(
  selectedNodeId: string,
  nodes: Map<string, SimpleNode>,
  connections: Map<string, VisualConnection>,
  options: NeighborHighlightOptions
): { connectedNodes: Set<string>; connectedEdges: Set<string> } {
  const connectedNodes = getConnectedNodes(selectedNodeId, nodes, connections, options.highlightDistance);
  const connectedEdges = getConnectedEdges(connectedNodes, connections);

  // Highlight connected nodes
  nodes.forEach((node, nodeId) => {
    const material = node.mesh.material as THREE.MeshBasicMaterial;
    
    if (connectedNodes.has(nodeId)) {
      // Highlight connected nodes
      material.opacity = 1.0;
      if (nodeId === selectedNodeId) {
        // Make the selected node extra prominent
        material.color.set(options.highlightColor);
        node.mesh.scale.setScalar(1.3);
      } else {
        // Highlight connected nodes with a different color
        const highlightColor = new THREE.Color(options.highlightColor);
        highlightColor.lerp(new THREE.Color(node.mesh.userData.originalColor), 0.5);
        material.color.set(highlightColor);
        node.mesh.scale.setScalar(1.1);
      }
    } else if (options.dimUnrelated) {
      // Dim unrelated nodes
      material.opacity = options.dimOpacity;
      material.color.set(node.mesh.userData.originalColor);
      node.mesh.scale.setScalar(0.8);
    }
  });
  // Highlight connected edges
  if (options.highlightEdges) {
    connections.forEach((conn, connId) => {
      const lineMaterial = (conn.lineMesh as THREE.Line).material as THREE.LineBasicMaterial;
      
      if (connectedEdges.has(connId)) {
        // Highlight connected edges
        lineMaterial.opacity = 1.0;
        lineMaterial.color.set(options.highlightColor);
        // Note: LineBasicMaterial doesn't support linewidth, it's always 1 pixel
      } else if (options.dimUnrelated) {
        // Dim unrelated edges
        lineMaterial.opacity = options.dimOpacity * 0.5;
      }
    });
  }

  return { connectedNodes, connectedEdges };
}

export function clearSubgraphHighlight(
  nodes: Map<string, SimpleNode>,
  connections: Map<string, VisualConnection>
): void {
  // Restore original node appearance
  nodes.forEach((node) => {
    const material = node.mesh.material as THREE.MeshBasicMaterial;
    material.color.set(node.mesh.userData.originalColor);
    material.opacity = node.mesh.userData.originalOpacity || 0.9;
    node.mesh.scale.setScalar(1.0);
  });
  // Restore original edge appearance
  connections.forEach((conn) => {
    const lineMaterial = (conn.lineMesh as THREE.Line).material as THREE.LineBasicMaterial;
    // Restore original edge styling
    lineMaterial.opacity = 0.8;
    // Reset color based on strength or use default
    lineMaterial.color.set(0xffffff);
  });
}
