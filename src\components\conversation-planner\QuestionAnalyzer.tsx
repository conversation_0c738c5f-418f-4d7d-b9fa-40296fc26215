import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Loader2, MessageSquare, Settings } from "lucide-react";
import { StyleSelector } from "./StyleSelector";
import { CharacterPersonaManager } from "./CharacterPersonaManager";
import { QuestionContextSelector } from "./QuestionContextSelector";
import { useSettingsStore } from "@/stores/useSettingsStore";
import { useAnalysisStore } from "@/stores/useAnalysisStore";
import { useUIStore } from "@/stores/useUIStore";
import { AnalysisTypeSelector } from "./AnalysisTypeSelector";
import { AnswerCountSelector } from "./AnswerCountSelector";
import { EmotionSelector } from "./EmotionSelector";
import { useChunkPreloader } from "@/hooks/useChunkPreloader";

interface QuestionAnalyzerProps {
  onAnalyze: () => void;
}

export const QuestionAnalyzer: React.FC<QuestionAnalyzerProps> = ({ onAnalyze }) => {
  const { settings, updateSettings } = useSettingsStore();
  const { question, setQuestion, characterPersona, setCharacterPersona, questionContext, setQuestionContext } = useAnalysisStore();
  const isAnalyzing = useUIStore((state) => state.loading);
  const [showSettings, setShowSettings] = useState(false);
  const { preloadCharacterPersonas, preloadChatbot } = useChunkPreloader();

  // Preload character personas when user selects character analysis
  useEffect(() => {
    if (settings.analysisType === 'character') {
      preloadCharacterPersonas();
    }
  }, [settings.analysisType, preloadCharacterPersonas]);

  // Preload chatbot when user has question and API key (likely to use chat mode)
  useEffect(() => {
    if (question.length > 10 && settings.openRouterApiKey) {
      preloadChatbot();
    }
  }, [question, settings.openRouterApiKey, preloadChatbot]);

  const getAnalyzeButtonText = () => {
    if (settings.analysisType === "character" && characterPersona) {
      return `Simulate with ${characterPersona.name}`;
    }
    if (settings.analysisType === "deep") {
      return "Start Strategic Deep Dive";
    }
    if (settings.analysisType === "multiple") {
      return `Generate ${settings.numberOfAnswers} Scenarios`;
    }
    return "Analyze Question (Basic)";
  };

  const getQuestionPlaceholder = () => {
    if (settings.analysisType === 'character') {
      return `What do you want to say to ${characterPersona?.name || 'the persona'}?`;
    }
    if (questionContext === "asking") {
      return "What question are you planning to ask?";
    }
    return "What question did someone ask you?";
  };
  return (
    <Card id="question-analyzer" className="shadow-xl border-0 bg-slate-900/95 backdrop-blur-sm overflow-hidden">      <CardHeader className="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 text-white border-b border-slate-600/50">
        <CardTitle className="flex items-center text-xl font-bold text-white drop-shadow-lg">
          <MessageSquare className="mr-3 h-6 w-6 drop-shadow-md" />
          Conversation Planning Center
        </CardTitle>
        <CardDescription className="text-white/95 text-base font-medium drop-shadow-md">
          Plan your conversations with AI-powered response suggestions and advanced analysis
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-8 p-8">
        {/* Question Context Selector */}
        {settings.openRouterApiKey && settings.analysisType === "multiple" && (
          <div className="p-4 bg-muted/30 rounded-xl border border-border/50">
            <QuestionContextSelector context={questionContext} onContextChange={setQuestionContext} />
          </div>
        )}

        {/* Question Input */}
        <div className="space-y-4">
          <Label htmlFor="question" className="text-base font-semibold text-foreground flex items-center gap-2">
            💬 Your Question/Statement
          </Label>
          <Input
            id="question"
            placeholder={getQuestionPlaceholder()}
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            className="h-14 text-base border-2 border-border/50 focus:border-primary transition-all duration-200 bg-background/50 backdrop-blur-sm shadow-sm"
            disabled={isAnalyzing}
          />
        </div>

        {/* Analysis Configuration */}
        {settings.openRouterApiKey && (
          <div className="space-y-8">
            <div id="analysis-type-selector" className="p-6 bg-muted/30 rounded-xl border border-border/50">
              <AnalysisTypeSelector
                analysisType={settings.analysisType}
                onAnalysisTypeChange={(type) => updateSettings({ analysisType: type })}
              />
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {settings.analysisType === "multiple" && (
                <div className="p-4 bg-background/50 rounded-xl border border-border/50">
                  <AnswerCountSelector
                    numberOfAnswers={settings.numberOfAnswers}
                    onNumberOfAnswersChange={(count) => updateSettings({ numberOfAnswers: count })}
                  />
                </div>
              )}

              {settings.analysisType === "character" && (
                <div className="p-4 bg-background/50 rounded-xl border border-border/50">
                  <CharacterPersonaManager selectedPersona={characterPersona} onPersonaChange={setCharacterPersona} />
                </div>
              )}
              
              {settings.analysisType === "emotional-angles" && (
                <div className="lg:col-span-2 p-4 bg-background/50 rounded-xl border border-border/50">
                  <EmotionSelector
                    selectedEmotions={settings.selectedEmotions || []}
                    onSelectedEmotionsChange={(emotions) => updateSettings({ selectedEmotions: emotions })}
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Settings and Status */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-muted/20 rounded-xl">
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowSettings(!showSettings)} 
              className="border-border/50 hover:border-border bg-background/50 backdrop-blur-sm"
            >
              <Settings className="mr-2 h-4 w-4" />
              Conversation Style Settings
            </Button>
            {settings.openRouterApiKey && (
              <Badge variant="secondary" className="bg-success/10 text-success border border-success/20">
                ✅ AI-Powered Analysis Ready
              </Badge>
            )}
          </div>
        </div>

        {/* Advanced Settings */}
        {showSettings && (
          <div className="p-6 bg-muted/30 rounded-xl border border-border/50 animate-in slide-in-from-top-2 duration-300">
            <StyleSelector style={settings.style} onStyleChange={(style) => updateSettings({ style })} />
          </div>
        )}        {/* Action Button */}
        <div className="pt-4">
          <Button
            id="analyze-button"
            onClick={onAnalyze}
            className="w-full h-16 text-lg font-semibold bg-gradient-to-r from-primary via-primary-hover to-purple-600 hover:from-primary/90 hover:via-primary-hover/90 hover:to-purple-600/90 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
            disabled={isAnalyzing || !question.trim() || (settings.analysisType === "character" && !characterPersona)}
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-3 h-5 w-5 animate-spin" />
                Analyzing Question...
              </>
            ) : (
              <>
                <MessageSquare className="mr-3 h-5 w-5" />
                {settings.openRouterApiKey ? getAnalyzeButtonText() : "Analyze Question (Basic)"}
              </>
            )}
          </Button>
        </div>

        {/* Status Messages */}
        {!settings.openRouterApiKey && (
          <div className="text-center p-4 bg-warning/10 border border-warning/20 rounded-xl">
            <p className="text-sm text-warning font-medium">
              🔑 Add your OpenRouter API key in API Management for advanced AI-powered analysis
            </p>
          </div>
        )}

        {settings.analysisType === "character" && !characterPersona && (
          <div className="text-center p-4 bg-purple-600/10 border border-purple-600/20 rounded-xl">
            <p className="text-sm text-purple-600 font-medium">
              👤 Please create or select a character persona to continue
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
