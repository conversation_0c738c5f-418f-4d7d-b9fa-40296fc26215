
import React from "react";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { MessageCircle, MessageSquareReply } from "lucide-react";

export type QuestionContext = 'asking' | 'receiving';

interface QuestionContextSelectorProps {
  context: QuestionContext;
  onContextChange: (context: QuestionContext) => void;
}

export const QuestionContextSelector: React.FC<QuestionContextSelectorProps> = ({
  context,
  onContextChange
}) => {
  return (
    <div className="space-y-3">      <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-slate-700">
            <MessageCircle className="h-5 w-5 text-slate-300" />
          </div>
          <Label className="text-lg font-semibold text-white">Question Context</Label>
        </div>
      <RadioGroup 
        value={context} 
        onValueChange={value => onContextChange(value as QuestionContext)} 
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
      >        <Label 
          htmlFor="asking" 
          className={`flex flex-col items-start text-left space-y-3 p-4 rounded-xl border-2 transition-all cursor-pointer hover:shadow-lg transform hover:-translate-y-1 animate-fade-in bg-slate-800/80 backdrop-blur-sm ${
            context === 'asking' 
              ? 'border-blue-400 bg-blue-900/40 shadow-md' 
              : 'border-slate-600/50 hover:border-blue-400/60 hover:bg-slate-700/60'
          }`}
          style={{ animationDelay: '100ms', animationFillMode: 'backwards' }}
        >
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${context === 'asking' ? 'bg-blue-800/60' : 'bg-slate-700'}`}>
              <MessageCircle className="h-6 w-6 text-blue-400" />
            </div>
            <p className="text-base font-semibold text-white">
              I want to ask this
            </p>
          </div>          <p className="text-sm text-white flex-grow">
            Get possible responses the other person might give.
          </p>
          <RadioGroupItem value="asking" id="asking" className="sr-only" />
        </Label>
          <Label 
          htmlFor="receiving" 
          className={`flex flex-col items-start text-left space-y-3 p-4 rounded-xl border-2 transition-all cursor-pointer hover:shadow-lg transform hover:-translate-y-1 animate-fade-in bg-slate-800/80 backdrop-blur-sm ${
            context === 'receiving' 
              ? 'border-green-400 bg-green-900/40 shadow-md' 
              : 'border-slate-600/50 hover:border-green-400/60 hover:bg-slate-700/60'
          }`}
          style={{ animationDelay: '200ms', animationFillMode: 'backwards' }}
        >
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${context === 'receiving' ? 'bg-green-800/60' : 'bg-slate-700'}`}>
              <MessageSquareReply className="h-6 w-6 text-green-400" />
            </div>
            <p className="text-base font-semibold text-white">
              Someone asked me this
            </p>
          </div>          <p className="text-sm text-white flex-grow">
            Get natural and effective ways you could respond.
          </p>
          <RadioGroupItem value="receiving" id="receiving" className="sr-only" />
        </Label>
      </RadioGroup>
    </div>
  );
};
