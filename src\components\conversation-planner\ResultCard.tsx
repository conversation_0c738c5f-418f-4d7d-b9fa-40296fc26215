import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { AnalysisResult } from "@/types/conversation";
import { useToast } from "@/hooks/use-toast";
import { ResultCardHeader } from "./result-card/ResultCardHeader";
import { ResultCardContent } from "./result-card/ResultCardContent";
import { ResultCardFooter } from "./result-card/ResultCardFooter";

interface ResultCardProps {
  result: AnalysisResult;
  onSave: (result: AnalysisResult) => void;
  onDelete: (id: string) => void;
  onRate: (id: string, rating: number) => void;
  onRefine: (id: string, prompt: string) => Promise<void>;
  onFollowUpQuestion: (question: string) => void;
  onAnswerSelect?: (answer: string, answerNumber: number) => void;
  onAddNote?: (questionId: string, answerId: string, answerContent: string) => void;
  onEnterChatMode?: (result: AnalysisResult, focusedAnswer?: string) => void;
  isRefining?: boolean;
}

export const ResultCard: React.FC<ResultCardProps> = ({
  result,
  onSave,
  onDelete,
  onRate,
  onRefine,
  onFollowUpQuestion,
  onAnswerSelect,
  onAddNote,
  onEnterChatMode,
  isRefining = false,
}) => {
  const [showRefinement, setShowRefinement] = useState(false);
  const [refinementPrompt, setRefinementPrompt] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const { toast } = useToast();

  const handleToggleItemSelect = (item: string) => {
    setSelectedItems((prev) =>
      prev.includes(item)
        ? prev.filter((i) => i !== item)
        : [...prev, item]
    );
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(result.analysis);
      toast({
        title: "Copied to clipboard",
        description: "The analysis has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Unable to copy to clipboard.",
        variant: "destructive",
      });
    }
  };

  const handleRefine = async () => {
    if (!refinementPrompt.trim()) {
      toast({
        title: "Refinement prompt required",
        description: "Please enter a refinement instruction.",
        variant: "destructive",
      });
      return;
    }

    await onRefine(result.id, refinementPrompt);
    setRefinementPrompt("");
    setShowRefinement(false);
  };

  const handleRate = (rating: number) => {
    onRate(result.id, rating);
  };

  const handleSaveSelectedItems = () => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select at least one item to save.",
        variant: "destructive",
      });
      return;
    }

    const newAnalysisContent = selectedItems.join("\n\n");

    const newResult: AnalysisResult = {
      ...result,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
      question: `Selection from: ${result.question}`,
      analysis: newAnalysisContent,
      timestamp: new Date(),
      parsedOutput: undefined,
      refinements: [],
      rating: undefined,
    };
    
    onSave(newResult);
    setSelectedItems([]); // Clear selection
  };

  const handleItemSelectForChat = (item: string) => {
    if (onEnterChatMode) {
      onEnterChatMode(result, item);
    } else if (onFollowUpQuestion) {
      onFollowUpQuestion(item);
    }
  };

  const handleAnswerSelectEvent = (answer: string, answerNumber: number) => {
    if (onEnterChatMode) {
      onEnterChatMode(result, answer);
    } else if (onAnswerSelect) {
      onAnswerSelect(answer, answerNumber);
    }
  };

  const handleEnterChatMode = () => {
    if (onEnterChatMode) {
      onEnterChatMode(result);
    }
  };

  return (
    <Card className="mb-8 shadow-xl border border-slate-200 rounded-xl overflow-hidden bg-white hover:shadow-2xl transition-shadow duration-300">
      <ResultCardHeader
        result={result}
        onRate={handleRate}
        onEnterChatMode={handleEnterChatMode}
      />

      <ResultCardContent
        result={result}
        showRefinement={showRefinement}
        onToggleRefinement={() => setShowRefinement(!showRefinement)}
        refinementPrompt={refinementPrompt}
        onRefinementPromptChange={setRefinementPrompt}
        onRefine={handleRefine}
        isRefining={isRefining}
        onFollowUpQuestion={onFollowUpQuestion}
        onAnswerSelect={handleAnswerSelectEvent}
        onItemSelectForChat={handleItemSelectForChat}
        selectedItems={selectedItems}
        onItemToggleSelect={handleToggleItemSelect}
      />

      <ResultCardFooter
        result={result}
        onSave={() => onSave(result)}
        onCopy={handleCopy}
        onToggleRefinement={() => setShowRefinement(!showRefinement)}
        onDelete={() => onDelete(result.id)}
        onAddNote={onAddNote}
        onSaveSelected={handleSaveSelectedItems}
        selectedItemsCount={selectedItems.length}
      />
    </Card>
  );
};
