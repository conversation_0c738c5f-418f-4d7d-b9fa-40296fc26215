
import { useState, useLayoutEffect } from 'react';
import { Canvas } from 'fabric';

export const useFabricSetup = (
  canvasEl: React.RefObject<HTMLCanvasElement>,
  canvasWrapperEl: React.RefObject<HTMLDivElement>
) => {
  const [canvas, setCanvas] = useState<Canvas | null>(null);

  useLayoutEffect(() => {
    const wrapper = canvasWrapperEl.current;
    if (!wrapper || !canvasEl.current) return;    const fabricCanvas = new Canvas(canvasEl.current, {
      selection: true,
      backgroundColor: 'hsl(var(--background))',
      stopContextMenu: true,
      viewportTransform: [1, 0, 0, 1, 0, 0], // Initialize with identity matrix
    });

    setCanvas(fabricCanvas);

    const resizeObserver = new ResizeObserver(entries => {
      const { width, height } = entries[0].contentRect;
      fabricCanvas.setWidth(width);
      fabricCanvas.setHeight(height);
      fabricCanvas.renderAll();
    });
    resizeObserver.observe(wrapper);

    return () => {
      resizeObserver.disconnect();
      fabricCanvas.dispose();
    };
  }, [canvasEl, canvasWrapperEl]);

  return canvas;
};
