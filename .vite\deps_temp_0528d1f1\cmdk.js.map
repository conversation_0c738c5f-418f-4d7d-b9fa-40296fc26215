{"version": 3, "sources": ["../../node_modules/cmdk/dist/chunk-NZJY6EH4.mjs", "../../node_modules/cmdk/node_modules/@radix-ui/primitive/dist/packages/core/primitive/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/primitive/dist/packages/core/primitive/src/primitive.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-compose-refs/dist/packages/react/compose-refs/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-compose-refs/dist/packages/react/compose-refs/src/composeRefs.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-context/dist/packages/react/context/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-context/dist/packages/react/context/src/createContext.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-use-layout-effect/dist/packages/react/use-layout-effect/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-use-layout-effect/dist/packages/react/use-layout-effect/src/useLayoutEffect.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-id/dist/packages/react/id/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-id/dist/packages/react/id/src/id.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-use-callback-ref/dist/packages/react/use-callback-ref/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-use-callback-ref/dist/packages/react/use-callback-ref/src/useCallbackRef.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-use-controllable-state/dist/packages/react/use-controllable-state/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-use-controllable-state/dist/packages/react/use-controllable-state/src/useControllableState.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-slot/dist/packages/react/slot/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-slot/dist/packages/react/slot/src/Slot.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-primitive/dist/packages/react/primitive/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-primitive/dist/packages/react/primitive/src/Primitive.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-use-escape-keydown/dist/packages/react/use-escape-keydown/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-use-escape-keydown/dist/packages/react/use-escape-keydown/src/useEscapeKeydown.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-dismissable-layer/dist/packages/react/dismissable-layer/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-dismissable-layer/dist/packages/react/dismissable-layer/src/DismissableLayer.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-focus-scope/dist/packages/react/focus-scope/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-focus-scope/dist/packages/react/focus-scope/src/FocusScope.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-portal/dist/packages/react/portal/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-portal/dist/packages/react/portal/src/Portal.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-presence/dist/packages/react/presence/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-presence/dist/packages/react/presence/src/Presence.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-presence/dist/packages/react/presence/src/useStateMachine.tsx", "../../node_modules/cmdk/node_modules/@radix-ui/react-focus-guards/dist/packages/react/focus-guards/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-focus-guards/dist/packages/react/focus-guards/src/FocusGuards.tsx", "../../node_modules/cmdk/node_modules/react-remove-scroll/dist/es2015/Combination.js", "../../node_modules/cmdk/node_modules/react-remove-scroll/dist/es2015/UI.js", "../../node_modules/cmdk/node_modules/react-remove-scroll/dist/es2015/medium.js", "../../node_modules/cmdk/node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "../../node_modules/cmdk/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "../../node_modules/cmdk/node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "../../node_modules/cmdk/node_modules/react-remove-scroll/dist/es2015/sidecar.js", "../../node_modules/cmdk/node_modules/@radix-ui/react-dialog/dist/packages/react/dialog/src/index.ts", "../../node_modules/cmdk/node_modules/@radix-ui/react-dialog/dist/packages/react/dialog/src/Dialog.tsx", "../../node_modules/cmdk/dist/index.mjs"], "sourcesContent": ["var U=1,Y=.9,H=.8,J=.17,p=.1,u=.999,$=.9999;var k=.99,m=/[\\\\\\/_+.#\"@\\[\\(\\{&]/,B=/[\\\\\\/_+.#\"@\\[\\(\\{&]/g,K=/[\\s-]/,X=/[\\s-]/g;function G(_,C,h,P,A,f,O){if(f===C.length)return A===_.length?U:k;var T=`${A},${f}`;if(O[T]!==void 0)return O[T];for(var L=P.charAt(f),c=h.indexOf(L,A),S=0,E,N,R,M;c>=0;)E=G(_,C,h,P,c+1,f+1,O),E>S&&(c===A?E*=U:m.test(_.charAt(c-1))?(E*=H,R=_.slice(A,c-1).match(B),R&&A>0&&(E*=Math.pow(u,R.length))):K.test(_.charAt(c-1))?(E*=Y,M=_.slice(A,c-1).match(X),M&&A>0&&(E*=Math.pow(u,M.length))):(E*=J,A>0&&(E*=Math.pow(u,c-A))),_.charAt(c)!==C.charAt(f)&&(E*=$)),(E<p&&h.charAt(c-1)===P.charAt(f+1)||P.charAt(f+1)===P.charAt(f)&&h.charAt(c-1)!==P.charAt(f))&&(N=G(_,C,h,P,c+1,f+2,O),N*p>E&&(E=N*p)),E>S&&(S=E),c=h.indexOf(L,c+1);return O[T]=S,S}function D(_){return _.toLowerCase().replace(X,\" \")}function W(_,C,h){return _=h&&h.length>0?`${_+\" \"+h.join(\" \")}`:_,G(_,C,D(_),D(C),0,0,{})}export{W as a};\n", "export { composeEventHandlers } from './primitive';\n", "function composeEventHandlers<E>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !((event as unknown) as Event).defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n", "export { composeRefs, useComposedRefs } from './composeRefs';\n", "import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    (ref as React.MutableRefObject<T>).current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]) {\n  return (node: T) => refs.forEach((ref) => setRef(ref, node));\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n", "export { createContext, createContextScope } from './createContext';\nexport type { CreateScope, Scope } from './createContext';\n", "import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  function Provider(props: ContextValueType & { children: React.ReactNode }) {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    function Provider(\n      props: ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    ) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    }\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n", "export { useLayoutEffect } from './useLayoutEffect';\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n", "export { useId } from './id';\n", "import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We `toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)['useId'.toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n", "export { useCallbackRef } from './useCallbackRef';\n", "import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n", "export { useControllableState } from './useControllableState';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n", "export {\n  Slot,\n  Slottable,\n  //\n  Root,\n} from './Slot';\nexport type { SlotProps } from './Slot';\n", "import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\nconst Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n\n  if (slottable) {\n    // the new element to render is the one passed as a child of `Slottable`\n    const newElement = slottable.props.children as React.ReactNode;\n\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        // because the new element will be the one rendered, we are only interested\n        // in grabbing its children (`newElement.props.children`)\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement)\n          ? (newElement.props.children as React.ReactNode)\n          : null;\n      } else {\n        return child;\n      }\n    });\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {React.isValidElement(newElement)\n          ? React.cloneElement(newElement, undefined, newChildren)\n          : null}\n      </SlotClone>\n    );\n  }\n\n  return (\n    <SlotClone {...slotProps} ref={forwardedRef}>\n      {children}\n    </SlotClone>\n  );\n});\n\nSlot.displayName = 'Slot';\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\nconst SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n\n  if (React.isValidElement(children)) {\n    return React.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      ref: forwardedRef ? composeRefs(forwardedRef, (children as any).ref) : (children as any).ref,\n    });\n  }\n\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\n\nSlotClone.displayName = 'SlotClone';\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst Slottable = ({ children }: { children: React.ReactNode }) => {\n  return <>{children}</>;\n};\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(child: React.ReactNode): child is React.ReactElement {\n  return React.isValidElement(child) && child.type === Slottable;\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\nconst Root = Slot;\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Root,\n};\nexport type { SlotProps };\n", "export {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n} from './Primitive';\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef } from './Primitive';\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { Slot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\n// Temporary while we await merge of this fix:\n// https://github.com/DefinitelyTyped/DefinitelyTyped/pull/55396\n// prettier-ignore\ntype PropsWithoutRef<P> = P extends any ? ('ref' extends keyof P ? Pick<P, Exclude<keyof P, 'ref'>> : P) : P;\ntype ComponentPropsWithoutRef<T extends React.ElementType> = PropsWithoutRef<\n  React.ComponentProps<T>\n>;\n\ntype Primitives = { [E in typeof NODES[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    React.useEffect(() => {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }, []);\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not nessesary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { ComponentPropsWithoutRef, PrimitivePropsWithRef };\n", "export { useEscapeKeydown } from './useEscapeKeydown';\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown);\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown);\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n", "export {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n} from './DismissableLayer';\nexport type { DismissableLayerProps } from './DismissableLayer';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ElementRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      } else {\n        // We need to remove the event listener in case the outside click has been canceled.\n        // See: https://github.com/radix-ui/primitives/issues/2171\n        ownerDocument.removeEventListener('click', handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n", "export {\n  FocusScope,\n  //\n  Root,\n} from './FocusScope';\nexport type { FocusScopeProps } from './FocusScope';\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "export {\n  Portal,\n  //\n  Root,\n} from './Portal';\nexport type { PortalProps } from './Portal';\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: HTMLElement | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container = globalThis?.document?.body, ...portalProps } = props;\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "export { Presence } from './Presence';\nexport type { PresenceProps } from './Presence';\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './useStateMachine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement;\n\n  const ref = useComposedRefs(presence.ref, (child as any).ref);\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration>({} as any);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied\n          // a frame after the animation ends, creating a flash of visible content.\n          // By manually flushing we ensure they sync within a frame, removing the flash.\n          ReactDOM.flushSync(() => send('ANIMATION_END'));\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      if (node) stylesRef.current = getComputedStyle(node);\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles?: CSSStyleDeclaration) {\n  return styles?.animationName || 'none';\n}\n\nexport { Presence };\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n", "export {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n} from './FocusGuards';\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.cssText = 'outline: none; opacity: 0; position: fixed; pointer-events: none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(function () { return styleSingleton(); })[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if ('touches' in event && event.touches.length === 2) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && e.target === event.target && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { gapMode: \"margin\" }) : null));\n}\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), s = _a[1], d = _a[2];\n            if (s > d) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== document.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        target = target.parentNode;\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    if (isDeltaPositive && ((noOverscroll && availableScroll === 0) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && availableScrollTop === 0) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n", "export {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n} from './Dialog';\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n} from './Dialog';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { Slot } from '@radix-ui/react-slot';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement>;\n  contentRef: React.RefObject<DialogContentElement>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ElementRef<typeof DismissableLayer>;\ntype DismissableLayerProps = Radix.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = Radix.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (beacuse of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ElementRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = Radix.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ElementRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = Radix.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ElementRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) throw new Error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n", "import{a as ce}from\"./chunk-NZJY6EH4.mjs\";import*as w from\"@radix-ui/react-dialog\";import*as t from\"react\";import{Primitive as I}from\"@radix-ui/react-primitive\";var V='[cmdk-group=\"\"]',X='[cmdk-group-items=\"\"]',ge='[cmdk-group-heading=\"\"]',Y='[cmdk-item=\"\"]',le=`${Y}:not([aria-disabled=\"true\"])`,Q=\"cmdk-item-select\",M=\"data-value\",Re=(r,o,n)=>ce(r,o,n),ue=t.createContext(void 0),G=()=>t.useContext(ue),de=t.createContext(void 0),Z=()=>t.useContext(de),fe=t.createContext(void 0),me=t.forwardRef((r,o)=>{let n=k(()=>{var e,s;return{search:\"\",value:(s=(e=r.value)!=null?e:r.defaultValue)!=null?s:\"\",filtered:{count:0,items:new Map,groups:new Set}}}),u=k(()=>new Set),c=k(()=>new Map),d=k(()=>new Map),f=k(()=>new Set),p=pe(r),{label:v,children:b,value:l,onValueChange:y,filter:S,shouldFilter:C,loop:L,disablePointerSelection:ee=!1,vimBindings:j=!0,...H}=r,te=t.useId(),$=t.useId(),K=t.useId(),x=t.useRef(null),g=Me();T(()=>{if(l!==void 0){let e=l.trim();n.current.value=e,h.emit()}},[l]),T(()=>{g(6,re)},[]);let h=t.useMemo(()=>({subscribe:e=>(f.current.add(e),()=>f.current.delete(e)),snapshot:()=>n.current,setState:(e,s,i)=>{var a,m,R;if(!Object.is(n.current[e],s)){if(n.current[e]=s,e===\"search\")z(),q(),g(1,U);else if(e===\"value\"&&(i||g(5,re),((a=p.current)==null?void 0:a.value)!==void 0)){let E=s!=null?s:\"\";(R=(m=p.current).onValueChange)==null||R.call(m,E);return}h.emit()}},emit:()=>{f.current.forEach(e=>e())}}),[]),B=t.useMemo(()=>({value:(e,s,i)=>{var a;s!==((a=d.current.get(e))==null?void 0:a.value)&&(d.current.set(e,{value:s,keywords:i}),n.current.filtered.items.set(e,ne(s,i)),g(2,()=>{q(),h.emit()}))},item:(e,s)=>(u.current.add(e),s&&(c.current.has(s)?c.current.get(s).add(e):c.current.set(s,new Set([e]))),g(3,()=>{z(),q(),n.current.value||U(),h.emit()}),()=>{d.current.delete(e),u.current.delete(e),n.current.filtered.items.delete(e);let i=O();g(4,()=>{z(),(i==null?void 0:i.getAttribute(\"id\"))===e&&U(),h.emit()})}),group:e=>(c.current.has(e)||c.current.set(e,new Set),()=>{d.current.delete(e),c.current.delete(e)}),filter:()=>p.current.shouldFilter,label:v||r[\"aria-label\"],disablePointerSelection:ee,listId:te,inputId:K,labelId:$,listInnerRef:x}),[]);function ne(e,s){var a,m;let i=(m=(a=p.current)==null?void 0:a.filter)!=null?m:Re;return e?i(e,n.current.search,s):0}function q(){if(!n.current.search||p.current.shouldFilter===!1)return;let e=n.current.filtered.items,s=[];n.current.filtered.groups.forEach(a=>{let m=c.current.get(a),R=0;m.forEach(E=>{let P=e.get(E);R=Math.max(P,R)}),s.push([a,R])});let i=x.current;A().sort((a,m)=>{var P,_;let R=a.getAttribute(\"id\"),E=m.getAttribute(\"id\");return((P=e.get(E))!=null?P:0)-((_=e.get(R))!=null?_:0)}).forEach(a=>{let m=a.closest(X);m?m.appendChild(a.parentElement===m?a:a.closest(`${X} > *`)):i.appendChild(a.parentElement===i?a:a.closest(`${X} > *`))}),s.sort((a,m)=>m[1]-a[1]).forEach(a=>{let m=x.current.querySelector(`${V}[${M}=\"${encodeURIComponent(a[0])}\"]`);m==null||m.parentElement.appendChild(m)})}function U(){let e=A().find(i=>i.getAttribute(\"aria-disabled\")!==\"true\"),s=e==null?void 0:e.getAttribute(M);h.setState(\"value\",s||void 0)}function z(){var s,i,a,m;if(!n.current.search||p.current.shouldFilter===!1){n.current.filtered.count=u.current.size;return}n.current.filtered.groups=new Set;let e=0;for(let R of u.current){let E=(i=(s=d.current.get(R))==null?void 0:s.value)!=null?i:\"\",P=(m=(a=d.current.get(R))==null?void 0:a.keywords)!=null?m:[],_=ne(E,P);n.current.filtered.items.set(R,_),_>0&&e++}for(let[R,E]of c.current)for(let P of E)if(n.current.filtered.items.get(P)>0){n.current.filtered.groups.add(R);break}n.current.filtered.count=e}function re(){var s,i,a;let e=O();e&&(((s=e.parentElement)==null?void 0:s.firstChild)===e&&((a=(i=e.closest(V))==null?void 0:i.querySelector(ge))==null||a.scrollIntoView({block:\"nearest\"})),e.scrollIntoView({block:\"nearest\"}))}function O(){var e;return(e=x.current)==null?void 0:e.querySelector(`${Y}[aria-selected=\"true\"]`)}function A(){var e;return Array.from((e=x.current)==null?void 0:e.querySelectorAll(le))}function W(e){let i=A()[e];i&&h.setState(\"value\",i.getAttribute(M))}function J(e){var R;let s=O(),i=A(),a=i.findIndex(E=>E===s),m=i[a+e];(R=p.current)!=null&&R.loop&&(m=a+e<0?i[i.length-1]:a+e===i.length?i[0]:i[a+e]),m&&h.setState(\"value\",m.getAttribute(M))}function oe(e){let s=O(),i=s==null?void 0:s.closest(V),a;for(;i&&!a;)i=e>0?we(i,V):Ie(i,V),a=i==null?void 0:i.querySelector(le);a?h.setState(\"value\",a.getAttribute(M)):J(e)}let ie=()=>W(A().length-1),ae=e=>{e.preventDefault(),e.metaKey?ie():e.altKey?oe(1):J(1)},se=e=>{e.preventDefault(),e.metaKey?W(0):e.altKey?oe(-1):J(-1)};return t.createElement(I.div,{ref:o,tabIndex:-1,...H,\"cmdk-root\":\"\",onKeyDown:e=>{var s;if((s=H.onKeyDown)==null||s.call(H,e),!e.defaultPrevented)switch(e.key){case\"n\":case\"j\":{j&&e.ctrlKey&&ae(e);break}case\"ArrowDown\":{ae(e);break}case\"p\":case\"k\":{j&&e.ctrlKey&&se(e);break}case\"ArrowUp\":{se(e);break}case\"Home\":{e.preventDefault(),W(0);break}case\"End\":{e.preventDefault(),ie();break}case\"Enter\":if(!e.nativeEvent.isComposing&&e.keyCode!==229){e.preventDefault();let i=O();if(i){let a=new Event(Q);i.dispatchEvent(a)}}}}},t.createElement(\"label\",{\"cmdk-label\":\"\",htmlFor:B.inputId,id:B.labelId,style:De},v),F(r,e=>t.createElement(de.Provider,{value:h},t.createElement(ue.Provider,{value:B},e))))}),be=t.forwardRef((r,o)=>{var K,x;let n=t.useId(),u=t.useRef(null),c=t.useContext(fe),d=G(),f=pe(r),p=(x=(K=f.current)==null?void 0:K.forceMount)!=null?x:c==null?void 0:c.forceMount;T(()=>{if(!p)return d.item(n,c==null?void 0:c.id)},[p]);let v=ve(n,u,[r.value,r.children,u],r.keywords),b=Z(),l=D(g=>g.value&&g.value===v.current),y=D(g=>p||d.filter()===!1?!0:g.search?g.filtered.items.get(n)>0:!0);t.useEffect(()=>{let g=u.current;if(!(!g||r.disabled))return g.addEventListener(Q,S),()=>g.removeEventListener(Q,S)},[y,r.onSelect,r.disabled]);function S(){var g,h;C(),(h=(g=f.current).onSelect)==null||h.call(g,v.current)}function C(){b.setState(\"value\",v.current,!0)}if(!y)return null;let{disabled:L,value:ee,onSelect:j,forceMount:H,keywords:te,...$}=r;return t.createElement(I.div,{ref:N([u,o]),...$,id:n,\"cmdk-item\":\"\",role:\"option\",\"aria-disabled\":!!L,\"aria-selected\":!!l,\"data-disabled\":!!L,\"data-selected\":!!l,onPointerMove:L||d.disablePointerSelection?void 0:C,onClick:L?void 0:S},r.children)}),he=t.forwardRef((r,o)=>{let{heading:n,children:u,forceMount:c,...d}=r,f=t.useId(),p=t.useRef(null),v=t.useRef(null),b=t.useId(),l=G(),y=D(C=>c||l.filter()===!1?!0:C.search?C.filtered.groups.has(f):!0);T(()=>l.group(f),[]),ve(f,p,[r.value,r.heading,v]);let S=t.useMemo(()=>({id:f,forceMount:c}),[c]);return t.createElement(I.div,{ref:N([p,o]),...d,\"cmdk-group\":\"\",role:\"presentation\",hidden:y?void 0:!0},n&&t.createElement(\"div\",{ref:v,\"cmdk-group-heading\":\"\",\"aria-hidden\":!0,id:b},n),F(r,C=>t.createElement(\"div\",{\"cmdk-group-items\":\"\",role:\"group\",\"aria-labelledby\":n?b:void 0},t.createElement(fe.Provider,{value:S},C))))}),ye=t.forwardRef((r,o)=>{let{alwaysRender:n,...u}=r,c=t.useRef(null),d=D(f=>!f.search);return!n&&!d?null:t.createElement(I.div,{ref:N([c,o]),...u,\"cmdk-separator\":\"\",role:\"separator\"})}),Ee=t.forwardRef((r,o)=>{let{onValueChange:n,...u}=r,c=r.value!=null,d=Z(),f=D(l=>l.search),p=D(l=>l.value),v=G(),b=t.useMemo(()=>{var y;let l=(y=v.listInnerRef.current)==null?void 0:y.querySelector(`${Y}[${M}=\"${encodeURIComponent(p)}\"]`);return l==null?void 0:l.getAttribute(\"id\")},[]);return t.useEffect(()=>{r.value!=null&&d.setState(\"search\",r.value)},[r.value]),t.createElement(I.input,{ref:o,...u,\"cmdk-input\":\"\",autoComplete:\"off\",autoCorrect:\"off\",spellCheck:!1,\"aria-autocomplete\":\"list\",role:\"combobox\",\"aria-expanded\":!0,\"aria-controls\":v.listId,\"aria-labelledby\":v.labelId,\"aria-activedescendant\":b,id:v.inputId,type:\"text\",value:c?r.value:f,onChange:l=>{c||d.setState(\"search\",l.target.value),n==null||n(l.target.value)}})}),Se=t.forwardRef((r,o)=>{let{children:n,label:u=\"Suggestions\",...c}=r,d=t.useRef(null),f=t.useRef(null),p=G();return t.useEffect(()=>{if(f.current&&d.current){let v=f.current,b=d.current,l,y=new ResizeObserver(()=>{l=requestAnimationFrame(()=>{let S=v.offsetHeight;b.style.setProperty(\"--cmdk-list-height\",S.toFixed(1)+\"px\")})});return y.observe(v),()=>{cancelAnimationFrame(l),y.unobserve(v)}}},[]),t.createElement(I.div,{ref:N([d,o]),...c,\"cmdk-list\":\"\",role:\"listbox\",\"aria-label\":u,id:p.listId},F(r,v=>t.createElement(\"div\",{ref:N([f,p.listInnerRef]),\"cmdk-list-sizer\":\"\"},v)))}),Ce=t.forwardRef((r,o)=>{let{open:n,onOpenChange:u,overlayClassName:c,contentClassName:d,container:f,...p}=r;return t.createElement(w.Root,{open:n,onOpenChange:u},t.createElement(w.Portal,{container:f},t.createElement(w.Overlay,{\"cmdk-overlay\":\"\",className:c}),t.createElement(w.Content,{\"aria-label\":r.label,\"cmdk-dialog\":\"\",className:d},t.createElement(me,{ref:o,...p}))))}),xe=t.forwardRef((r,o)=>D(u=>u.filtered.count===0)?t.createElement(I.div,{ref:o,...r,\"cmdk-empty\":\"\",role:\"presentation\"}):null),Pe=t.forwardRef((r,o)=>{let{progress:n,children:u,label:c=\"Loading...\",...d}=r;return t.createElement(I.div,{ref:o,...d,\"cmdk-loading\":\"\",role:\"progressbar\",\"aria-valuenow\":n,\"aria-valuemin\":0,\"aria-valuemax\":100,\"aria-label\":c},F(r,f=>t.createElement(\"div\",{\"aria-hidden\":!0},f)))}),He=Object.assign(me,{List:Se,Item:be,Input:Ee,Group:he,Separator:ye,Dialog:Ce,Empty:xe,Loading:Pe});function we(r,o){let n=r.nextElementSibling;for(;n;){if(n.matches(o))return n;n=n.nextElementSibling}}function Ie(r,o){let n=r.previousElementSibling;for(;n;){if(n.matches(o))return n;n=n.previousElementSibling}}function pe(r){let o=t.useRef(r);return T(()=>{o.current=r}),o}var T=typeof window==\"undefined\"?t.useEffect:t.useLayoutEffect;function k(r){let o=t.useRef();return o.current===void 0&&(o.current=r()),o}function N(r){return o=>{r.forEach(n=>{typeof n==\"function\"?n(o):n!=null&&(n.current=o)})}}function D(r){let o=Z(),n=()=>r(o.snapshot());return t.useSyncExternalStore(o.subscribe,n,n)}function ve(r,o,n,u=[]){let c=t.useRef(),d=G();return T(()=>{var v;let f=(()=>{var b;for(let l of n){if(typeof l==\"string\")return l.trim();if(typeof l==\"object\"&&\"current\"in l)return l.current?(b=l.current.textContent)==null?void 0:b.trim():c.current}})(),p=u.map(b=>b.trim());d.value(r,f,p),(v=o.current)==null||v.setAttribute(M,f),c.current=f}),c}var Me=()=>{let[r,o]=t.useState(),n=k(()=>new Map);return T(()=>{n.current.forEach(u=>u()),n.current=new Map},[r]),(u,c)=>{n.current.set(u,c),o({})}};function Te(r){let o=r.type;return typeof o==\"function\"?o(r.props):\"render\"in o?o.render(r.props):r}function F({asChild:r,children:o},n){return r&&t.isValidElement(o)?t.cloneElement(Te(o),{ref:o.ref},n(o.props.children)):n(o)}var De={position:\"absolute\",width:\"1px\",height:\"1px\",padding:\"0\",margin:\"-1px\",overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\"};export{He as Command,Ce as CommandDialog,xe as CommandEmpty,he as CommandGroup,Ee as CommandInput,be as CommandItem,Se as CommandList,Pe as CommandLoading,me as CommandRoot,ye as CommandSeparator,D as useCommandState};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,IAAE;AAAN,IAAQ,IAAE;AAAV,IAAa,IAAE;AAAf,IAAkB,IAAE;AAApB,IAAwB,IAAE;AAA1B,IAA6B,IAAE;AAA/B,IAAoC,IAAE;AAAM,IAAI,IAAE;AAAN,IAAU,IAAE;AAAZ,IAAkC,IAAE;AAApC,IAA2D,IAAE;AAA7D,IAAqE,IAAE;AAAS,SAAS,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,MAAG,MAAI,EAAE,OAAO,QAAO,MAAI,EAAE,SAAO,IAAE;AAAE,MAAIA,KAAE,GAAG,CAAC,IAAI,CAAC;AAAG,MAAG,EAAEA,EAAC,MAAI,OAAO,QAAO,EAAEA,EAAC;AAAE,WAAQ,IAAE,EAAE,OAAO,CAAC,GAAE,IAAE,EAAE,QAAQ,GAAE,CAAC,GAAE,IAAE,GAAE,GAAEC,IAAE,GAAEC,IAAE,KAAG,IAAG,KAAE,EAAE,GAAE,GAAE,GAAE,GAAE,IAAE,GAAE,IAAE,GAAE,CAAC,GAAE,IAAE,MAAI,MAAI,IAAE,KAAG,IAAE,EAAE,KAAK,EAAE,OAAO,IAAE,CAAC,CAAC,KAAG,KAAG,GAAE,IAAE,EAAE,MAAM,GAAE,IAAE,CAAC,EAAE,MAAM,CAAC,GAAE,KAAG,IAAE,MAAI,KAAG,KAAK,IAAI,GAAE,EAAE,MAAM,MAAI,EAAE,KAAK,EAAE,OAAO,IAAE,CAAC,CAAC,KAAG,KAAG,GAAEA,KAAE,EAAE,MAAM,GAAE,IAAE,CAAC,EAAE,MAAM,CAAC,GAAEA,MAAG,IAAE,MAAI,KAAG,KAAK,IAAI,GAAEA,GAAE,MAAM,OAAK,KAAG,GAAE,IAAE,MAAI,KAAG,KAAK,IAAI,GAAE,IAAE,CAAC,KAAI,EAAE,OAAO,CAAC,MAAI,EAAE,OAAO,CAAC,MAAI,KAAG,MAAK,IAAE,KAAG,EAAE,OAAO,IAAE,CAAC,MAAI,EAAE,OAAO,IAAE,CAAC,KAAG,EAAE,OAAO,IAAE,CAAC,MAAI,EAAE,OAAO,CAAC,KAAG,EAAE,OAAO,IAAE,CAAC,MAAI,EAAE,OAAO,CAAC,OAAKD,KAAE,EAAE,GAAE,GAAE,GAAE,GAAE,IAAE,GAAE,IAAE,GAAE,CAAC,GAAEA,KAAE,IAAE,MAAI,IAAEA,KAAE,KAAI,IAAE,MAAI,IAAE,IAAG,IAAE,EAAE,QAAQ,GAAE,IAAE,CAAC;AAAE,SAAO,EAAED,EAAC,IAAE,GAAE;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,EAAE,YAAY,EAAE,QAAQ,GAAE,GAAG;AAAC;AAAC,SAAS,EAAE,GAAE,GAAE,GAAE;AAAC,SAAO,IAAE,KAAG,EAAE,SAAO,IAAE,GAAG,IAAE,MAAI,EAAE,KAAK,GAAG,CAAC,KAAG,GAAE,EAAE,GAAE,GAAE,EAAE,CAAC,GAAE,EAAE,CAAC,GAAE,GAAE,GAAE,CAAC,CAAC;AAAC;;;;;;AEAv4B,SAASG,0CACPC,sBACAC,iBACA,EAAA,2BAA6B,KAA3BC,IAAoC,CAAA,GACtC;AACA,SAAO,SAASC,YAAYC,OAAU;AACpCJ,6BAAoB,QAApBA,yBAAoB,UAApBA,qBAAuBI,KAAH;AAEpB,QAAIF,6BAA6B,SAAS,CAAGE,MAA4BC,iBACvE,QAAOJ,oBAAP,QAAOA,oBAAP,SAAA,SAAOA,gBAAkBG,KAAH;;;;;;AED5B,SAASE,6BAAUC,KAAqBC,OAAU;AAChD,MAAI,OAAOD,QAAQ,WACjBA,KAAIC,KAAD;WACMD,QAAQ,QAAQA,QAAQE,OAChCF,KAAkCG,UAAUF;;AAQjD,SAASG,6CAAkBC,MAAwB;AACjD,SAAQC,CAAAA,SAAYD,KAAKE;IAASP,CAAAA,QAAQD,6BAAOC,KAAKM,IAAN;EAA5B;;AAOtB,SAASE,6CAAsBH,MAAwB;AAErD,aAAOI,aAAAA,aAAkBL,0CAAW,GAAIC,IAAJ,GAAWA,IAAxC;;;;;AE5BT,SAASK,0CACPC,mBACAC,gBACA;AACA,QAAMC,cAAUC,cAAAA,eAAkDF,cAAlD;AAEhB,WAASG,SAASC,OAAyD;AACzE,UAAM,EATV,UASsB,GAAGC,QAAH,IAAeD;AAGjC,UAAME,YAAQJ,cAAAA;MAAc,MAAMG;MAASE,OAAOC,OAAOH,OAAd;IAA7B;AACd,eAAO,cAAAI,eAAC,QAAQ,UAAhB;MAAyB;OAAeC,QAAjC;;AAGT,WAASC,YAAWC,cAAsB;AACxC,UAAMP,cAAUH,cAAAA,YAAiBD,OAAjB;AAChB,QAAII,QAAS,QAAOA;AACpB,QAAIL,mBAAmBa,OAAW,QAAOb;AAEzC,UAAM,IAAIc,MAAO,KAAIF,YAAa,4BAA2Bb,iBAAkB,IAAzE;;AAGRI,WAASY,cAAchB,oBAAoB;AAC3C,SAAO;IAACI;IAAUQ;;;AAcpB,SAASK,yCAAmBC,WAAmBC,yBAAwC,CAAA,GAAI;AACzF,MAAIC,kBAAyB,CAAA;AAM7B,WAASrB,2CACPC,mBACAC,gBACA;AACA,UAAMoB,kBAAclB,cAAAA,eAAkDF,cAAlD;AACpB,UAAMqB,QAAQF,gBAAgBG;AAC9BH,sBAAkB;SAAIA;MAAiBnB;;AAEvC,aAASG,SACPC,OACA;AACA,YAAM,EAzDZ,OAAA,UAyD+B,GAAGC,QAAH,IAAeD;AACxC,YAAMH,WAAUsB,UAAK,QAALA,UAAK,SAAL,SAAAA,MAAQN,SAAH,EAAcI,KAAnB,MAA6BD;AAG7C,YAAMd,YAAQJ,cAAAA;QAAc,MAAMG;QAASE,OAAOC,OAAOH,OAAd;MAA7B;AACd,iBAAO,cAAAI,eAAC,QAAQ,UAAhB;QAAyB;SAAeC,QAAjC;;AAGT,aAASC,YAAWC,cAAsBW,OAA4C;AACpF,YAAMtB,WAAUsB,UAAK,QAALA,UAAK,SAAL,SAAAA,MAAQN,SAAH,EAAcI,KAAnB,MAA6BD;AAC7C,YAAMf,cAAUH,cAAAA,YAAiBD,OAAjB;AAChB,UAAII,QAAS,QAAOA;AACpB,UAAIL,mBAAmBa,OAAW,QAAOb;AAEzC,YAAM,IAAIc,MAAO,KAAIF,YAAa,4BAA2Bb,iBAAkB,IAAzE;;AAGRI,aAASY,cAAchB,oBAAoB;AAC3C,WAAO;MAACI;MAAUQ;;;AAOpB,QAAMa,cAA2B,MAAM;AACrC,UAAMC,gBAAgBN,gBAAgBO,IAAK1B,CAAAA,mBAAmB;AAC5D,iBAAOE,cAAAA,eAAoBF,cAApB;KADa;AAGtB,WAAO,SAAS2B,SAASJ,OAAc;AACrC,YAAMK,YAAWL,UAAK,QAALA,UAAK,SAAL,SAAAA,MAAQN,SAAH,MAAiBQ;AACvC,iBAAOvB,cAAAA;QACL,OAAO;UAAE,CAAE,UAASe,SAAU,EAArB,GAAyB;YAAE,GAAGM;YAAO,CAACN,SAAD,GAAaW;;;QAC3D;UAACL;UAAOK;;MAFH;;;AAOXJ,cAAYP,YAAYA;AACxB,SAAO;IAACnB;IAAe+B,2CAAqBL,aAAD,GAAiBN,sBAAjB;;;AAO7C,SAASW,8CAAwBC,QAAuB;AACtD,QAAMC,YAAYD,OAAO,CAAD;AACxB,MAAIA,OAAOR,WAAW,EAAG,QAAOS;AAEhC,QAAMP,eAA2B,MAAM;AACrC,UAAMQ,aAAaF,OAAOJ;MAAKF,CAAAA,iBAAiB;QAC9CG,UAAUH,YAAW;QACrBP,WAAWO,YAAYP;;IAFN;AAKnB,WAAO,SAASgB,kBAAkBC,gBAAgB;AAChD,YAAMC,cAAaH,WAAWI,OAAO,CAACD,YAAY,EAlHxD,UAAA,UAkHoElB,MAAgB;AAI5E,cAAMoB,aAAaV,SAASO,cAAD;AAC3B,cAAMI,eAAeD,WAAY,UAASpB,SAAU,EAArB;AAC/B,eAAO;UAAE,GAAGkB;UAAY,GAAGG;;SAC1B,CAAA,CAPgB;AASnB,iBAAOpC,cAAAA;QAAc,OAAO;UAAE,CAAE,UAAS6B,UAAUd,SAAU,EAA/B,GAAmCkB;;QAAe;UAACA;;MAA1E;;;AAIXX,eAAYP,YAAYc,UAAUd;AAClC,SAAOO;;;;;;;;AEvHT,IAAMe,4CAAkBC,QAAQC,eAAD,QAACA,eAAD,SAAA,SAACA,WAAYC,QAAb,IAAyBC,cAAAA,kBAAwB,MAAM;AAAA;;;AELtF,IAAMC,mCAAcC,aAAc,QAAQC,SAAR,CAAf,MAAuC,MAAMC;AAChE,IAAIC,8BAAQ;AAEZ,SAASC,0CAAMC,iBAAkC;AAC/C,QAAM,CAACC,IAAIC,KAAL,IAAoBC,sBAA6BT,iCAAU,CAA7C;AAEpBU,4CAAgB,MAAM;AACpB,QAAI,CAACJ,gBAAiBE;MAAOG,CAAAA,YAAYA,YAAb,QAAaA,YAAb,SAAaA,UAAWC,OAAOR,6BAAD;IAA/B;KAC1B;IAACE;GAFW;AAGf,SAAOA,oBAAoBC,KAAM,SAAQA,EAAG,KAAI;;;;;;;;AEPlD,SAASM,0CAAkDC,UAA4B;AACrF,QAAMC,kBAAcC,cAAAA,QAAaF,QAAb;AAEpBE,oBAAAA,WAAgB,MAAM;AACpBD,gBAAYE,UAAUH;GADxB;AAKA,aAAOE,cAAAA;IAAc,MAAO,IAAIE,SAAhC;AAA4B,UAAA;AAAA,cAAA,uBAAaH,YAAYE,aAAzB,QAAA,yBAAA,SAAA,SAAa,qBAAA,KAAAF,aAAW,GAAcG,IAAd;;IAA2B,CAAA;EAAxE;;;;AEHT,SAASC,yCAAwB,EAXjC,MAAA,aAWiC,WAGpB,MAAM;AAAA,EAAjBC,GACgC;AAChC,QAAM,CAACC,kBAAkBC,mBAAnB,IAA0CC,2CAAqB;IAhBvE;IAAA;GAgBsE;AACpE,QAAMC,eAAeC,SAASC;AAC9B,QAAMC,SAAQH,eAAeC,OAAOJ;AACpC,QAAMO,eAAeC,0CAAeT,QAAD;AAEnC,QAAMU,eAAgEC,cAAAA,aACnEC,CAAAA,cAAc;AACb,QAAIR,cAAc;AAChB,YAAMS,SAASD;AACf,YAAML,QAAQ,OAAOK,cAAc,aAAaC,OAAOR,IAAD,IAASO;AAC/D,UAAIL,UAAUF,KAAMG,cAAaD,KAAD;UAEhCL,qBAAoBU,SAAD;KAGvB;IAACR;IAAcC;IAAMH;IAAqBM;GAV0B;AAatE,SAAO;IAACD;IAAOG;;;AAGjB,SAASP,2CAAwB,EArCjC,aAAA,SAuCEH,GAC8C;AAC9C,QAAMc,wBAAoBH,cAAAA,UAA8BI,WAA9B;AAC1B,QAAM,CAACR,KAAD,IAAUO;AAChB,QAAME,mBAAeL,cAAAA,QAAaJ,KAAb;AACrB,QAAMC,eAAeC,0CAAeT,QAAD;AAEnCW,oBAAAA,WAAgB,MAAM;AACpB,QAAIK,aAAaC,YAAYV,OAAO;AAClCC,mBAAaD,KAAD;AACZS,mBAAaC,UAAUV;;KAExB;IAACA;IAAOS;IAAcR;GALzB;AAOA,SAAOM;;;;;;;;;;;;AE1CT,IAAMI,gDAAOC,cAAAA,YAAyC,CAACC,OAAOC,iBAAiB;AAC7E,QAAM,EAAA,UAAY,GAAGC,UAAH,IAAiBF;AACnC,QAAMG,gBAAgBJ,cAAAA,SAAeK,QAAQC,QAAvB;AACtB,QAAMC,YAAYH,cAAcI,KAAKC,iCAAnB;AAElB,MAAIF,WAAW;AAEb,UAAMG,aAAaH,UAAUN,MAAMK;AAEnC,UAAMK,cAAcP,cAAcQ,IAAKC,CAAAA,UAAU;AAC/C,UAAIA,UAAUN,WAAW;AAGvB,YAAIP,cAAAA,SAAec,MAAMJ,UAArB,IAAmC,EAAG,QAAOV,cAAAA,SAAee,KAAK,IAApB;AACjD,mBAAOf,cAAAA,gBAAqBU,UAArB,IACFA,WAAWT,MAAMK,WAClB;YAEJ,QAAOO;KATS;AAapB,eACE,cAAAG,eAAC,iCAAD,SAAA,CAAA,GAAeb,WADjB;MAC4B,KAAKD;KAA/B,OACGF,cAAAA,gBAAqBU,UAArB,QACGV,cAAAA,cAAmBU,YAAYO,QAAWN,WAA1C,IACA,IAHN;;AAQJ,aACE,cAAAK,eAAC,iCAAD,SAAA,CAAA,GAAeb,WADjB;IAC4B,KAAKD;GAA/B,GACGI,QADH;CAhCS;AAsCbP,0CAAKmB,cAAc;AAUnB,IAAMC,sCAAYnB,cAAAA,YAAsC,CAACC,OAAOC,iBAAiB;AAC/E,QAAM,EAAA,UAAY,GAAGC,UAAH,IAAiBF;AAEnC,UAAID,cAAAA,gBAAqBM,QAArB,EACF,YAAON,cAAAA,cAAmBM,UAAU;IAClC,GAAGc,iCAAWjB,WAAWG,SAASL,KAArB;IACboB,KAAKnB,eAAeoB,0CAAYpB,cAAeI,SAAiBe,GAAjC,IAAyCf,SAAiBe;GAFpF;AAMT,SAAOrB,cAAAA,SAAec,MAAMR,QAArB,IAAiC,IAAIN,cAAAA,SAAee,KAAK,IAApB,IAA4B;CAVxD;AAalBI,gCAAUD,cAAc;AAMxB,IAAMK,4CAAY,CAAC,EAAA,SAAEjB,MAA8C;AACjE,aAAO,cAAAU,eAAA,cAAAQ,UAAA,MAAGlB,QAAH;;AAOT,SAASG,kCAAYI,OAAqD;AACxE,aAAOb,cAAAA,gBAAqBa,KAArB,KAA+BA,MAAMY,SAASF;;AAGvD,SAASH,iCAAWjB,WAAqBuB,YAAsB;AAE7D,QAAMC,gBAAgB;IAAE,GAAGD;;AAE3B,aAAWE,YAAYF,YAAY;AACjC,UAAMG,gBAAgB1B,UAAUyB,QAAD;AAC/B,UAAME,iBAAiBJ,WAAWE,QAAD;AAEjC,UAAMG,YAAY,WAAWC,KAAKJ,QAAhB;AAClB,QAAIG,WAAW;AAEb,UAAIF,iBAAiBC,eACnBH,eAAcC,QAAD,IAAa,IAAIK,SAAoB;AAChDH,uBAAc,GAAIG,IAAJ;AACdJ,sBAAa,GAAII,IAAJ;;eAIRJ,cACPF,eAAcC,QAAD,IAAaC;eAIrBD,aAAa,QACpBD,eAAcC,QAAD,IAAa;MAAE,GAAGC;MAAe,GAAGC;;aACxCF,aAAa,YACtBD,eAAcC,QAAD,IAAa;MAACC;MAAeC;MAAgBI,OAAOC,OAAvC,EAAgDC,KAAK,GAArD;;AAI9B,SAAO;IAAE,GAAGjC;IAAW,GAAGwB;;;;;AEpH5B,IAAMU,8BAAQ;EACZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAuBF,IAAMC,4CAAYD,4BAAME,OAAO,CAACC,WAAWC,SAAS;AAClD,QAAMC,WAAOC,cAAAA,YAAiB,CAACC,OAA2CC,iBAAsB;AAC9F,UAAM,EAAA,SAAW,GAAGC,eAAH,IAAsBF;AACvC,UAAMG,OAAYC,UAAUC,4CAAOR;AAEnCE,sBAAAA,WAAgB,MAAM;AACnBO,aAAeC,OAAOC,IAAI,UAAX,CAAhB,IAA0C;OACzC,CAAA,CAFH;AAIA,eAAO,cAAAC,eAAC,MAAD,SAAA,CAAA,GAAUP,gBAAjB;MAAiC,KAAKD;KAA/B,CAAA;GARI;AAWbH,OAAKY,cAAe,aAAYb,IAAK;AAErC,SAAO;IAAE,GAAGD;IAAW,CAACC,IAAD,GAAQC;;GAC9B,CAAA,CAfe;AA0DlB,SAASa,0CAAmDC,QAAqBC,OAAU;AACzF,MAAID,OAAQE,sBAAAA;IAAmB,MAAMF,OAAOG,cAAcF,KAArB;EAAzB;;;;;AEhGd,SAASG,0CACPC,qBACAC,gBAA0BC,eAAH,QAAGA,eAAH,SAAA,SAAGA,WAAYC,UACtC;AACA,QAAMC,kBAAkBC,0CAAeL,mBAAD;AAEtCM,oBAAAA,WAAgB,MAAM;AACpB,UAAMC,gBAAiBC,CAAAA,UAAyB;AAC9C,UAAIA,MAAMC,QAAQ,SAChBL,iBAAgBI,KAAD;;AAGnBP,kBAAcS,iBAAiB,WAAWH,aAA1C;AACA,WAAO,MAAMN,cAAcU,oBAAoB,WAAWJ,aAA7C;KACZ;IAACH;IAAiBH;GARrB;;;;AECF,IAAMW,+CAAyB;AAC/B,IAAMC,uCAAiB;AACvB,IAAMC,6CAAuB;AAC7B,IAAMC,sCAAgB;AAEtB,IAAIC;AAEJ,IAAMC,oDAA0BC,cAAAA,eAAoB;EAClDC,QAAQ,oBAAIC,IAAJ;EACRC,wCAAwC,oBAAID,IAAJ;EACxCE,UAAU,oBAAIF,IAAJ;CAHoB;AA0ChC,IAAMG,gDAAmBL,cAAAA,YACvB,CAACM,OAAOC,iBAAiB;AAAA,MAAA;AACvB,QAAM,EAAA,8BAC0B,OAD1B,iBAAA,sBAAA,gBAAA,mBAAA,WAOJ,GAAGC,WAAH,IACEF;AACJ,QAAMG,cAAUT,cAAAA,YAAiBD,6CAAjB;AAChB,QAAM,CAACW,OAAMC,OAAP,QAAkBX,cAAAA,UAA+C,IAA/C;AACxB,QAAMY,iBAAa,sBAAGF,UAAH,QAAGA,UAAH,SAAA,SAAGA,MAAME,mBAAT,QAAA,wBAAA,SAAA,sBAA0BC,eAA1B,QAA0BA,eAA1B,SAAA,SAA0BA,WAAYC;AACzD,QAAM,CAAA,EAAGC,KAAH,QAAYf,cAAAA,UAAe,CAAA,CAAf;AAClB,QAAMgB,eAAeC;IAAgBV;IAAeG,CAAAA,SAASC,QAAQD,IAAD;EAAhC;AACpC,QAAMT,SAASiB,MAAMC,KAAKV,QAAQR,MAAnB;AACf,QAAM,CAACmB,4CAAD,IAAiD;OAAIX,QAAQN;IAAwCkB,MAAM,EAA1D;AACvD,QAAMC,oDAAoDrB,OAAOsB,QAAQH,4CAAf;AAC1D,QAAMI,QAAQd,QAAOT,OAAOsB,QAAQb,KAAf,IAAuB;AAC5C,QAAMe,8BAA8BhB,QAAQN,uCAAuCuB,OAAO;AAC1F,QAAMC,yBAAyBH,SAASF;AAExC,QAAMM,qBAAqBC,4CAAuBC,CAAAA,UAAU;AAC1D,UAAMC,SAASD,MAAMC;AACrB,UAAMC,wBAAwB;SAAIvB,QAAQL;MAAU6B;MAAMC,CAAAA,WAAWA,OAAOC,SAASJ,MAAhB;IAAvC;AAC9B,QAAI,CAACJ,0BAA0BK,sBAAuB;AACtDI,6BAAoB,QAApBA,yBAAoB,UAApBA,qBAAuBN,KAAH;AACpBO,0BAAiB,QAAjBA,sBAAiB,UAAjBA,kBAAoBP,KAAH;AACjB,QAAI,CAACA,MAAMQ,iBAAkBC,eAAS,QAATA,cAAS,UAATA,UAAS;KACrC3B,aAP6C;AAShD,QAAM4B,eAAeC,sCAAiBX,CAAAA,UAAU;AAC9C,UAAMC,SAASD,MAAMC;AACrB,UAAMW,kBAAkB;SAAIjC,QAAQL;MAAU6B;MAAMC,CAAAA,WAAWA,OAAOC,SAASJ,MAAhB;IAAvC;AACxB,QAAIW,gBAAiB;AACrBC,uBAAc,QAAdA,mBAAc,UAAdA,eAAiBb,KAAH;AACdO,0BAAiB,QAAjBA,sBAAiB,UAAjBA,kBAAoBP,KAAH;AACjB,QAAI,CAACA,MAAMQ,iBAAkBC,eAAS,QAATA,cAAS,UAATA,UAAS;KACrC3B,aAPiC;AASpCgC,4CAAkBd,CAAAA,UAAU;AAC1B,UAAMe,iBAAiBrB,UAAUf,QAAQR,OAAOyB,OAAO;AACvD,QAAI,CAACmB,eAAgB;AACrBC,wBAAe,QAAfA,oBAAe,UAAfA,gBAAkBhB,KAAH;AACf,QAAI,CAACA,MAAMQ,oBAAoBC,WAAW;AACxCT,YAAMiB,eAAN;AACAR,gBAAS;;KAEV3B,aARa;AAUhBZ,oBAAAA,WAAgB,MAAM;AACpB,QAAI,CAACU,MAAM;AACX,QAAIsC,6BAA6B;AAC/B,UAAIvC,QAAQN,uCAAuCuB,SAAS,GAAG;AAC7D5B,0DAA4Bc,cAAcqC,KAAKC,MAAMC;AACrDvC,sBAAcqC,KAAKC,MAAMC,gBAAgB;;AAE3C1C,cAAQN,uCAAuCiD,IAAI1C,KAAnD;;AAEFD,YAAQR,OAAOmD,IAAI1C,KAAnB;AACA2C,yCAAc;AACd,WAAO,MAAM;AACX,UACEL,+BACAvC,QAAQN,uCAAuCuB,SAAS,EAExDd,eAAcqC,KAAKC,MAAMC,gBAAgBrD;;KAG5C;IAACY;IAAME;IAAeoC;IAA6BvC;GAnBtD;AA2BAT,oBAAAA,WAAgB,MAAM;AACpB,WAAO,MAAM;AACX,UAAI,CAACU,MAAM;AACXD,cAAQR,OAAOqD,OAAO5C,KAAtB;AACAD,cAAQN,uCAAuCmD,OAAO5C,KAAtD;AACA2C,2CAAc;;KAEf;IAAC3C;IAAMD;GAPV;AASAT,oBAAAA,WAAgB,MAAM;AACpB,UAAMuD,eAAe,MAAMxC,MAAM,CAAA,CAAD;AAChCD,aAAS0C,iBAAiB7D,sCAAgB4D,YAA1C;AACA,WAAO,MAAMzC,SAAS2C,oBAAoB9D,sCAAgB4D,YAA7C;KACZ,CAAA,CAJH;AAMA,aACE,cAAAG,eAAC,0CAAU,KAAX,SAAA,CAAA,GACMlD,YAFR;IAGI,KAAKQ;IACL,OAAO;MACLmC,eAAe1B,8BACXE,yBACE,SACA,SACFgC;MACJ,GAAGrD,MAAM4C;;IAEX,gBAAgBU,0CAAqBtD,MAAMuD,gBAAgBrB,aAAaqB,cAApC;IACpC,eAAeD,0CAAqBtD,MAAMwD,eAAetB,aAAasB,aAAnC;IACnC,sBAAsBF,0CACpBtD,MAAMyD,sBACNnC,mBAAmBmC,oBAFqB;GAb5C,CAAA;CA9FmB;AAoHzB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,oCAAc;AAKpB,IAAMC,gDAAyBjE,cAAAA,YAG7B,CAACM,OAAOC,iBAAiB;AACzB,QAAME,cAAUT,cAAAA,YAAiBD,6CAAjB;AAChB,QAAMmE,UAAMlE,cAAAA,QAA4C,IAA5C;AACZ,QAAMgB,eAAeC,0CAAgBV,cAAc2D,GAAf;AAEpClE,oBAAAA,WAAgB,MAAM;AACpB,UAAMU,OAAOwD,IAAIC;AACjB,QAAIzD,MAAM;AACRD,cAAQL,SAASgD,IAAI1C,IAArB;AACA,aAAO,MAAM;AACXD,gBAAQL,SAASkD,OAAO5C,IAAxB;;;KAGH;IAACD,QAAQL;GARZ;AAUA,aAAO,cAAAsD,eAAC,0CAAU,KAAX,SAAA,CAAA,GAAmBpD,OAA1B;IAAiC,KAAKU;GAA/B,CAAA;CAlBsB;AAqB/B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAYA,SAASa,4CACPO,sBACAxB,gBAA0BC,eAAH,QAAGA,eAAH,SAAA,SAAGA,WAAYC,UACtC;AACA,QAAMsD,2BAA2BC,0CAAejC,oBAAD;AAC/C,QAAMkC,kCAA8BtE,cAAAA,QAAa,KAAb;AACpC,QAAMuE,qBAAiBvE,cAAAA,QAAa,MAAM;EAAA,CAAnB;AAEvBA,oBAAAA,WAAgB,MAAM;AACpB,UAAMwE,oBAAqB1C,CAAAA,UAAwB;AACjD,UAAIA,MAAMC,UAAU,CAACuC,4BAA4BH,SAAS;AAGxD,YAASM,2CAAT,WAAoD;AAClDC,6DACE9E,4CACAwE,0BACAO,aACA;YAAEC,UAAU;WAJc;;AAH9B,cAAMD,cAAc;UAAEE,eAAe/C;;AAuBrC,YAAIA,MAAMgD,gBAAgB,SAAS;AACjClE,wBAAc6C,oBAAoB,SAASc,eAAeJ,OAA1D;AACAI,yBAAeJ,UAAUM;AACzB7D,wBAAc4C,iBAAiB,SAASe,eAAeJ,SAAS;YAAEY,MAAM;WAAxE;cAEAN,0CAAwC;;AAK1C7D,sBAAc6C,oBAAoB,SAASc,eAAeJ,OAA1D;AAEFG,kCAA4BH,UAAU;;AAexC,UAAMa,UAAUC,OAAOC,WAAW,MAAM;AACtCtE,oBAAc4C,iBAAiB,eAAegB,iBAA9C;OACC,CAFa;AAGhB,WAAO,MAAM;AACXS,aAAOE,aAAaH,OAApB;AACApE,oBAAc6C,oBAAoB,eAAee,iBAAjD;AACA5D,oBAAc6C,oBAAoB,SAASc,eAAeJ,OAA1D;;KAED;IAACvD;IAAewD;GA7DnB;AA+DA,SAAO;;IAELL,sBAAsB,MAAOO,4BAA4BH,UAAU;;;AAQvE,SAAS1B,sCACPE,gBACA/B,gBAA0BC,eAAH,QAAGA,eAAH,SAAA,SAAGA,WAAYC,UACtC;AACA,QAAMsE,qBAAqBf,0CAAe1B,cAAD;AACzC,QAAM0C,gCAA4BrF,cAAAA,QAAa,KAAb;AAElCA,oBAAAA,WAAgB,MAAM;AACpB,UAAMsF,cAAexD,CAAAA,UAAsB;AACzC,UAAIA,MAAMC,UAAU,CAACsD,0BAA0BlB,SAAS;AACtD,cAAMQ,cAAc;UAAEE,eAAe/C;;AACrC4C,2DAA6B7E,qCAAeuF,oBAAoBT,aAAa;UAC3EC,UAAU;SADgB;;;AAKhChE,kBAAc4C,iBAAiB,WAAW8B,WAA1C;AACA,WAAO,MAAM1E,cAAc6C,oBAAoB,WAAW6B,WAA7C;KACZ;IAAC1E;IAAewE;GAXnB;AAaA,SAAO;IACLvB,gBAAgB,MAAOwB,0BAA0BlB,UAAU;IAC3DL,eAAe,MAAOuB,0BAA0BlB,UAAU;;;AAI9D,SAASd,uCAAiB;AACxB,QAAMvB,QAAQ,IAAIyD,YAAY5F,oCAAhB;AACdmB,WAAS0E,cAAc1D,KAAvB;;AAGF,SAAS4C,mDACPe,MACAC,SACAC,QACA,EAAA,SAAEf,GACF;AACA,QAAM7C,SAAS4D,OAAOd,cAAc9C;AACpC,QAAMD,QAAQ,IAAIyD,YAAYE,MAAM;IAAEG,SAAS;IAAOC,YAAY;;GAApD;AACd,MAAIH,QAAS3D,QAAOyB,iBAAiBiC,MAAMC,SAA0B;IAAEX,MAAM;GAAhE;AAEb,MAAIH,SACFkB,2CAA4B/D,QAAQD,KAAT;MAE3BC,QAAOyD,cAAc1D,KAArB;;;;;AEpVJ,IAAMiE,2CAAqB;AAC3B,IAAMC,6CAAuB;AAC7B,IAAMC,sCAAgB;EAAEC,SAAS;EAAOC,YAAY;;AAQpD,IAAMC,yCAAmB;AAgCzB,IAAMC,gDAAaC,eAAAA,YAAqD,CAACC,OAAOC,iBAAiB;AAC/F,QAAM,EAAA,OACG,OADH,UAEM,OACVC,kBAAkBC,sBAClBC,oBAAoBC,wBACpB,GAAGC,WAAH,IACEN;AACJ,QAAM,CAACO,YAAWC,YAAZ,QAA4BT,eAAAA,UAAmC,IAAnC;AAClC,QAAMG,mBAAmBO,0CAAeN,oBAAD;AACvC,QAAMC,qBAAqBK,0CAAeJ,sBAAD;AACzC,QAAMK,4BAAwBX,eAAAA,QAAiC,IAAjC;AAC9B,QAAMY,eAAeC;IAAgBX;IAAeY,CAAAA,SAASL,aAAaK,IAAD;EAArC;AAEpC,QAAMC,iBAAaf,eAAAA,QAAa;IAC9BgB,QAAQ;IACRC,QAAQ;AACN,WAAKD,SAAS;;IAEhBE,SAAS;AACP,WAAKF,SAAS;;GANC,EAQhBG;AAGHnB,qBAAAA,WAAgB,MAAM;AACpB,QAAIoB,SAAS;AACX,UAASC,gBAAT,SAAuBC,OAAmB;AACxC,YAAIP,WAAWC,UAAU,CAACR,WAAW;AACrC,cAAMe,SAASD,MAAMC;AACrB,YAAIf,WAAUgB,SAASD,MAAnB,EACFZ,uBAAsBQ,UAAUI;YAEhCE,6BAAMd,sBAAsBQ,SAAS;UAAEO,QAAQ;SAA1C;SAIAC,iBAAT,SAAwBL,OAAmB;AACzC,YAAIP,WAAWC,UAAU,CAACR,WAAW;AACrC,cAAMoB,gBAAgBN,MAAMM;AAY5B,YAAIA,kBAAkB,KAAM;AAI5B,YAAI,CAACpB,WAAUgB,SAASI,aAAnB,EACHH,6BAAMd,sBAAsBQ,SAAS;UAAEO,QAAQ;SAA1C;SAOAG,kBAAT,SAAyBC,WAA6B;AACpD,cAAMC,iBAAiBC,SAASC;AAChC,YAAIF,mBAAmBC,SAASE,KAAM;AACtC,mBAAWC,YAAYL,UACrB,KAAIK,SAASC,aAAaC,SAAS,EAAGZ,6BAAMjB,UAAD;;AAI/CwB,eAASM,iBAAiB,WAAWjB,aAArC;AACAW,eAASM,iBAAiB,YAAYX,cAAtC;AACA,YAAMY,mBAAmB,IAAIC,iBAAiBX,eAArB;AACzB,UAAIrB,WAAW+B,kBAAiBE,QAAQjC,YAAW;QAAEkC,WAAW;QAAMC,SAAS;OAAhE;AAEf,aAAO,MAAM;AACXX,iBAASY,oBAAoB,WAAWvB,aAAxC;AACAW,iBAASY,oBAAoB,YAAYjB,cAAzC;AACAY,yBAAiBM,WAAjB;;;KAGH;IAACzB;IAASZ;IAAWO,WAAWC;GAzDnC;AA2DAhB,qBAAAA,WAAgB,MAAM;AACpB,QAAIQ,YAAW;AACbsC,6CAAiBC,IAAIhC,UAArB;AACA,YAAMiC,2BAA2BhB,SAASC;AAC1C,YAAMgB,sBAAsBzC,WAAUgB,SAASwB,wBAAnB;AAE5B,UAAI,CAACC,qBAAqB;AACxB,cAAMC,aAAa,IAAIC,YAAY1D,0CAAoBE,mCAApC;AACnBa,mBAAU8B,iBAAiB7C,0CAAoBU,gBAA/C;AACAK,mBAAU4C,cAAcF,UAAxB;AACA,YAAI,CAACA,WAAWG,kBAAkB;AAChCC,2CAAWC,kCAAYC,4CAAsBhD,UAAD,CAAtB,GAAoC;YAAEkB,QAAQ;WAA1D;AACV,cAAIM,SAASC,kBAAkBe,yBAC7BvB,6BAAMjB,UAAD;;;AAKX,aAAO,MAAM;AACXA,mBAAUoC,oBAAoBnD,0CAAoBU,gBAAlD;AAKAsD,mBAAW,MAAM;AACf,gBAAMC,eAAe,IAAIP,YAAYzD,4CAAsBC,mCAAtC;AACrBa,qBAAU8B,iBAAiB5C,4CAAsBW,kBAAjD;AACAG,qBAAU4C,cAAcM,YAAxB;AACA,cAAI,CAACA,aAAaL,iBAChB5B,6BAAMuB,6BAAD,QAACA,6BAAD,SAACA,2BAA4BhB,SAASE,MAAM;YAAER,QAAQ;WAAtD;AAGPlB,qBAAUoC,oBAAoBlD,4CAAsBW,kBAApD;AAEAyC,iDAAiBa,OAAO5C,UAAxB;WACC,CAXO;;;KAcb;IAACP;IAAWL;IAAkBE;IAAoBU;GAtCrD;AAyCA,QAAM6C,oBAAgB5D,eAAAA,aACnBsB,CAAAA,UAA+B;AAC9B,QAAI,CAACuC,QAAQ,CAACzC,QAAS;AACvB,QAAIL,WAAWC,OAAQ;AAEvB,UAAM8C,WAAWxC,MAAMyC,QAAQ,SAAS,CAACzC,MAAM0C,UAAU,CAAC1C,MAAM2C,WAAW,CAAC3C,MAAM4C;AAClF,UAAMnC,iBAAiBC,SAASC;AAEhC,QAAI6B,YAAY/B,gBAAgB;AAC9B,YAAMvB,YAAYc,MAAM6C;AACxB,YAAM,CAACC,OAAOC,IAAR,IAAgBC,uCAAiB9D,SAAD;AACtC,YAAM+D,4BAA4BH,SAASC;AAG3C,UAAI,CAACE,2BACH;AAAA,YAAIxC,mBAAmBvB,UAAWc,OAAMkD,eAAN;aAC7B;AACL,YAAI,CAAClD,MAAMmD,YAAY1C,mBAAmBsC,MAAM;AAC9C/C,gBAAMkD,eAAN;AACA,cAAIX,KAAMpC,6BAAM2C,OAAO;YAAE1C,QAAQ;WAAlB;mBACNJ,MAAMmD,YAAY1C,mBAAmBqC,OAAO;AACrD9C,gBAAMkD,eAAN;AACA,cAAIX,KAAMpC,6BAAM4C,MAAM;YAAE3C,QAAQ;WAAjB;;;;KAKvB;IAACmC;IAAMzC;IAASL,WAAWC;GA3BP;AA8BtB,aACE,eAAA0D,eAAC,0CAAU,KADb,SAAA;IACiB,UAAU;KAAQnE,YAAjC;IAA6C,KAAKK;IAAc,WAAWgD;GAA3E,CAAA;CA5Je;AAgKnB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAUA,SAASN,iCAAWqB,YAA2B,EAAA,SAAW,MAATjD,IAAmB,CAAA,GAAI;AACtE,QAAMsB,2BAA2BhB,SAASC;AAC1C,aAAW2C,aAAaD,YAAY;AAClClD,gCAAMmD,WAAW;;KAAZ;AACL,QAAI5C,SAASC,kBAAkBe,yBAA0B;;;AAO7D,SAASsB,uCAAiB9D,WAAwB;AAChD,QAAMmE,aAAanB,4CAAsBhD,SAAD;AACxC,QAAM4D,QAAQS,kCAAYF,YAAYnE,SAAb;AACzB,QAAM6D,OAAOQ,kCAAYF,WAAWG,QAAX,GAAsBtE,SAAvB;AACxB,SAAO;IAAC4D;IAAOC;;;AAajB,SAASb,4CAAsBhD,WAAwB;AACrD,QAAMuE,QAAuB,CAAA;AAC7B,QAAMC,SAAShD,SAASiD,iBAAiBzE,WAAW0E,WAAWC,cAAc;IAC3EC,YAAatE,CAAAA,SAAc;AACzB,YAAMuE,gBAAgBvE,KAAKwE,YAAY,WAAWxE,KAAKyE,SAAS;AAChE,UAAIzE,KAAK0E,YAAY1E,KAAK2E,UAAUJ,cAAe,QAAOH,WAAWQ;AAIrE,aAAO5E,KAAK6E,YAAY,IAAIT,WAAWU,gBAAgBV,WAAWQ;;GAPvD;AAUf,SAAOV,OAAOa,SAAP,EAAmBd,OAAMe,KAAKd,OAAOe,WAAlB;AAG1B,SAAOhB;;AAOT,SAASF,kCAAYmB,UAAyBxF,WAAwB;AACpE,aAAWyF,WAAWD,UAAU;AAE9B,QAAI,CAACE,+BAASD,SAAS;MAAEE,MAAM3F;KAAlB,EAAgC,QAAOyF;;;AAIxD,SAASC,+BAASpF,MAAmB,EAAA,KAAEqF,GAAgC;AACrE,MAAIC,iBAAiBtF,IAAD,EAAOuF,eAAe,SAAU,QAAO;AAC3D,SAAOvF,MAAM;AAEX,QAAIqF,SAASG,UAAaxF,SAASqF,KAAM,QAAO;AAChD,QAAIC,iBAAiBtF,IAAD,EAAOyF,YAAY,OAAQ,QAAO;AACtDzF,WAAOA,KAAK0F;;AAEd,SAAO;;AAGT,SAASC,wCAAkBR,SAAmE;AAC5F,SAAOA,mBAAmBS,oBAAoB,YAAYT;;AAG5D,SAASxE,4BAAMwE,SAAkC,EAAA,SAAW,MAATvE,IAAmB,CAAA,GAAI;AAExE,MAAIuE,WAAWA,QAAQxE,OAAO;AAC5B,UAAMuB,2BAA2BhB,SAASC;AAE1CgE,YAAQxE,MAAM;MAAEkF,eAAe;KAA/B;AAEA,QAAIV,YAAYjD,4BAA4ByD,wCAAkBR,OAAD,KAAavE,OACxEuE,SAAQvE,OAAR;;;AASN,IAAMoB,yCAAmB8D,6CAAsB;AAE/C,SAASA,+CAAyB;AAEhC,MAAIC,QAAyB,CAAA;AAE7B,SAAO;IACL9D,IAAIhC,YAA2B;AAE7B,YAAM+F,mBAAmBD,MAAM,CAAD;AAC9B,UAAI9F,eAAe+F,iBACjBA,sBAAgB,QAAhBA,qBAAgB,UAAhBA,iBAAkB7F,MAAlB;AAGF4F,cAAQE,kCAAYF,OAAO9F,UAAR;AACnB8F,YAAMG,QAAQjG,UAAd;;IAGF4C,OAAO5C,YAA2B;AAAA,UAAA;AAChC8F,cAAQE,kCAAYF,OAAO9F,UAAR;AACnB,OAAA,UAAA8F,MAAM,CAAD,OAAL,QAAA,YAAA,UAAA,QAAU3F,OAAV;;;;AAKN,SAAS6F,kCAAeE,OAAYC,MAAS;AAC3C,QAAMC,eAAe;OAAIF;;AACzB,QAAMG,QAAQD,aAAaE,QAAQH,IAArB;AACd,MAAIE,UAAU,GACZD,cAAaG,OAAOF,OAAO,CAA3B;AAEF,SAAOD;;AAGT,SAAS5D,kCAAYgE,OAAsB;AACzC,SAAOA,MAAMC;IAAQN,CAAAA,SAASA,KAAK5B,YAAY;EAAxC;;;;;;AE7UT,IAAMmC,oCAAc;AAWpB,IAAMC,gDAASC,eAAAA,YAA6C,CAACC,OAAOC,iBAAiB;AAAA,MAAA;AACnF,QAAM,EAAA,YAAcC,eAAH,QAAGA,eAAH,SAAA,UAAA,uBAAGA,WAAYC,cAAf,QAAA,yBAAA,SAAA,SAAG,qBAAsBC,MAAM,GAAGC,YAAH,IAAmBL;AACnE,SAAOM,YACHC,kBAAAA,QAASC,iBAAa,eAAAC,eAAC,0CAAU,KAAX,SAAA,CAAA,GAAmBJ,aAD7C;IAC0D,KAAKJ;GAArC,CAAA,GAAuDK,SAA7E,IACA;CAJS;AAOf,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;;;;;AGjBO,SAASI,0CACdC,cACAC,SACA;AACA,aAAOC,eAAAA,YAAiB,CAACC,OAAwBC,UAA4C;AAC3F,UAAMC,YAAaJ,QAAQE,KAAD,EAAgBC,KAAxB;AAClB,WAAOC,cAAP,QAAOA,cAAP,SAAOA,YAAaF;KACnBH,YAHI;;ADJT,IAAMM,4CAAqCC,CAAAA,UAAU;AACnD,QAAM,EAZR,SAAA,SAYmBC,IAAaD;AAC9B,QAAME,WAAWC,kCAAYC,OAAD;AAE5B,QAAMC,QACJ,OAAOJ,aAAa,aAChBA,SAAS;IAAEG,SAASF,SAASI;GAArB,IACRX,eAAAA,SAAeY,KAAKN,QAApB;AAGN,QAAMO,MAAMC,0CAAgBP,SAASM,KAAMH,MAAcG,GAA9B;AAC3B,QAAME,aAAa,OAAOT,aAAa;AACvC,SAAOS,cAAcR,SAASI,gBAAYX,eAAAA,cAAmBU,OAAO;IAvBtE;GAuB4C,IAAqC;;AAGjFN,0CAASY,cAAc;AAMvB,SAASR,kCAAYC,SAAkB;AACrC,QAAM,CAACQ,OAAMC,OAAP,QAAkBlB,eAAAA,UAAA;AACxB,QAAMmB,gBAAYnB,eAAAA,QAAkC,CAAA,CAAlC;AAClB,QAAMoB,qBAAiBpB,eAAAA,QAAaS,OAAb;AACvB,QAAMY,2BAAuBrB,eAAAA,QAAqB,MAArB;AAC7B,QAAMF,eAAeW,UAAU,YAAY;AAC3C,QAAM,CAACR,OAAOqB,IAAR,IAAgBzB,0CAAgBC,cAAc;IAClDyB,SAAS;MACPC,SAAS;MACTC,eAAe;;IAEjBC,kBAAkB;MAChBC,OAAO;MACPC,eAAe;;IAEjBC,WAAW;MACTF,OAAO;;GAV0B;AAcrC3B,qBAAAA,WAAgB,MAAM;AACpB,UAAM8B,uBAAuBC,uCAAiBZ,UAAUa,OAAX;AAC7CX,yBAAqBW,UAAU/B,UAAU,YAAY6B,uBAAuB;KAC3E;IAAC7B;GAHJ;AAKAgC,4CAAgB,MAAM;AACpB,UAAMC,SAASf,UAAUa;AACzB,UAAMG,aAAaf,eAAeY;AAClC,UAAMI,oBAAoBD,eAAe1B;AAEzC,QAAI2B,mBAAmB;AACrB,YAAMC,oBAAoBhB,qBAAqBW;AAC/C,YAAMF,uBAAuBC,uCAAiBG,MAAD;AAE7C,UAAIzB,QACFa,MAAK,OAAD;eACKQ,yBAAyB,WAAUI,WAAM,QAANA,WAAM,SAAN,SAAAA,OAAQI,aAAY;AAGhEhB,aAAK,SAAD;WACC;AAOL,cAAMiB,cAAcF,sBAAsBP;AAE1C,YAAIK,cAAcI,YAChBjB,MAAK,eAAD;YAEJA,MAAK,SAAD;;AAIRF,qBAAeY,UAAUvB;;KAE1B;IAACA;IAASa;GAjCE;AAmCfW,4CAAgB,MAAM;AACpB,QAAIhB,OAAM;AAMR,YAAMuB,qBAAsBtC,CAAAA,UAA0B;AACpD,cAAM4B,uBAAuBC,uCAAiBZ,UAAUa,OAAX;AAC7C,cAAMS,qBAAqBX,qBAAqBY,SAASxC,MAAMyC,aAApC;AAC3B,YAAIzC,MAAM0C,WAAW3B,SAAQwB;AAI3BI,gCAAAA;YAAmB,MAAMvB,KAAK,eAAD;UAA7B;;AAGJ,YAAMwB,uBAAwB5C,CAAAA,UAA0B;AACtD,YAAIA,MAAM0C,WAAW3B;AAEnBI,+BAAqBW,UAAUD,uCAAiBZ,UAAUa,OAAX;;AAGnDf,YAAK8B,iBAAiB,kBAAkBD,oBAAxC;AACA7B,YAAK8B,iBAAiB,mBAAmBP,kBAAzC;AACAvB,YAAK8B,iBAAiB,gBAAgBP,kBAAtC;AACA,aAAO,MAAM;AACXvB,cAAK+B,oBAAoB,kBAAkBF,oBAA3C;AACA7B,cAAK+B,oBAAoB,mBAAmBR,kBAA5C;AACAvB,cAAK+B,oBAAoB,gBAAgBR,kBAAzC;;;AAKFlB,WAAK,eAAD;KAEL;IAACL;IAAMK;GApCK;AAsCf,SAAO;IACLX,WAAW;MAAC;MAAW;MAAoB+B,SAASzC,KAAzC;IACXY,SAAKb,eAAAA,aAAmBiB,CAAAA,SAAsB;AAC5C,UAAIA,KAAME,WAAUa,UAAUiB,iBAAiBhC,IAAD;AAC9CC,cAAQD,IAAD;OACN,CAAA,CAHE;;;AAST,SAASc,uCAAiBG,QAA8B;AACtD,UAAOA,WAAM,QAANA,WAAM,SAAN,SAAAA,OAAQS,kBAAiB;;;;;AG3IlC,IAAIO,8BAAQ;AAWZ,SAASC,4CAAiB;AACxBC,qBAAAA,WAAgB,MAAM;AAAA,QAAA,cAAA;AACpB,UAAMC,aAAaC,SAASC,iBAAiB,0BAA1B;AACnBD,aAASE,KAAKC,sBAAsB,eAApC,eAAkDJ,WAAW,CAAD,OAA5D,QAAA,iBAAA,SAAA,eAAmEK,uCAAgB,CAAnF;AACAJ,aAASE,KAAKC,sBAAsB,cAApC,gBAAiDJ,WAAW,CAAD,OAA3D,QAAA,kBAAA,SAAA,gBAAkEK,uCAAgB,CAAlF;AACAC;AAEA,WAAO,MAAM;AACX,UAAIA,gCAAU,EACZL,UAASC,iBAAiB,0BAA1B,EAAsDK;QAASC,CAAAA,SAASA,KAAKC,OAAL;MAAxE;AAEFH;;KAED,CAAA,CAZH;;AAeF,SAASD,yCAAmB;AAC1B,QAAMK,UAAUT,SAASU,cAAc,MAAvB;AAChBD,UAAQE,aAAa,0BAA0B,EAA/C;AACAF,UAAQG,WAAW;AACnBH,UAAQI,MAAMC,UAAU;AACxB,SAAOL;;;;AClCT,IAAAM,SAAuB;;;ACAvB,YAAuB;;;ACAhB,IAAI,YAAY,oBAAoB;;;ADI3C,IAAI,UAAU,WAAY;AACtB;AACJ;AAIA,IAAI,eAAqB,iBAAW,SAAU,OAAO,WAAW;AAC5D,MAAI,MAAY,aAAO,IAAI;AAC3B,MAAI,KAAW,eAAS;AAAA,IACpB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,EACxB,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAC1C,MAAI,eAAe,MAAM,cAAc,WAAW,MAAM,UAAU,YAAY,MAAM,WAAW,kBAAkB,MAAM,iBAAiB,UAAU,MAAM,SAAS,SAAS,MAAM,QAAQ,UAAU,MAAM,SAAS,cAAc,MAAM,aAAa,QAAQ,MAAM,OAAO,iBAAiB,MAAM,gBAAgB,KAAK,MAAM,IAAI,YAAY,OAAO,SAAS,QAAQ,IAAI,OAAO,OAAO,OAAO,CAAC,gBAAgB,YAAY,aAAa,mBAAmB,WAAW,UAAU,WAAW,eAAe,SAAS,kBAAkB,IAAI,CAAC;AACtgB,MAAI,UAAU;AACd,MAAI,eAAe,aAAa,CAAC,KAAK,SAAS,CAAC;AAChD,MAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,SAAS;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,WAAkB,oBAAc,SAAS,EAAE,SAAS,WAAW,iBAAkC,QAAgB,aAA0B,OAAc,cAA4B,gBAAgB,CAAC,CAAC,gBAAgB,SAAS,IAAI,CAAC;AAAA,IACrO,eAAsB,mBAAmB,eAAS,KAAK,QAAQ,GAAG,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG,EAAE,KAAK,aAAa,CAAC,CAAC,IAAY,oBAAc,WAAW,SAAS,CAAC,GAAG,gBAAgB,EAAE,WAAsB,KAAK,aAAa,CAAC,GAAG,QAAQ;AAAA,EAAE;AACjQ,CAAC;AACD,aAAa,eAAe;AAAA,EACxB,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,OAAO;AACX;AACA,aAAa,aAAa;AAAA,EACtB,WAAW;AAAA,EACX,WAAW;AACf;;;AEjCA,IAAAC,SAAuB;;;ACDvB,IAAI,mBAAmB;AACvB,IAAI,OAAO,WAAW,aAAa;AAC/B,MAAI;AACI,cAAU,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,MAC/C,KAAK,WAAY;AACb,2BAAmB;AACnB,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAED,WAAO,iBAAiB,QAAQ,SAAS,OAAO;AAEhD,WAAO,oBAAoB,QAAQ,SAAS,OAAO;AAAA,EACvD,SACO,KAAK;AACR,uBAAmB;AAAA,EACvB;AACJ;AAdY;AAeL,IAAI,aAAa,mBAAmB,EAAE,SAAS,MAAM,IAAI;;;AClBhE,IAAI,uBAAuB,SAAU,MAAM;AAEvC,SAAO,KAAK,YAAY;AAC5B;AACA,IAAI,uBAAuB,SAAU,MAAM,UAAU;AACjD,MAAI,SAAS,OAAO,iBAAiB,IAAI;AACzC;AAAA;AAAA,IAEA,OAAO,QAAQ,MAAM;AAAA,IAEjB,EAAE,OAAO,cAAc,OAAO,aAAa,CAAC,qBAAqB,IAAI,KAAK,OAAO,QAAQ,MAAM;AAAA;AACvG;AACA,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AAChG,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAO,qBAAqB,MAAM,WAAW;AAAG;AACzF,IAAI,0BAA0B,SAAU,MAAM,MAAM;AACvD,MAAI,UAAU;AACd,KAAG;AAEC,QAAI,OAAO,eAAe,eAAe,mBAAmB,YAAY;AACpE,gBAAU,QAAQ;AAAA,IACtB;AACA,QAAI,eAAe,uBAAuB,MAAM,OAAO;AACvD,QAAI,cAAc;AACd,UAAI,KAAK,mBAAmB,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC;AAC/D,UAAI,IAAI,GAAG;AACP,eAAO;AAAA,MACX;AAAA,IACJ;AACA,cAAU,QAAQ;AAAA,EACtB,SAAS,WAAW,YAAY,SAAS;AACzC,SAAO;AACX;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,eAAe,GAAG;AAChF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,sBAAsB,SAAU,IAAI;AACpC,MAAI,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,cAAc,GAAG;AAC/E,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAI,yBAAyB,SAAU,MAAM,MAAM;AAC/C,SAAO,SAAS,MAAM,wBAAwB,IAAI,IAAI,wBAAwB,IAAI;AACtF;AACA,IAAI,qBAAqB,SAAU,MAAM,MAAM;AAC3C,SAAO,SAAS,MAAM,oBAAoB,IAAI,IAAI,oBAAoB,IAAI;AAC9E;AACA,IAAI,qBAAqB,SAAU,MAAM,WAAW;AAMhD,SAAO,SAAS,OAAO,cAAc,QAAQ,KAAK;AACtD;AACO,IAAI,eAAe,SAAU,MAAM,WAAW,OAAO,aAAa,cAAc;AACnF,MAAI,kBAAkB,mBAAmB,MAAM,OAAO,iBAAiB,SAAS,EAAE,SAAS;AAC3F,MAAI,QAAQ,kBAAkB;AAE9B,MAAI,SAAS,MAAM;AACnB,MAAI,eAAe,UAAU,SAAS,MAAM;AAC5C,MAAI,qBAAqB;AACzB,MAAI,kBAAkB,QAAQ;AAC9B,MAAI,kBAAkB;AACtB,MAAI,qBAAqB;AACzB,KAAG;AACC,QAAI,KAAK,mBAAmB,MAAM,MAAM,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC9F,QAAI,gBAAgB,WAAW,WAAW,kBAAkB;AAC5D,QAAI,YAAY,eAAe;AAC3B,UAAI,uBAAuB,MAAM,MAAM,GAAG;AACtC,2BAAmB;AACnB,8BAAsB;AAAA,MAC1B;AAAA,IACJ;AACA,aAAS,OAAO;AAAA,EACpB;AAAA;AAAA,IAEC,CAAC,gBAAgB,WAAW,SAAS;AAAA,IAEjC,iBAAiB,UAAU,SAAS,MAAM,KAAK,cAAc;AAAA;AAClE,MAAI,oBAAqB,gBAAgB,oBAAoB,KAAO,CAAC,gBAAgB,QAAQ,kBAAmB;AAC5G,yBAAqB;AAAA,EACzB,WACS,CAAC,oBACJ,gBAAgB,uBAAuB,KAAO,CAAC,gBAAgB,CAAC,QAAQ,qBAAsB;AAChG,yBAAqB;AAAA,EACzB;AACA,SAAO;AACX;;;AFzFO,IAAI,aAAa,SAAU,OAAO;AACrC,SAAO,oBAAoB,QAAQ,CAAC,MAAM,eAAe,CAAC,EAAE,SAAS,MAAM,eAAe,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;AACjH;AACO,IAAI,aAAa,SAAU,OAAO;AAAE,SAAO,CAAC,MAAM,QAAQ,MAAM,MAAM;AAAG;AAChF,IAAI,aAAa,SAAU,KAAK;AAC5B,SAAO,OAAO,aAAa,MAAM,IAAI,UAAU;AACnD;AACA,IAAI,eAAe,SAAU,GAAG,GAAG;AAAE,SAAO,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG;AAC5E,IAAI,gBAAgB,SAAU,IAAI;AAAE,SAAO,4BAA4B,OAAO,IAAI,mDAAmD,EAAE,OAAO,IAAI,2BAA2B;AAAG;AAChL,IAAI,YAAY;AAChB,IAAI,YAAY,CAAC;AACV,SAAS,oBAAoB,OAAO;AACvC,MAAI,qBAA2B,cAAO,CAAC,CAAC;AACxC,MAAI,gBAAsB,cAAO,CAAC,GAAG,CAAC,CAAC;AACvC,MAAI,aAAmB,cAAO;AAC9B,MAAI,KAAW,gBAAS,WAAW,EAAE,CAAC;AACtC,MAAI,QAAc,gBAAS,WAAY;AAAE,WAAO,eAAe;AAAA,EAAG,CAAC,EAAE,CAAC;AACtE,MAAI,YAAkB,cAAO,KAAK;AAClC,EAAM,iBAAU,WAAY;AACxB,cAAU,UAAU;AAAA,EACxB,GAAG,CAAC,KAAK,CAAC;AACV,EAAM,iBAAU,WAAY;AACxB,QAAI,MAAM,OAAO;AACb,eAAS,KAAK,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAC7D,UAAI,UAAU,cAAc,CAAC,MAAM,QAAQ,OAAO,IAAI,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU,GAAG,IAAI,EAAE,OAAO,OAAO;AAC/G,cAAQ,QAAQ,SAAU,IAAI;AAAE,eAAO,GAAG,UAAU,IAAI,uBAAuB,OAAO,EAAE,CAAC;AAAA,MAAG,CAAC;AAC7F,aAAO,WAAY;AACf,iBAAS,KAAK,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAChE,gBAAQ,QAAQ,SAAU,IAAI;AAAE,iBAAO,GAAG,UAAU,OAAO,uBAAuB,OAAO,EAAE,CAAC;AAAA,QAAG,CAAC;AAAA,MACpG;AAAA,IACJ;AACA;AAAA,EACJ,GAAG,CAAC,MAAM,OAAO,MAAM,QAAQ,SAAS,MAAM,MAAM,CAAC;AACrD,MAAI,oBAA0B,mBAAY,SAAU,OAAO,QAAQ;AAC/D,QAAI,aAAa,SAAS,MAAM,QAAQ,WAAW,GAAG;AAClD,aAAO,CAAC,UAAU,QAAQ;AAAA,IAC9B;AACA,QAAI,QAAQ,WAAW,KAAK;AAC5B,QAAI,aAAa,cAAc;AAC/B,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI,SAAS,YAAY,QAAQ,MAAM,SAAS,WAAW,CAAC,IAAI,MAAM,CAAC;AACvE,QAAI;AACJ,QAAI,SAAS,MAAM;AACnB,QAAI,gBAAgB,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM;AAEhE,QAAI,aAAa,SAAS,kBAAkB,OAAO,OAAO,SAAS,SAAS;AACxE,aAAO;AAAA,IACX;AACA,QAAI,+BAA+B,wBAAwB,eAAe,MAAM;AAChF,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,8BAA8B;AAC9B,oBAAc;AAAA,IAClB,OACK;AACD,oBAAc,kBAAkB,MAAM,MAAM;AAC5C,qCAA+B,wBAAwB,eAAe,MAAM;AAAA,IAEhF;AACA,QAAI,CAAC,8BAA8B;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW,WAAW,oBAAoB,UAAU,UAAU,SAAS;AACxE,iBAAW,UAAU;AAAA,IACzB;AACA,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,WAAW,WAAW;AAC1C,WAAO,aAAa,eAAe,QAAQ,OAAO,kBAAkB,MAAM,SAAS,QAAQ,IAAI;AAAA,EACnG,GAAG,CAAC,CAAC;AACL,MAAI,gBAAsB,mBAAY,SAAU,QAAQ;AACpD,QAAI,QAAQ;AACZ,QAAI,CAAC,UAAU,UAAU,UAAU,UAAU,SAAS,CAAC,MAAM,OAAO;AAEhE;AAAA,IACJ;AACA,QAAI,QAAQ,YAAY,QAAQ,WAAW,KAAK,IAAI,WAAW,KAAK;AACpE,QAAI,cAAc,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,aAAO,EAAE,SAAS,MAAM,QAAQ,EAAE,WAAW,MAAM,UAAU,aAAa,EAAE,OAAO,KAAK;AAAA,IAAG,CAAC,EAAE,CAAC;AAElK,QAAI,eAAe,YAAY,QAAQ;AACnC,UAAI,MAAM,YAAY;AAClB,cAAM,eAAe;AAAA,MACzB;AACA;AAAA,IACJ;AAEA,QAAI,CAAC,aAAa;AACd,UAAI,cAAc,UAAU,QAAQ,UAAU,CAAC,GAC1C,IAAI,UAAU,EACd,OAAO,OAAO,EACd,OAAO,SAAU,MAAM;AAAE,eAAO,KAAK,SAAS,MAAM,MAAM;AAAA,MAAG,CAAC;AACnE,UAAI,aAAa,WAAW,SAAS,IAAI,kBAAkB,OAAO,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU,QAAQ;AACtG,UAAI,YAAY;AACZ,YAAI,MAAM,YAAY;AAClB,gBAAM,eAAe;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,eAAqB,mBAAY,SAAU,MAAM,OAAO,QAAQ,QAAQ;AACxE,QAAI,QAAQ,EAAE,MAAY,OAAc,QAAgB,OAAe;AACvE,uBAAmB,QAAQ,KAAK,KAAK;AACrC,eAAW,WAAY;AACnB,yBAAmB,UAAU,mBAAmB,QAAQ,OAAO,SAAU,GAAG;AAAE,eAAO,MAAM;AAAA,MAAO,CAAC;AAAA,IACvG,GAAG,CAAC;AAAA,EACR,GAAG,CAAC,CAAC;AACL,MAAI,mBAAyB,mBAAY,SAAU,OAAO;AACtD,kBAAc,UAAU,WAAW,KAAK;AACxC,eAAW,UAAU;AAAA,EACzB,GAAG,CAAC,CAAC;AACL,MAAI,cAAoB,mBAAY,SAAU,OAAO;AACjD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,MAAI,kBAAwB,mBAAY,SAAU,OAAO;AACrD,iBAAa,MAAM,MAAM,WAAW,KAAK,GAAG,MAAM,QAAQ,kBAAkB,OAAO,MAAM,QAAQ,OAAO,CAAC;AAAA,EAC7G,GAAG,CAAC,CAAC;AACL,EAAM,iBAAU,WAAY;AACxB,cAAU,KAAK,KAAK;AACpB,UAAM,aAAa;AAAA,MACf,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACxB,CAAC;AACD,aAAS,iBAAiB,SAAS,eAAe,UAAU;AAC5D,aAAS,iBAAiB,aAAa,eAAe,UAAU;AAChE,aAAS,iBAAiB,cAAc,kBAAkB,UAAU;AACpE,WAAO,WAAY;AACf,kBAAY,UAAU,OAAO,SAAU,MAAM;AAAE,eAAO,SAAS;AAAA,MAAO,CAAC;AACvE,eAAS,oBAAoB,SAAS,eAAe,UAAU;AAC/D,eAAS,oBAAoB,aAAa,eAAe,UAAU;AACnE,eAAS,oBAAoB,cAAc,kBAAkB,UAAU;AAAA,IAC3E;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,kBAAkB,MAAM,iBAAiB,QAAQ,MAAM;AAC3D,SAAc;AAAA,IAAoB;AAAA,IAAU;AAAA,IACxC,QAAc,qBAAc,OAAO,EAAE,QAAQ,cAAc,EAAE,EAAE,CAAC,IAAI;AAAA,IACpE,kBAAwB,qBAAc,iBAAiB,EAAE,SAAS,SAAS,CAAC,IAAI;AAAA,EAAI;AAC5F;;;AG9IA,IAAO,kBAAQ,cAAc,WAAW,mBAAmB;;;ANC3D,IAAI,oBAA0B,kBAAW,SAAU,OAAO,KAAK;AAAE,SAAc,qBAAc,cAAc,SAAS,CAAC,GAAG,OAAO,EAAE,KAAU,SAAS,gBAAQ,CAAC,CAAC;AAAI,CAAC;AACnK,kBAAkB,aAAa,aAAa;AAC5C,IAAO,sBAAQ;;;AQiBf,IAAMC,oCAAc;AAGpB,IAAM,CAACC,2CAAqBC,yCAAtB,IAA2CC,yCAAmBH,iCAAD;AAcnE,IAAM,CAACI,sCAAgBC,sCAAjB,IAAqCJ,0CAAwCD,iCAArB;AAU9D,IAAMM,4CAAiCC,CAAAA,UAAoC;AACzE,QAAM,EAAA,eAAA,UAGJC,MAAMC,UAHF,aAAA,cAAA,QAMI,KAARC,IACEH;AACJ,QAAMI,iBAAaC,eAAAA,QAAgC,IAAhC;AACnB,QAAMC,iBAAaD,eAAAA,QAAmC,IAAnC;AACnB,QAAM,CAACJ,OAAO,OAAOM,OAAf,IAA0BC,yCAAqB;IACnDC,MAAMP;IACNQ,aAAaC;IACbC,UAAUC;GAHwC;AAMpD,aACE,eAAAC,eAAC,sCADH;IAEI,OAAOC;IACP;IACA;IACA,WAAWC,0CAAK;IAChB,SAASA,0CAAK;IACd,eAAeA,0CAAK;IACpB;IACA,cAAcT;IACd,kBAAcF,eAAAA;MAAkB,MAAME;QAASU,CAAAA,aAAa,CAACA;MAAhB;MAA2B;QAACV;;IAA3D;IACd;KAECW,QAZH;;AAiBJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,qCAAe;AAMrB,IAAMC,gDAAgBf,eAAAA,YACpB,CAACL,OAAwCqB,iBAAiB;AACxD,QAAM,EAAA,eAAiB,GAAGC,aAAH,IAAoBtB;AAC3C,QAAMuB,UAAUzB,uCAAiBqB,oCAAcJ,aAAf;AAChC,QAAMS,qBAAqBC,0CAAgBJ,cAAcE,QAAQnB,UAAvB;AAC1C,aACE,eAAAU,eAAC,0CAAU,QADb,SAAA;IAEI,MAAK;IACL,iBAAc;IACd,iBAAeS,QAAQtB;IACvB,iBAAesB,QAAQG;IACvB,cAAYC,+BAASJ,QAAQtB,IAAT;KAChBqB,cANN;IAOE,KAAKE;IACL,SAASI,0CAAqB5B,MAAM6B,SAASN,QAAQO,YAAxB;GAR/B,CAAA;CANgB;AAoBtB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,oCAAc;AAGpB,IAAM,CAACC,sCAAgBC,sCAAjB,IAAqCvC,0CAAwCqC,mCAAa;EAC9FG,YAAYC;CADgD;AAkB9D,IAAMC,4CAA6CpC,CAAAA,UAA0C;AAC3F,QAAM,EAAA,eAAA,YAAA,UAAA,UAAuCqC,IAAcrC;AAC3D,QAAMuB,UAAUzB,uCAAiBiC,mCAAahB,aAAd;AAChC,aACE,eAAAD,eAAC,sCADH;IACkB,OAAOC;IAAe;KACnCV,eAAAA,SAAeiC;IAAIpB;IAAWqB,CAAAA,cAC7B,eAAAzB,eAAC,2CAFL;MAEc,SAASoB,cAAcX,QAAQtB;WACvC,eAAAa,eAAC,2CADH;MACmB,SAAO;MAAC;OACtByB,KADH,CADF;EADD,CADH;;AAYJ,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMC,qCAAe;AAWrB,IAAMC,gDAAgBpC,eAAAA,YACpB,CAACL,OAAwCqB,iBAAiB;AACxD,QAAMqB,gBAAgBT,uCAAiBO,oCAAcxC,MAAMe,aAArB;AACtC,QAAM,EAAA,aAAe2B,cAAcR,YAAY,GAAGS,aAAH,IAAoB3C;AACnE,QAAMuB,UAAUzB,uCAAiB0C,oCAAcxC,MAAMe,aAArB;AAChC,SAAOQ,QAAQpB,YACb,eAAAW,eAAC,2CADH;IACY,SAASoB,cAAcX,QAAQtB;SACvC,eAAAa,eAAC,yCAAD,SAAA,CAAA,GAAuB6B,cADzB;IACuC,KAAKtB;GAA1C,CAAA,CADF,IAGE;CATc;AAatB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMuB,8CAAoBvC,eAAAA,YACxB,CAACL,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,eAAiB,GAAGsB,aAAH,IAAoB3C;AAC3C,QAAMuB,UAAUzB,uCAAiB0C,oCAAczB,aAAf;AAChC;;;QAGE,eAAAD,eAAC,qBAAD;MAAc,IAAI+B;MAAM,gBAAc;MAAC,QAAQ;QAACtB,QAAQjB;;WACtD,eAAAQ,eAAC,0CAAU,KADb,SAAA;MAEI,cAAYa,+BAASJ,QAAQtB,IAAT;OAChB0C,cAFN;MAGE,KAAKtB;MAEL,OAAO;QAAEyB,eAAe;QAAQ,GAAGH,aAAaI;;KALlD,CAAA,CADF;;CAPoB;AAwB1B,IAAMC,qCAAe;AAWrB,IAAMC,gDAAgB5C,eAAAA,YACpB,CAACL,OAAwCqB,iBAAiB;AACxD,QAAMqB,gBAAgBT,uCAAiBe,oCAAchD,MAAMe,aAArB;AACtC,QAAM,EAAA,aAAe2B,cAAcR,YAAY,GAAGgB,aAAH,IAAoBlD;AACnE,QAAMuB,UAAUzB,uCAAiBkD,oCAAchD,MAAMe,aAArB;AAChC,aACE,eAAAD,eAAC,2CADH;IACY,SAASoB,cAAcX,QAAQtB;KACtCsB,QAAQpB,YACP,eAAAW,eAAC,0CAAD,SAAA,CAAA,GAAwBoC,cAF5B;IAE0C,KAAK7B;GAA3C,CAAA,QAEA,eAAAP,eAAC,6CAAD,SAAA,CAAA,GAA2BoC,cAF3B;IAEyC,KAAK7B;GAA9C,CAAA,CAJJ;CANgB;AAiBtB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAQA,IAAM8B,+CAAqB9C,eAAAA,YACzB,CAACL,OAA4CqB,iBAAiB;AAC5D,QAAME,UAAUzB,uCAAiBkD,oCAAchD,MAAMe,aAArB;AAChC,QAAMT,iBAAaD,eAAAA,QAA6B,IAA7B;AACnB,QAAM+C,eAAe3B,0CAAgBJ,cAAcE,QAAQjB,YAAYA,UAAnC;AAGpCD,qBAAAA,WAAgB,MAAM;AACpB,UAAMgD,UAAU/C,WAAWgD;AAC3B,QAAID,QAAS,QAAOE,WAAWF,OAAD;KAC7B,CAAA,CAHH;AAKA,aACE,eAAAvC,eAAC,yCAAD,SAAA,CAAA,GACMd,OAFR;IAGI,KAAKoD;IAGL,WAAW7B,QAAQtB;IACnB,6BAA2B;IAC3B,kBAAkB2B,0CAAqB5B,MAAMwD,kBAAmBC,CAAAA,UAAU;AAAA,UAAA;AACxEA,YAAMC,eAAN;AACA,OAAA,wBAAAnC,QAAQnB,WAAWkD,aAAnB,QAAA,0BAAA,UAAA,sBAA4BK,MAA5B;KAFoC;IAItC,sBAAsB/B,0CAAqB5B,MAAM4D,sBAAuBH,CAAAA,UAAU;AAChF,YAAMI,gBAAgBJ,MAAMK,OAAOD;AACnC,YAAME,gBAAgBF,cAAcG,WAAW,KAAKH,cAAcI,YAAY;AAC9E,YAAMC,eAAeL,cAAcG,WAAW,KAAKD;AAInD,UAAIG,aAAcT,OAAMC,eAAN;KAPsB;IAW1C,gBAAgB9B;MAAqB5B,MAAMmE;MAAiBV,CAAAA,UAC1DA,MAAMC,eAAN;IADkC;GAtBtC,CAAA;CAbqB;AA6C3B,IAAMU,kDAAwB/D,eAAAA,YAC5B,CAACL,OAA4CqB,iBAAiB;AAC5D,QAAME,UAAUzB,uCAAiBkD,oCAAchD,MAAMe,aAArB;AAChC,QAAMsD,8BAA0BhE,eAAAA,QAAa,KAAb;AAChC,QAAMiE,+BAA2BjE,eAAAA,QAAa,KAAb;AAEjC,aACE,eAAAS,eAAC,yCAAD,SAAA,CAAA,GACMd,OAFR;IAGI,KAAKqB;IACL,WAAW;IACX,6BAA6B;IAC7B,kBAAmBoC,CAAAA,UAAU;AAAA,UAAA;AAC3B,OAAA,wBAAAzD,MAAMwD,sBAAN,QAAA,0BAAA,UAAA,sBAAA,KAAAxD,OAAyByD,KAApB;AAEL,UAAI,CAACA,MAAMc,kBAAkB;AAAA,YAAA;AAC3B,YAAI,CAACF,wBAAwBf,QAAS,EAAA,yBAAA/B,QAAQnB,WAAWkD,aAAnB,QAAA,2BAAA,UAAA,uBAA4BK,MAA5B;AAEtCF,cAAMC,eAAN;;AAGFW,8BAAwBf,UAAU;AAClCgB,+BAAyBhB,UAAU;;IAErC,mBAAoBG,CAAAA,UAAU;AAAA,UAAA,uBAAA;AAC5B,OAAA,wBAAAzD,MAAMwE,uBAAN,QAAA,0BAAA,UAAA,sBAAA,KAAAxE,OAA0ByD,KAArB;AAEL,UAAI,CAACA,MAAMc,kBAAkB;AAC3BF,gCAAwBf,UAAU;AAClC,YAAIG,MAAMK,OAAOD,cAAcY,SAAS,cACtCH,0BAAyBhB,UAAU;;AAOvC,YAAMoB,SAASjB,MAAMiB;AACrB,YAAMC,mBAAe,yBAAGpD,QAAQnB,WAAWkD,aAAtB,QAAA,2BAAA,SAAA,SAAG,uBAA4BsB,SAASF,MAArC;AACxB,UAAIC,gBAAiBlB,OAAMC,eAAN;AAMrB,UAAID,MAAMK,OAAOD,cAAcY,SAAS,aAAaH,yBAAyBhB,QAC5EG,OAAMC,eAAN;;GAvCN,CAAA;CAPwB;AAgF9B,IAAMmB,8CAAoBxE,eAAAA,YACxB,CAACL,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,eAAA,WAAA,iBAAA,kBAA+D,GAAG6B,aAAH,IAAoBlD;AACzF,QAAMuB,UAAUzB,uCAAiBkD,oCAAcjC,aAAf;AAChC,QAAMT,iBAAaD,eAAAA,QAA6B,IAA7B;AACnB,QAAM+C,eAAe3B,0CAAgBJ,cAAcf,UAAf;AAIpCwE,4CAAc;AAEd,aACE,eAAAhE,eAAA,eAAAiE,UAAA,UACE,eAAAjE,eAAC,2CAFL;IAGM,SAAO;IACP,MAAI;IACJ,SAASkE;IACT,kBAAkBC;IAClB,oBAAoBzB;SAEpB,eAAA1C,eAAC,2CAPH,SAAA;IAQI,MAAK;IACL,IAAIS,QAAQG;IACZ,oBAAkBH,QAAQ2D;IAC1B,mBAAiB3D,QAAQ4D;IACzB,cAAYxD,+BAASJ,QAAQtB,IAAT;KAChBiD,cANN;IAOE,KAAKE;IACL,WAAW,MAAM7B,QAAQV,aAAa,KAArB;GARnB,CAAA,CAPF,GAkBCuE,KAAA;CA/BiB;AA8C1B,IAAMC,mCAAa;AAMnB,IAAMC,gDAAcjF,eAAAA,YAClB,CAACL,OAAsCqB,iBAAiB;AACtD,QAAM,EAAA,eAAiB,GAAGkE,WAAH,IAAkBvF;AACzC,QAAMuB,UAAUzB,uCAAiBuF,kCAAYtE,aAAb;AAChC,aAAO,eAAAD,eAAC,0CAAU,IAAlB,SAAA;IAAqB,IAAIS,QAAQ4D;KAAaI,YAAvC;IAAmD,KAAKlE;GAAxD,CAAA;CAJS;AAQpB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMmE,yCAAmB;AAMzB,IAAMC,gDAAoBpF,eAAAA,YACxB,CAACL,OAA4CqB,iBAAiB;AAC5D,QAAM,EAAA,eAAiB,GAAGqE,iBAAH,IAAwB1F;AAC/C,QAAMuB,UAAUzB,uCAAiB0F,wCAAkBzE,aAAnB;AAChC,aAAO,eAAAD,eAAC,0CAAU,GAAlB,SAAA;IAAoB,IAAIS,QAAQ2D;KAAmBQ,kBAA5C;IAA8D,KAAKrE;GAAnE,CAAA;CAJe;AAQ1B,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAMA,IAAMsE,mCAAa;AAKnB,IAAMC,gDAAcvF,eAAAA,YAClB,CAACL,OAAsCqB,iBAAiB;AACtD,QAAM,EAAA,eAAiB,GAAGwE,WAAH,IAAkB7F;AACzC,QAAMuB,UAAUzB,uCAAiB6F,kCAAY5E,aAAb;AAChC,aACE,eAAAD,eAAC,0CAAU,QADb,SAAA;IAEI,MAAK;KACD+E,YAFN;IAGE,KAAKxE;IACL,SAASO;MAAqB5B,MAAM6B;MAAS,MAAMN,QAAQV,aAAa,KAArB;IAAtB;GAJ/B,CAAA;CALc;AAepB,OAAA,OAAA,2CAAA;EAAA,aAAA;CAAA;AAIA,SAASc,+BAAS1B,MAAe;AAC/B,SAAOA,OAAO,SAAS;;AAGzB,IAAM6F,2CAAqB;AAE3B,IAAM,CAACC,2CAAiBC,uCAAlB,IAAuCC,0CAAcH,0CAAoB;EAC7EI,aAAalD;EACbmD,WAAWd;EACXe,UAAU;CAH8C;AAkD1D,IAAMC,4CAAOC;AAEb,IAAMC,4CAASC;AACf,IAAMC,4CAAUC;AAChB,IAAMC,4CAAUC;;;ACviBmE,QAAgB;AAA8D,IAAI,IAAE;AAAN,IAAwBC,KAAE;AAA1B,IAAkD,KAAG;AAArD,IAA+EC,KAAE;AAAjF,IAAkG,KAAG,GAAGA,EAAC;AAAzG,IAAwI,IAAE;AAA1I,IAA6J,IAAE;AAA/J,IAA4K,KAAG,CAAC,GAAE,GAAE,MAAI,EAAG,GAAE,GAAE,CAAC;AAAhM,IAAkM,KAAK,gBAAc,MAAM;AAA3N,IAA6NC,KAAE,MAAM,aAAW,EAAE;AAAlP,IAAoP,KAAK,gBAAc,MAAM;AAA7Q,IAA+Q,IAAE,MAAM,aAAW,EAAE;AAApS,IAAsS,KAAK,gBAAc,MAAM;AAA/T,IAAiU,KAAK,aAAW,CAAC,GAAE,MAAI;AAAC,MAAI,IAAEC,GAAE,MAAI;AAAC,QAAI,GAAE;AAAE,WAAM,EAAC,QAAO,IAAG,QAAO,KAAG,IAAE,EAAE,UAAQ,OAAK,IAAE,EAAE,iBAAe,OAAK,IAAE,IAAG,UAAS,EAAC,OAAM,GAAE,OAAM,oBAAI,OAAI,QAAO,oBAAI,MAAG,EAAC;AAAA,EAAC,CAAC,GAAEC,KAAED,GAAE,MAAI,oBAAI,KAAG,GAAE,IAAEA,GAAE,MAAI,oBAAI,KAAG,GAAE,IAAEA,GAAE,MAAI,oBAAI,KAAG,GAAE,IAAEA,GAAE,MAAI,oBAAI,KAAG,GAAEE,KAAE,GAAG,CAAC,GAAE,EAAC,OAAM,GAAE,UAAS,GAAE,OAAM,GAAE,eAAc,GAAE,QAAO,GAAE,cAAa,GAAE,MAAK,GAAE,yBAAwB,KAAG,OAAG,aAAY,IAAE,MAAG,GAAGC,GAAC,IAAE,GAAE,KAAK,QAAM,GAAEC,KAAI,QAAM,GAAEC,KAAI,QAAM,GAAE,IAAI,SAAO,IAAI,GAAE,IAAE,GAAG;AAAE,IAAE,MAAI;AAAC,QAAG,MAAI,QAAO;AAAC,UAAI,IAAE,EAAE,KAAK;AAAE,QAAE,QAAQ,QAAM,GAAE,EAAE,KAAK;AAAA,IAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE,EAAE,MAAI;AAAC,MAAE,GAAE,EAAE;AAAA,EAAC,GAAE,CAAC,CAAC;AAAE,MAAI,IAAI,UAAQ,OAAK,EAAC,WAAU,QAAI,EAAE,QAAQ,IAAI,CAAC,GAAE,MAAI,EAAE,QAAQ,OAAO,CAAC,IAAG,UAAS,MAAI,EAAE,SAAQ,UAAS,CAAC,GAAE,GAAE,MAAI;AAAC,QAAI,GAAEC,IAAE;AAAE,QAAG,CAAC,OAAO,GAAG,EAAE,QAAQ,CAAC,GAAE,CAAC,GAAE;AAAC,UAAG,EAAE,QAAQ,CAAC,IAAE,GAAE,MAAI,SAAS,GAAE,GAAE,EAAE,GAAE,EAAE,GAAEC,EAAC;AAAA,eAAU,MAAI,YAAU,KAAG,EAAE,GAAE,EAAE,KAAI,IAAEL,GAAE,YAAU,OAAK,SAAO,EAAE,WAAS,SAAQ;AAAC,YAAI,IAAE,KAAG,OAAK,IAAE;AAAG,SAAC,KAAGI,KAAEJ,GAAE,SAAS,kBAAgB,QAAM,EAAE,KAAKI,IAAE,CAAC;AAAE;AAAA,MAAM;AAAC,QAAE,KAAK;AAAA,IAAC;AAAA,EAAC,GAAE,MAAK,MAAI;AAAC,MAAE,QAAQ,QAAQ,OAAG,EAAE,CAAC;AAAA,EAAC,EAAC,IAAG,CAAC,CAAC,GAAEE,KAAI,UAAQ,OAAK,EAAC,OAAM,CAAC,GAAE,GAAE,MAAI;AAAC,QAAI;AAAE,YAAM,IAAE,EAAE,QAAQ,IAAI,CAAC,MAAI,OAAK,SAAO,EAAE,WAAS,EAAE,QAAQ,IAAI,GAAE,EAAC,OAAM,GAAE,UAAS,EAAC,CAAC,GAAE,EAAE,QAAQ,SAAS,MAAM,IAAI,GAAE,GAAG,GAAE,CAAC,CAAC,GAAE,EAAE,GAAE,MAAI;AAAC,QAAE,GAAE,EAAE,KAAK;AAAA,IAAC,CAAC;AAAA,EAAE,GAAE,MAAK,CAAC,GAAE,OAAKP,GAAE,QAAQ,IAAI,CAAC,GAAE,MAAI,EAAE,QAAQ,IAAI,CAAC,IAAE,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,IAAE,EAAE,QAAQ,IAAI,GAAE,oBAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAG,EAAE,GAAE,MAAI;AAAC,MAAE,GAAE,EAAE,GAAE,EAAE,QAAQ,SAAOM,GAAE,GAAE,EAAE,KAAK;AAAA,EAAC,CAAC,GAAE,MAAI;AAAC,MAAE,QAAQ,OAAO,CAAC,GAAEN,GAAE,QAAQ,OAAO,CAAC,GAAE,EAAE,QAAQ,SAAS,MAAM,OAAO,CAAC;AAAE,QAAI,IAAE,EAAE;AAAE,MAAE,GAAE,MAAI;AAAC,QAAE,IAAG,KAAG,OAAK,SAAO,EAAE,aAAa,IAAI,OAAK,KAAGM,GAAE,GAAE,EAAE,KAAK;AAAA,IAAC,CAAC;AAAA,EAAC,IAAG,OAAM,QAAI,EAAE,QAAQ,IAAI,CAAC,KAAG,EAAE,QAAQ,IAAI,GAAE,oBAAI,KAAG,GAAE,MAAI;AAAC,MAAE,QAAQ,OAAO,CAAC,GAAE,EAAE,QAAQ,OAAO,CAAC;AAAA,EAAC,IAAG,QAAO,MAAIL,GAAE,QAAQ,cAAa,OAAM,KAAG,EAAE,YAAY,GAAE,yBAAwB,IAAG,QAAO,IAAG,SAAQG,IAAE,SAAQD,IAAE,cAAa,EAAC,IAAG,CAAC,CAAC;AAAE,WAAS,GAAG,GAAE,GAAE;AAAC,QAAI,GAAEE;AAAE,QAAI,KAAGA,MAAG,IAAEJ,GAAE,YAAU,OAAK,SAAO,EAAE,WAAS,OAAKI,KAAE;AAAG,WAAO,IAAE,EAAE,GAAE,EAAE,QAAQ,QAAO,CAAC,IAAE;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAG,CAAC,EAAE,QAAQ,UAAQJ,GAAE,QAAQ,iBAAe,MAAG;AAAO,QAAI,IAAE,EAAE,QAAQ,SAAS,OAAM,IAAE,CAAC;AAAE,MAAE,QAAQ,SAAS,OAAO,QAAQ,OAAG;AAAC,UAAII,KAAE,EAAE,QAAQ,IAAI,CAAC,GAAE,IAAE;AAAE,MAAAA,GAAE,QAAQ,OAAG;AAAC,YAAI,IAAE,EAAE,IAAI,CAAC;AAAE,YAAE,KAAK,IAAI,GAAE,CAAC;AAAA,MAAC,CAAC,GAAE,EAAE,KAAK,CAAC,GAAE,CAAC,CAAC;AAAA,IAAC,CAAC;AAAE,QAAI,IAAE,EAAE;AAAQ,MAAE,EAAE,KAAK,CAAC,GAAEA,OAAI;AAAC,UAAI,GAAE;AAAE,UAAI,IAAE,EAAE,aAAa,IAAI,GAAE,IAAEA,GAAE,aAAa,IAAI;AAAE,eAAQ,IAAE,EAAE,IAAI,CAAC,MAAI,OAAK,IAAE,OAAK,IAAE,EAAE,IAAI,CAAC,MAAI,OAAK,IAAE;AAAA,IAAE,CAAC,EAAE,QAAQ,OAAG;AAAC,UAAIA,KAAE,EAAE,QAAQT,EAAC;AAAE,MAAAS,KAAEA,GAAE,YAAY,EAAE,kBAAgBA,KAAE,IAAE,EAAE,QAAQ,GAAGT,EAAC,MAAM,CAAC,IAAE,EAAE,YAAY,EAAE,kBAAgB,IAAE,IAAE,EAAE,QAAQ,GAAGA,EAAC,MAAM,CAAC;AAAA,IAAC,CAAC,GAAE,EAAE,KAAK,CAAC,GAAES,OAAIA,GAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,OAAG;AAAC,UAAIA,KAAE,EAAE,QAAQ,cAAc,GAAG,CAAC,IAAI,CAAC,KAAK,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI;AAAE,MAAAA,MAAG,QAAMA,GAAE,cAAc,YAAYA,EAAC;AAAA,IAAC,CAAC;AAAA,EAAC;AAAC,WAASC,KAAG;AAAC,QAAI,IAAE,EAAE,EAAE,KAAK,OAAG,EAAE,aAAa,eAAe,MAAI,MAAM,GAAE,IAAE,KAAG,OAAK,SAAO,EAAE,aAAa,CAAC;AAAE,MAAE,SAAS,SAAQ,KAAG,MAAM;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAI,GAAE,GAAE,GAAED;AAAE,QAAG,CAAC,EAAE,QAAQ,UAAQJ,GAAE,QAAQ,iBAAe,OAAG;AAAC,QAAE,QAAQ,SAAS,QAAMD,GAAE,QAAQ;AAAK;AAAA,IAAM;AAAC,MAAE,QAAQ,SAAS,SAAO,oBAAI;AAAI,QAAI,IAAE;AAAE,aAAQ,KAAKA,GAAE,SAAQ;AAAC,UAAI,KAAG,KAAG,IAAE,EAAE,QAAQ,IAAI,CAAC,MAAI,OAAK,SAAO,EAAE,UAAQ,OAAK,IAAE,IAAG,KAAGK,MAAG,IAAE,EAAE,QAAQ,IAAI,CAAC,MAAI,OAAK,SAAO,EAAE,aAAW,OAAKA,KAAE,CAAC,GAAE,IAAE,GAAG,GAAE,CAAC;AAAE,QAAE,QAAQ,SAAS,MAAM,IAAI,GAAE,CAAC,GAAE,IAAE,KAAG;AAAA,IAAG;AAAC,aAAO,CAAC,GAAE,CAAC,KAAI,EAAE,QAAQ,UAAQ,KAAK,EAAE,KAAG,EAAE,QAAQ,SAAS,MAAM,IAAI,CAAC,IAAE,GAAE;AAAC,QAAE,QAAQ,SAAS,OAAO,IAAI,CAAC;AAAE;AAAA,IAAK;AAAC,MAAE,QAAQ,SAAS,QAAM;AAAA,EAAC;AAAC,WAAS,KAAI;AAAC,QAAI,GAAE,GAAE;AAAE,QAAI,IAAE,EAAE;AAAE,YAAM,IAAE,EAAE,kBAAgB,OAAK,SAAO,EAAE,gBAAc,OAAK,KAAG,IAAE,EAAE,QAAQ,CAAC,MAAI,OAAK,SAAO,EAAE,cAAc,EAAE,MAAI,QAAM,EAAE,eAAe,EAAC,OAAM,UAAS,CAAC,IAAG,EAAE,eAAe,EAAC,OAAM,UAAS,CAAC;AAAA,EAAE;AAAC,WAAS,IAAG;AAAC,QAAI;AAAE,YAAO,IAAE,EAAE,YAAU,OAAK,SAAO,EAAE,cAAc,GAAGR,EAAC,wBAAwB;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAI;AAAE,WAAO,MAAM,MAAM,IAAE,EAAE,YAAU,OAAK,SAAO,EAAE,iBAAiB,EAAE,CAAC;AAAA,EAAC;AAAC,WAASW,GAAE,GAAE;AAAC,QAAI,IAAE,EAAE,EAAE,CAAC;AAAE,SAAG,EAAE,SAAS,SAAQ,EAAE,aAAa,CAAC,CAAC;AAAA,EAAC;AAAC,WAASC,GAAE,GAAE;AAAC,QAAI;AAAE,QAAI,IAAE,EAAE,GAAE,IAAE,EAAE,GAAE,IAAE,EAAE,UAAU,OAAG,MAAI,CAAC,GAAEJ,KAAE,EAAE,IAAE,CAAC;AAAE,KAAC,IAAEJ,GAAE,YAAU,QAAM,EAAE,SAAOI,KAAE,IAAE,IAAE,IAAE,EAAE,EAAE,SAAO,CAAC,IAAE,IAAE,MAAI,EAAE,SAAO,EAAE,CAAC,IAAE,EAAE,IAAE,CAAC,IAAGA,MAAG,EAAE,SAAS,SAAQA,GAAE,aAAa,CAAC,CAAC;AAAA,EAAC;AAAC,WAAS,GAAG,GAAE;AAAC,QAAI,IAAE,EAAE,GAAE,IAAE,KAAG,OAAK,SAAO,EAAE,QAAQ,CAAC,GAAE;AAAE,WAAK,KAAG,CAAC,IAAG,KAAE,IAAE,IAAE,GAAG,GAAE,CAAC,IAAE,GAAG,GAAE,CAAC,GAAE,IAAE,KAAG,OAAK,SAAO,EAAE,cAAc,EAAE;AAAE,QAAE,EAAE,SAAS,SAAQ,EAAE,aAAa,CAAC,CAAC,IAAEI,GAAE,CAAC;AAAA,EAAC;AAAC,MAAI,KAAG,MAAID,GAAE,EAAE,EAAE,SAAO,CAAC,GAAE,KAAG,OAAG;AAAC,MAAE,eAAe,GAAE,EAAE,UAAQ,GAAG,IAAE,EAAE,SAAO,GAAG,CAAC,IAAEC,GAAE,CAAC;AAAA,EAAC,GAAE,KAAG,OAAG;AAAC,MAAE,eAAe,GAAE,EAAE,UAAQD,GAAE,CAAC,IAAE,EAAE,SAAO,GAAG,EAAE,IAAEC,GAAE,EAAE;AAAA,EAAC;AAAE,SAAS,gBAAc,0CAAE,KAAI,EAAC,KAAI,GAAE,UAAS,IAAG,GAAGP,IAAE,aAAY,IAAG,WAAU,OAAG;AAAC,QAAI;AAAE,SAAI,IAAEA,GAAE,cAAY,QAAM,EAAE,KAAKA,IAAE,CAAC,GAAE,CAAC,EAAE,iBAAiB,SAAO,EAAE,KAAI;AAAA,MAAC,KAAI;AAAA,MAAI,KAAI,KAAI;AAAC,aAAG,EAAE,WAAS,GAAG,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,aAAY;AAAC,WAAG,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI;AAAA,MAAI,KAAI,KAAI;AAAC,aAAG,EAAE,WAAS,GAAG,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,WAAU;AAAC,WAAG,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,QAAO;AAAC,UAAE,eAAe,GAAEM,GAAE,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI,OAAM;AAAC,UAAE,eAAe,GAAE,GAAG;AAAE;AAAA,MAAK;AAAA,MAAC,KAAI;AAAQ,YAAG,CAAC,EAAE,YAAY,eAAa,EAAE,YAAU,KAAI;AAAC,YAAE,eAAe;AAAE,cAAI,IAAE,EAAE;AAAE,cAAG,GAAE;AAAC,gBAAI,IAAE,IAAI,MAAM,CAAC;AAAE,cAAE,cAAc,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,IAAC;AAAA,EAAC,EAAC,GAAI,gBAAc,SAAQ,EAAC,cAAa,IAAG,SAAQD,GAAE,SAAQ,IAAGA,GAAE,SAAQ,OAAM,GAAE,GAAE,CAAC,GAAE,EAAE,GAAE,OAAK,gBAAc,GAAG,UAAS,EAAC,OAAM,EAAC,GAAI,gBAAc,GAAG,UAAS,EAAC,OAAMA,GAAC,GAAE,CAAC,CAAC,CAAC,CAAC;AAAC,CAAC;AAA7iK,IAA+iK,KAAK,aAAW,CAAC,GAAE,MAAI;AAAC,MAAIH,IAAE;AAAE,MAAI,IAAI,QAAM,GAAEJ,KAAI,SAAO,IAAI,GAAE,IAAI,aAAW,EAAE,GAAE,IAAEF,GAAE,GAAE,IAAE,GAAG,CAAC,GAAEG,MAAG,KAAGG,KAAE,EAAE,YAAU,OAAK,SAAOA,GAAE,eAAa,OAAK,IAAE,KAAG,OAAK,SAAO,EAAE;AAAW,IAAE,MAAI;AAAC,QAAG,CAACH,GAAE,QAAO,EAAE,KAAK,GAAE,KAAG,OAAK,SAAO,EAAE,EAAE;AAAA,EAAC,GAAE,CAACA,EAAC,CAAC;AAAE,MAAI,IAAE,GAAG,GAAED,IAAE,CAAC,EAAE,OAAM,EAAE,UAASA,EAAC,GAAE,EAAE,QAAQ,GAAE,IAAE,EAAE,GAAE,IAAEU,GAAE,OAAG,EAAE,SAAO,EAAE,UAAQ,EAAE,OAAO,GAAE,IAAEA,GAAE,OAAGT,MAAG,EAAE,OAAO,MAAI,QAAG,OAAG,EAAE,SAAO,EAAE,SAAS,MAAM,IAAI,CAAC,IAAE,IAAE,IAAE;AAAE,EAAE,YAAU,MAAI;AAAC,QAAI,IAAED,GAAE;AAAQ,QAAG,EAAE,CAAC,KAAG,EAAE,UAAU,QAAO,EAAE,iBAAiB,GAAE,CAAC,GAAE,MAAI,EAAE,oBAAoB,GAAE,CAAC;AAAA,EAAC,GAAE,CAAC,GAAE,EAAE,UAAS,EAAE,QAAQ,CAAC;AAAE,WAAS,IAAG;AAAC,QAAI,GAAE;AAAE,MAAE,IAAG,KAAG,IAAE,EAAE,SAAS,aAAW,QAAM,EAAE,KAAK,GAAE,EAAE,OAAO;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,MAAE,SAAS,SAAQ,EAAE,SAAQ,IAAE;AAAA,EAAC;AAAC,MAAG,CAAC,EAAE,QAAO;AAAK,MAAG,EAAC,UAAS,GAAE,OAAM,IAAG,UAAS,GAAE,YAAWE,IAAE,UAAS,IAAG,GAAGC,GAAC,IAAE;AAAE,SAAS,gBAAc,0CAAE,KAAI,EAAC,KAAI,EAAE,CAACH,IAAE,CAAC,CAAC,GAAE,GAAGG,IAAE,IAAG,GAAE,aAAY,IAAG,MAAK,UAAS,iBAAgB,CAAC,CAAC,GAAE,iBAAgB,CAAC,CAAC,GAAE,iBAAgB,CAAC,CAAC,GAAE,iBAAgB,CAAC,CAAC,GAAE,eAAc,KAAG,EAAE,0BAAwB,SAAO,GAAE,SAAQ,IAAE,SAAO,EAAC,GAAE,EAAE,QAAQ;AAAC,CAAC;AAAnhM,IAAqhM,KAAK,aAAW,CAAC,GAAE,MAAI;AAAC,MAAG,EAAC,SAAQ,GAAE,UAASH,IAAE,YAAW,GAAE,GAAG,EAAC,IAAE,GAAE,IAAI,QAAM,GAAEC,KAAI,SAAO,IAAI,GAAE,IAAI,SAAO,IAAI,GAAE,IAAI,QAAM,GAAE,IAAEH,GAAE,GAAE,IAAEY,GAAE,OAAG,KAAG,EAAE,OAAO,MAAI,QAAG,OAAG,EAAE,SAAO,EAAE,SAAS,OAAO,IAAI,CAAC,IAAE,IAAE;AAAE,IAAE,MAAI,EAAE,MAAM,CAAC,GAAE,CAAC,CAAC,GAAE,GAAG,GAAET,IAAE,CAAC,EAAE,OAAM,EAAE,SAAQ,CAAC,CAAC;AAAE,MAAI,IAAI,UAAQ,OAAK,EAAC,IAAG,GAAE,YAAW,EAAC,IAAG,CAAC,CAAC,CAAC;AAAE,SAAS,gBAAc,0CAAE,KAAI,EAAC,KAAI,EAAE,CAACA,IAAE,CAAC,CAAC,GAAE,GAAG,GAAE,cAAa,IAAG,MAAK,gBAAe,QAAO,IAAE,SAAO,KAAE,GAAE,KAAK,gBAAc,OAAM,EAAC,KAAI,GAAE,sBAAqB,IAAG,eAAc,MAAG,IAAG,EAAC,GAAE,CAAC,GAAE,EAAE,GAAE,OAAK,gBAAc,OAAM,EAAC,oBAAmB,IAAG,MAAK,SAAQ,mBAAkB,IAAE,IAAE,OAAM,GAAI,gBAAc,GAAG,UAAS,EAAC,OAAM,EAAC,GAAE,CAAC,CAAC,CAAC,CAAC;AAAC,CAAC;AAAroN,IAAuoN,KAAK,aAAW,CAAC,GAAE,MAAI;AAAC,MAAG,EAAC,cAAa,GAAE,GAAGD,GAAC,IAAE,GAAE,IAAI,SAAO,IAAI,GAAE,IAAEU,GAAE,OAAG,CAAC,EAAE,MAAM;AAAE,SAAM,CAAC,KAAG,CAAC,IAAE,OAAO,gBAAc,0CAAE,KAAI,EAAC,KAAI,EAAE,CAAC,GAAE,CAAC,CAAC,GAAE,GAAGV,IAAE,kBAAiB,IAAG,MAAK,YAAW,CAAC;AAAC,CAAC;AAA/zN,IAAi0N,KAAK,aAAW,CAAC,GAAE,MAAI;AAAC,MAAG,EAAC,eAAc,GAAE,GAAGA,GAAC,IAAE,GAAE,IAAE,EAAE,SAAO,MAAK,IAAE,EAAE,GAAE,IAAEU,GAAE,OAAG,EAAE,MAAM,GAAET,KAAES,GAAE,OAAG,EAAE,KAAK,GAAE,IAAEZ,GAAE,GAAE,IAAI,UAAQ,MAAI;AAAC,QAAI;AAAE,QAAI,KAAG,IAAE,EAAE,aAAa,YAAU,OAAK,SAAO,EAAE,cAAc,GAAGD,EAAC,IAAI,CAAC,KAAK,mBAAmBI,EAAC,CAAC,IAAI;AAAE,WAAO,KAAG,OAAK,SAAO,EAAE,aAAa,IAAI;AAAA,EAAC,GAAE,CAAC,CAAC;AAAE,SAAS,YAAU,MAAI;AAAC,MAAE,SAAO,QAAM,EAAE,SAAS,UAAS,EAAE,KAAK;AAAA,EAAC,GAAE,CAAC,EAAE,KAAK,CAAC,GAAI,gBAAc,0CAAE,OAAM,EAAC,KAAI,GAAE,GAAGD,IAAE,cAAa,IAAG,cAAa,OAAM,aAAY,OAAM,YAAW,OAAG,qBAAoB,QAAO,MAAK,YAAW,iBAAgB,MAAG,iBAAgB,EAAE,QAAO,mBAAkB,EAAE,SAAQ,yBAAwB,GAAE,IAAG,EAAE,SAAQ,MAAK,QAAO,OAAM,IAAE,EAAE,QAAM,GAAE,UAAS,OAAG;AAAC,SAAG,EAAE,SAAS,UAAS,EAAE,OAAO,KAAK,GAAE,KAAG,QAAM,EAAE,EAAE,OAAO,KAAK;AAAA,EAAC,EAAC,CAAC;AAAC,CAAC;AAAjiP,IAAmiP,KAAK,aAAW,CAAC,GAAE,MAAI;AAAC,MAAG,EAAC,UAAS,GAAE,OAAMA,KAAE,eAAc,GAAG,EAAC,IAAE,GAAE,IAAI,SAAO,IAAI,GAAE,IAAI,SAAO,IAAI,GAAEC,KAAEH,GAAE;AAAE,SAAS,YAAU,MAAI;AAAC,QAAG,EAAE,WAAS,EAAE,SAAQ;AAAC,UAAI,IAAE,EAAE,SAAQ,IAAE,EAAE,SAAQ,GAAE,IAAE,IAAI,eAAe,MAAI;AAAC,YAAE,sBAAsB,MAAI;AAAC,cAAI,IAAE,EAAE;AAAa,YAAE,MAAM,YAAY,sBAAqB,EAAE,QAAQ,CAAC,IAAE,IAAI;AAAA,QAAC,CAAC;AAAA,MAAC,CAAC;AAAE,aAAO,EAAE,QAAQ,CAAC,GAAE,MAAI;AAAC,6BAAqB,CAAC,GAAE,EAAE,UAAU,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC,GAAE,CAAC,CAAC,GAAI,gBAAc,0CAAE,KAAI,EAAC,KAAI,EAAE,CAAC,GAAE,CAAC,CAAC,GAAE,GAAG,GAAE,aAAY,IAAG,MAAK,WAAU,cAAaE,IAAE,IAAGC,GAAE,OAAM,GAAE,EAAE,GAAE,OAAK,gBAAc,OAAM,EAAC,KAAI,EAAE,CAAC,GAAEA,GAAE,YAAY,CAAC,GAAE,mBAAkB,GAAE,GAAE,CAAC,CAAC,CAAC;AAAC,CAAC;AAAxmQ,IAA0mQ,KAAK,aAAW,CAAC,GAAE,MAAI;AAAC,MAAG,EAAC,MAAK,GAAE,cAAaD,IAAE,kBAAiB,GAAE,kBAAiB,GAAE,WAAU,GAAE,GAAGC,GAAC,IAAE;AAAE,SAAS,gBAAgB,2CAAK,EAAC,MAAK,GAAE,cAAaD,GAAC,GAAI,gBAAgB,2CAAO,EAAC,WAAU,EAAC,GAAI,gBAAgB,2CAAQ,EAAC,gBAAe,IAAG,WAAU,EAAC,CAAC,GAAI,gBAAgB,2CAAQ,EAAC,cAAa,EAAE,OAAM,eAAc,IAAG,WAAU,EAAC,GAAI,gBAAc,IAAG,EAAC,KAAI,GAAE,GAAGC,GAAC,CAAC,CAAC,CAAC,CAAC;AAAC,CAAC;AAAh+Q,IAAk+Q,KAAK,aAAW,CAAC,GAAE,MAAIS,GAAE,CAAAV,OAAGA,GAAE,SAAS,UAAQ,CAAC,IAAI,gBAAc,0CAAE,KAAI,EAAC,KAAI,GAAE,GAAG,GAAE,cAAa,IAAG,MAAK,eAAc,CAAC,IAAE,IAAI;AAAhmR,IAAkmR,KAAK,aAAW,CAAC,GAAE,MAAI;AAAC,MAAG,EAAC,UAAS,GAAE,UAASA,IAAE,OAAM,IAAE,cAAa,GAAG,EAAC,IAAE;AAAE,SAAS,gBAAc,0CAAE,KAAI,EAAC,KAAI,GAAE,GAAG,GAAE,gBAAe,IAAG,MAAK,eAAc,iBAAgB,GAAE,iBAAgB,GAAE,iBAAgB,KAAI,cAAa,EAAC,GAAE,EAAE,GAAE,OAAK,gBAAc,OAAM,EAAC,eAAc,KAAE,GAAE,CAAC,CAAC,CAAC;AAAC,CAAC;AAA53R,IAA83R,KAAG,OAAO,OAAO,IAAG,EAAC,MAAK,IAAG,MAAK,IAAG,OAAM,IAAG,OAAM,IAAG,WAAU,IAAG,QAAO,IAAG,OAAM,IAAG,SAAQ,GAAE,CAAC;AAAE,SAAS,GAAG,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE;AAAmB,SAAK,KAAG;AAAC,QAAG,EAAE,QAAQ,CAAC,EAAE,QAAO;AAAE,QAAE,EAAE;AAAA,EAAkB;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE;AAAuB,SAAK,KAAG;AAAC,QAAG,EAAE,QAAQ,CAAC,EAAE,QAAO;AAAE,QAAE,EAAE;AAAA,EAAsB;AAAC;AAAC,SAAS,GAAG,GAAE;AAAC,MAAI,IAAI,SAAO,CAAC;AAAE,SAAO,EAAE,MAAI;AAAC,MAAE,UAAQ;AAAA,EAAC,CAAC,GAAE;AAAC;AAAC,IAAI,IAAE,OAAO,UAAQ,cAAc,cAAY;AAAgB,SAASD,GAAE,GAAE;AAAC,MAAI,IAAI,SAAO;AAAE,SAAO,EAAE,YAAU,WAAS,EAAE,UAAQ,EAAE,IAAG;AAAC;AAAC,SAAS,EAAE,GAAE;AAAC,SAAO,OAAG;AAAC,MAAE,QAAQ,OAAG;AAAC,aAAO,KAAG,aAAW,EAAE,CAAC,IAAE,KAAG,SAAO,EAAE,UAAQ;AAAA,IAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAASW,GAAE,GAAE;AAAC,MAAI,IAAE,EAAE,GAAE,IAAE,MAAI,EAAE,EAAE,SAAS,CAAC;AAAE,SAAS,uBAAqB,EAAE,WAAU,GAAE,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE,GAAEV,KAAE,CAAC,GAAE;AAAC,MAAI,IAAI,SAAO,GAAE,IAAEF,GAAE;AAAE,SAAO,EAAE,MAAI;AAAC,QAAI;AAAE,QAAI,KAAG,MAAI;AAAC,UAAI;AAAE,eAAQ,KAAK,GAAE;AAAC,YAAG,OAAO,KAAG,SAAS,QAAO,EAAE,KAAK;AAAE,YAAG,OAAO,KAAG,YAAU,aAAY,EAAE,QAAO,EAAE,WAAS,IAAE,EAAE,QAAQ,gBAAc,OAAK,SAAO,EAAE,KAAK,IAAE,EAAE;AAAA,MAAO;AAAA,IAAC,GAAG,GAAEG,KAAED,GAAE,IAAI,OAAG,EAAE,KAAK,CAAC;AAAE,MAAE,MAAM,GAAE,GAAEC,EAAC,IAAG,IAAE,EAAE,YAAU,QAAM,EAAE,aAAa,GAAE,CAAC,GAAE,EAAE,UAAQ;AAAA,EAAC,CAAC,GAAE;AAAC;AAAC,IAAI,KAAG,MAAI;AAAC,MAAG,CAAC,GAAE,CAAC,IAAI,WAAS,GAAE,IAAEF,GAAE,MAAI,oBAAI,KAAG;AAAE,SAAO,EAAE,MAAI;AAAC,MAAE,QAAQ,QAAQ,CAAAC,OAAGA,GAAE,CAAC,GAAE,EAAE,UAAQ,oBAAI;AAAA,EAAG,GAAE,CAAC,CAAC,CAAC,GAAE,CAACA,IAAE,MAAI;AAAC,MAAE,QAAQ,IAAIA,IAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,EAAC;AAAC;AAAE,SAAS,GAAG,GAAE;AAAC,MAAI,IAAE,EAAE;AAAK,SAAO,OAAO,KAAG,aAAW,EAAE,EAAE,KAAK,IAAE,YAAW,IAAE,EAAE,OAAO,EAAE,KAAK,IAAE;AAAC;AAAC,SAAS,EAAE,EAAC,SAAQ,GAAE,UAAS,EAAC,GAAE,GAAE;AAAC,SAAO,KAAK,iBAAe,CAAC,IAAI,eAAa,GAAG,CAAC,GAAE,EAAC,KAAI,EAAE,IAAG,GAAE,EAAE,EAAE,MAAM,QAAQ,CAAC,IAAE,EAAE,CAAC;AAAC;AAAC,IAAI,KAAG,EAAC,UAAS,YAAW,OAAM,OAAM,QAAO,OAAM,SAAQ,KAAI,QAAO,QAAO,UAAS,UAAS,MAAK,oBAAmB,YAAW,UAAS,aAAY,IAAG;", "names": ["T", "N", "M", "composeEventHandlers", "originalEventHandler", "ourEventHandler", "checkForDefaultPrevented", "handleEvent", "event", "defaultPrevented", "setRef", "ref", "value", "undefined", "current", "composeRefs", "refs", "node", "for<PERSON>ach", "useComposedRefs", "React", "createContext", "rootComponentName", "defaultContext", "Context", "React", "Provider", "props", "context", "value", "Object", "values", "$3bkAK$createElement", "children", "useContext", "consumerName", "undefined", "Error", "displayName", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "BaseContext", "index", "length", "scope", "createScope", "scopeContexts", "map", "useScope", "contexts", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "useComposedScopes", "overrideScopes", "nextScopes", "reduce", "scopeProps", "currentScope", "useLayoutEffect", "Boolean", "globalThis", "document", "React", "useReactId", "React", "toString", "undefined", "count", "useId", "deterministicId", "id", "setId", "useState", "useLayoutEffect", "reactId", "String", "useCallbackRef", "callback", "callback<PERSON><PERSON>", "React", "current", "args", "useControllableState", "onChange", "uncontrolledProp", "setUncontrolledProp", "useUncontrolledState", "isControlled", "prop", "undefined", "value", "handleChange", "useCallbackRef", "setValue", "React", "nextValue", "setter", "uncontrolledState", "defaultProp", "prevValueRef", "current", "Slot", "React", "props", "forwardedRef", "slotProps", "childrenA<PERSON>y", "toArray", "children", "slottable", "find", "isSlottable", "newElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "child", "count", "only", "$9IrjX$createElement", "undefined", "displayName", "SlotClone", "mergeProps", "ref", "composeRefs", "Slottable", "$9IrjX$Fragment", "type", "childProps", "overrideProps", "propName", "slotPropValue", "childPropV<PERSON>ue", "<PERSON><PERSON><PERSON><PERSON>", "test", "args", "filter", "Boolean", "join", "NODES", "Primitive", "reduce", "primitive", "node", "Node", "React", "props", "forwardedRef", "primitiveProps", "Comp", "<PERSON><PERSON><PERSON><PERSON>", "Slot", "window", "Symbol", "for", "$4q5Fq$createElement", "displayName", "dispatchDiscreteCustomEvent", "target", "event", "ReactDOM", "dispatchEvent", "useEscapeKeydown", "onEscapeKeyDownProp", "ownerDocument", "globalThis", "document", "onEscapeKeyDown", "useCallbackRef", "React", "handleKeyDown", "event", "key", "addEventListener", "removeEventListener", "DISMISSABLE_LAYER_NAME", "CONTEXT_UPDATE", "POINTER_DOWN_OUTSIDE", "FOCUS_OUTSIDE", "originalBodyPointerEvents", "DismissableLayerContext", "React", "layers", "Set", "layersWithOutsidePointerEventsDisabled", "branches", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "props", "forwardedRef", "layerProps", "context", "node", "setNode", "ownerDocument", "globalThis", "document", "force", "composedRefs", "useComposedRefs", "Array", "from", "highestLayerWithOutsidePointerEventsDisabled", "slice", "highestLayerWithOutsidePointerEventsDisabledIndex", "indexOf", "index", "isBodyPointerEventsDisabled", "size", "isPointerEventsEnabled", "pointerDownOutside", "usePointerDownOutside", "event", "target", "isPointerDownOnBranch", "some", "branch", "contains", "onPointerDownOutside", "onInteractOutside", "defaultPrevented", "on<PERSON><PERSON><PERSON>", "focusOutside", "useFocusOutside", "isFocusInBranch", "onFocusOutside", "useEscapeKeydown", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onEscapeKeyDown", "preventDefault", "disableOutsidePointerEvents", "body", "style", "pointerEvents", "add", "dispatchUpdate", "delete", "handleUpdate", "addEventListener", "removeEventListener", "$kqwpH$createElement", "undefined", "composeEventHandlers", "onFocusCapture", "onBlurCapture", "onPointerDownCapture", "BRANCH_NAME", "DismissableLayerBranch", "ref", "current", "handlePointerDownOutside", "useCallbackRef", "isPointerInsideReactTreeRef", "handleClickRef", "handlePointerDown", "handleAndDispatchPointerDownOutsideEvent", "handleAndDispatchCustomEvent", "eventDetail", "discrete", "originalEvent", "pointerType", "once", "timerId", "window", "setTimeout", "clearTimeout", "handleFocusOutside", "isFocusInsideReactTreeRef", "handleFocus", "CustomEvent", "dispatchEvent", "name", "handler", "detail", "bubbles", "cancelable", "dispatchDiscreteCustomEvent", "AUTOFOCUS_ON_MOUNT", "AUTOFOCUS_ON_UNMOUNT", "EVENT_OPTIONS", "bubbles", "cancelable", "FOCUS_SCOPE_NAME", "FocusScope", "React", "props", "forwardedRef", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "container", "<PERSON><PERSON><PERSON><PERSON>", "useCallbackRef", "lastFocusedElementRef", "composedRefs", "useComposedRefs", "node", "focusScope", "paused", "pause", "resume", "current", "trapped", "handleFocusIn", "event", "target", "contains", "focus", "select", "handleFocusOut", "relatedTarget", "handleMutations", "mutations", "focusedElement", "document", "activeElement", "body", "mutation", "removedNodes", "length", "addEventListener", "mutationObserver", "MutationObserver", "observe", "childList", "subtree", "removeEventListener", "disconnect", "focusScopesStack", "add", "previouslyFocusedElement", "hasFocusedCandidate", "mountEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "focusFirst", "removeLinks", "getTabbableCandidates", "setTimeout", "unmountEvent", "remove", "handleKeyDown", "loop", "isTabKey", "key", "altKey", "ctrl<PERSON>ey", "metaKey", "currentTarget", "first", "last", "getTabbableEdges", "hasTabbableElementsInside", "preventDefault", "shift<PERSON>ey", "$45QHv$createElement", "candidates", "candidate", "findVisible", "reverse", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "tagName", "type", "disabled", "hidden", "FILTER_SKIP", "tabIndex", "FILTER_ACCEPT", "nextNode", "push", "currentNode", "elements", "element", "isHidden", "upTo", "getComputedStyle", "visibility", "undefined", "display", "parentElement", "isSelectableInput", "HTMLInputElement", "preventScroll", "createFocusScopesStack", "stack", "activeFocusScope", "arrayRemove", "unshift", "array", "item", "updatedArray", "index", "indexOf", "splice", "items", "filter", "PORTAL_NAME", "Portal", "React", "props", "forwardedRef", "globalThis", "document", "body", "portalProps", "container", "ReactDOM", "createPortal", "$7SXl2$createElement", "useStateMachine", "initialState", "machine", "React", "state", "event", "nextState", "Presence", "props", "children", "presence", "usePresence", "present", "child", "isPresent", "only", "ref", "useComposedRefs", "forceMount", "displayName", "node", "setNode", "stylesRef", "prevPresentRef", "prevAnimationNameRef", "send", "mounted", "UNMOUNT", "ANIMATION_OUT", "unmountSuspended", "MOUNT", "ANIMATION_END", "unmounted", "currentAnimationName", "getAnimationName", "current", "useLayoutEffect", "styles", "wasPresent", "hasPresentChanged", "prevAnimationName", "display", "isAnimating", "handleAnimationEnd", "isCurrentAnimation", "includes", "animationName", "target", "ReactDOM", "handleAnimationStart", "addEventListener", "removeEventListener", "getComputedStyle", "count", "useFocusGuards", "React", "edgeGuards", "document", "querySelectorAll", "body", "insertAdjacentElement", "createFocusGuard", "count", "for<PERSON>ach", "node", "remove", "element", "createElement", "setAttribute", "tabIndex", "style", "cssText", "React", "React", "DIALOG_NAME", "createDialogContext", "createDialogScope", "createContextScope", "Dialog<PERSON><PERSON>", "useDialogContext", "Dialog", "props", "open", "openProp", "modal", "triggerRef", "React", "contentRef", "<PERSON><PERSON><PERSON>", "useControllableState", "prop", "defaultProp", "defaultOpen", "onChange", "onOpenChange", "$67UHm$createElement", "__scopeDialog", "useId", "prevOpen", "children", "TRIGGER_NAME", "DialogTrigger", "forwardedRef", "triggerProps", "context", "composedTriggerRef", "useComposedRefs", "contentId", "getState", "composeEventHandlers", "onClick", "onOpenToggle", "PORTAL_NAME", "PortalProvider", "usePortalContext", "forceMount", "undefined", "DialogPortal", "container", "map", "child", "OVERLAY_NAME", "DialogOverlay", "portalContext", "overlayProps", "DialogOverlayImpl", "Slot", "pointerEvents", "style", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contentProps", "DialogContentModal", "composedRefs", "content", "current", "hideOthers", "onCloseAutoFocus", "event", "preventDefault", "focus", "onPointerDownOutside", "originalEvent", "detail", "ctrlLeftClick", "button", "ctrl<PERSON>ey", "isRightClick", "onFocusOutside", "DialogContentNonModal", "hasInteractedOutsideRef", "hasPointerDownOutsideRef", "defaultPrevented", "onInteractOutside", "type", "target", "targetIsTrigger", "contains", "DialogContentImpl", "useFocusGuards", "$67UHm$Fragment", "trapFocus", "onOpenAutoFocus", "descriptionId", "titleId", "process", "TITLE_NAME", "DialogTitle", "titleProps", "DESCRIPTION_NAME", "DialogDescription", "descriptionProps", "CLOSE_NAME", "DialogClose", "closeProps", "TITLE_WARNING_NAME", "WarningProvider", "useWarningContext", "createContext", "contentName", "<PERSON><PERSON><PERSON>", "docs<PERSON>lug", "Root", "Dialog", "Portal", "DialogPortal", "Overlay", "DialogOverlay", "Content", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "X", "Y", "G", "k", "u", "p", "H", "$", "K", "m", "U", "B", "W", "J", "D"]}