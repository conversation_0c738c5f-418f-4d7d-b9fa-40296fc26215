
import React, { useState } from "react";
import { UserNote } from "@/types/conversation";
import { AnalyticsDashboard } from "./AnalyticsDashboard";
import { AIInsightsPanel } from "./AIInsightsPanel";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BarChart3, Brain } from "lucide-react";
import { useSettingsStore } from "@/stores/useSettingsStore"; // fixed: import from main settings

interface AnalyticsDashboardEnhancedProps {
  notes: UserNote[];
}

export const AnalyticsDashboardEnhanced: React.FC<AnalyticsDashboardEnhancedProps> = ({ notes }) => {
  const { settings } = useSettingsStore(); // fixed: use main settings
  const [activeTab, setActiveTab] = useState("analytics");
  
  const aiEnabled = !!settings.openRouterApiKey; // fixed: get api key from main settings

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">      <TabsList className="grid w-full grid-cols-2 bg-slate-800 p-1 rounded-lg h-auto text-slate-300 border border-slate-700">
        <TabsTrigger value="analytics" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex items-center gap-2 py-3 px-4 hover:text-white hover:bg-slate-700 transition-all duration-200 font-medium rounded-md">
          <BarChart3 className="h-4 w-4" />
          Analytics
        </TabsTrigger>
        <TabsTrigger value="ai-insights" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg flex items-center gap-2 py-3 px-4 hover:text-white hover:bg-slate-700 transition-all duration-200 font-medium rounded-md">
          <Brain className="h-4 w-4" />
          AI Insights
          {!aiEnabled && <span className="text-xs bg-orange-500 text-white px-2 py-1 rounded-full ml-1">Setup Required</span>}
        </TabsTrigger>
      </TabsList>

      <TabsContent value="analytics" className="mt-6">
        <AnalyticsDashboard notes={notes} />
      </TabsContent>

      <TabsContent value="ai-insights" className="mt-6">
        <AIInsightsPanel notes={notes} />
      </TabsContent>
    </Tabs>
  );
};
