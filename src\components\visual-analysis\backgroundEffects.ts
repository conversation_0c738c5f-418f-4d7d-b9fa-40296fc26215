import * as THREE from 'three';

export interface BackgroundEffectOptions {
  particleCount: number;
  particleSize: number;
  animationSpeed: number;
  opacity: number;
  color: THREE.ColorRepresentation;
}

export function createParticleField(options: BackgroundEffectOptions): THREE.Points {
  const geometry = new THREE.BufferGeometry();
  const positions = new Float32Array(options.particleCount * 3);
  const velocities = new Float32Array(options.particleCount * 3);

  // Spread particles across a large area
  for (let i = 0; i < options.particleCount; i++) {
    const i3 = i * 3;
    positions[i3] = (Math.random() - 0.5) * 100; // x
    positions[i3 + 1] = (Math.random() - 0.5) * 100; // y
    positions[i3 + 2] = -10; // z (behind main content)
    
    // Random slow velocities
    velocities[i3] = (Math.random() - 0.5) * options.animationSpeed;
    velocities[i3 + 1] = (Math.random() - 0.5) * options.animationSpeed;
    velocities[i3 + 2] = 0;
  }

  geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
  geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));

  const material = new THREE.PointsMaterial({
    color: options.color,
    size: options.particleSize,
    transparent: true,
    opacity: options.opacity,
    blending: THREE.AdditiveBlending,
  });

  const particles = new THREE.Points(geometry, material);
  return particles;
}

export function updateParticleField(particles: THREE.Points, deltaTime: number): void {
  const positions = particles.geometry.attributes.position;
  const velocities = particles.geometry.attributes.velocity;

  for (let i = 0; i < positions.count; i++) {
    const i3 = i * 3;
    
    // Update positions based on velocities
    positions.array[i3] += velocities.array[i3] * deltaTime;
    positions.array[i3 + 1] += velocities.array[i3 + 1] * deltaTime;
    
    // Wrap around screen edges
    if (positions.array[i3] > 50) positions.array[i3] = -50;
    if (positions.array[i3] < -50) positions.array[i3] = 50;
    if (positions.array[i3 + 1] > 50) positions.array[i3 + 1] = -50;
    if (positions.array[i3 + 1] < -50) positions.array[i3 + 1] = 50;
  }

  positions.needsUpdate = true;
}

export function createGradientBackground(
  scene: THREE.Scene, 
  camera: THREE.OrthographicCamera
): THREE.Mesh {
  // Create a large plane that fills the orthographic view
  const geometry = new THREE.PlaneGeometry(200, 200);
  
  // Create a gradient material using vertex colors or a shader
  const material = new THREE.ShaderMaterial({
    uniforms: {
      time: { value: 0 }
    },
    vertexShader: `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `,
    fragmentShader: `
      uniform float time;
      varying vec2 vUv;
      
      void main() {
        vec2 center = vec2(0.5);
        float dist = distance(vUv, center);
        
        // Create subtle animated gradient
        vec3 color1 = vec3(0.02, 0.02, 0.08); // Dark blue
        vec3 color2 = vec3(0.05, 0.02, 0.1);  // Dark purple
        vec3 color3 = vec3(0.01, 0.05, 0.08); // Dark teal
        
        float wave = sin(time * 0.5 + dist * 10.0) * 0.5 + 0.5;
        vec3 color = mix(color1, color2, vUv.y);
        color = mix(color, color3, wave * 0.3);
        
        gl_FragColor = vec4(color, 1.0);
      }
    `,
    transparent: false,
  });

  const backgroundMesh = new THREE.Mesh(geometry, material);
  backgroundMesh.position.z = -20; // Place behind everything
  scene.add(backgroundMesh);
  
  return backgroundMesh;
}

export function updateGradientBackground(backgroundMesh: THREE.Mesh, time: number): void {
  const material = backgroundMesh.material as THREE.ShaderMaterial;
  if (material.uniforms.time) {
    material.uniforms.time.value = time;
  }
}
