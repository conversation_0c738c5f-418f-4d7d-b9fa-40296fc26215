import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(() => ({
  server: {
    host: "localhost",
    port: 5173,
  },
  plugins: [
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'vendor-react': ['react', 'react-dom', 'react-router-dom'],
          'vendor-ui': [
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu',
            '@radix-ui/react-toast',
            '@radix-ui/react-tabs',
            '@radix-ui/react-select',
            '@radix-ui/react-accordion',
            '@radix-ui/react-popover',
            '@radix-ui/react-tooltip',
            '@radix-ui/react-scroll-area',
            '@radix-ui/react-separator',
            '@radix-ui/react-label',
            '@radix-ui/react-checkbox',
            '@radix-ui/react-radio-group',
            '@radix-ui/react-slider',
            '@radix-ui/react-switch',
            '@radix-ui/react-progress',
            '@radix-ui/react-avatar',
            '@radix-ui/react-alert-dialog',
            '@radix-ui/react-aspect-ratio',
            '@radix-ui/react-collapsible',
            '@radix-ui/react-context-menu',
            '@radix-ui/react-hover-card',
            '@radix-ui/react-menubar',
            '@radix-ui/react-navigation-menu',
            '@radix-ui/react-slot',
            '@radix-ui/react-toggle',
            '@radix-ui/react-toggle-group',
          ],
          'vendor-utils': [
            'clsx',
            'class-variance-authority',
            'tailwind-merge',
            'zod',
            'date-fns',
            'lucide-react',
          ],
          'vendor-data': [
            '@tanstack/react-query',
            'zustand',
            'react-hook-form',
            '@hookform/resolvers',
          ],
          'vendor-canvas': ['fabric'],
          'vendor-charts': ['recharts'],
          'vendor-misc': [
            'cmdk',
            'embla-carousel-react',
            'input-otp',
            'jspdf',
            'next-themes',
            'react-day-picker',
            'react-resizable-panels',
            'react-shepherd',
            'sonner',
            'tailwindcss-animate',
            'vaul',
          ],
          
          // Feature chunks
          'conversation-planner': [
            './src/components/ConversationPlanner.tsx',
            './src/components/conversation-planner/QuestionAnalyzer.tsx',
            './src/components/conversation-planner/AnalysisResults.tsx',
            './src/components/conversation-planner/ApiKeyManager.tsx',
            './src/components/conversation-planner/ModelSelector.tsx',
            './src/components/conversation-planner/StyleSelector.tsx',
            './src/components/conversation-planner/PlannerHeader.tsx',
          ],
          'chatbot-features': [
            './src/components/conversation-planner/ChatbotMode.tsx',
            './src/components/conversation-planner/ChatbotModeManager.tsx',
            './src/components/conversation-planner/chatbot/ChatHeader.tsx',
            './src/components/conversation-planner/chatbot/ChatMessages.tsx',
            './src/components/conversation-planner/chatbot/ChatInput.tsx',
            './src/components/conversation-planner/chatbot/ChatInfoBar.tsx',
            './src/components/conversation-planner/chatbot/ChatPerformanceChart.tsx',
            './src/components/conversation-planner/chatbot/useChat.ts',
          ],
          'historical-analysis': [
            './src/components/conversation-planner/HistoricalAnalysis.tsx',
            './src/components/conversation-planner/historical-analysis/AnalyticsDashboardEnhanced.tsx',
            './src/components/conversation-planner/historical-analysis/HistoricalAnalysisList.tsx',
            './src/components/conversation-planner/historical-analysis/HistoricalAnalysisControls.tsx',
            './src/components/conversation-planner/historical-analysis/AIInsightsPanel.tsx',
            './src/components/conversation-planner/historical-analysis/AIModelSettings.tsx',
            './src/components/conversation-planner/historical-analysis/HistoricalAnalysisApiManager.tsx',
          ],
          'canvas-visualization': [
            './src/components/conversation-planner/insights-canvas/VisualHistoricalAnalysisCanvas.tsx',
            './src/components/conversation-planner/insights-canvas/FabricCanvas.tsx',
            './src/components/conversation-planner/insights-canvas/PatternOverlayVisualization.tsx',
          ],
          'character-personas': [
            './src/components/conversation-planner/CharacterPersonaManager.tsx',
            './src/components/conversation-planner/CharacterPersonaModal.tsx',
            './src/components/conversation-planner/character-persona/CharacterPersonaLibrary.tsx',
            './src/components/conversation-planner/character-persona/CharacterPersonaForm.tsx',
          ],
          'services': [
            './src/services/aiCanvasService.ts',
            './src/services/aiHistoricalService.ts',
            './src/services/chatService.ts',
            './src/services/libraryService.ts',
            './src/services/openRouterService.ts',
            './src/services/visualAnalysisService.ts',
          ],
        },
      },
    },    chunkSizeWarningLimit: 1000, // Increase warning limit to 1MB
    sourcemap: false, // Disable sourcemaps in production to reduce size
    minify: true, // Use default esbuild minifier
  },
  // Add optimization hints for better caching
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'zustand',
      'lucide-react',
    ],
    exclude: ['fabric'], // Exclude heavy canvas library from pre-bundling
  },
}));
