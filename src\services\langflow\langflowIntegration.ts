import { AnalysisResult } from '@/types/conversation';
import { AnalysisCluster, ChatSimulation } from '@/components/visual-analysis/types';

/**
 * LangFlow workflow definition
 */
export interface LangFlowWorkflow {
  id: string;
  name: string;
  description: string;
  nodes: LangFlowNode[];
  edges: LangFlowEdge[];
  inputs: Record<string, any>;
  outputs: Record<string, any>;
}

/**
 * LangFlow node definition
 */
export interface LangFlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    label: string;
    component: string;
    parameters: Record<string, any>;
  };
}

/**
 * LangFlow edge definition
 */
export interface LangFlowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

/**
 * LangFlow execution result
 */
export interface LangFlowExecutionResult {
  success: boolean;
  output: any;
  error?: string;
  executionTime: number;
  nodeResults: Record<string, any>;
}

/**
 * LangFlow integration service for visual workflow creation and execution
 */
export class LangFlowIntegration {
  private baseUrl: string;
  private apiKey?: string;

  constructor(baseUrl: string = 'http://localhost:7860', apiKey?: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  /**
   * Create a workflow for analyzing chat analysis records
   */
  createAnalysisWorkflow(analysisResults: AnalysisResult[]): LangFlowWorkflow {
    return {
      id: `analysis_workflow_${Date.now()}`,
      name: 'Chat Analysis Workflow',
      description: 'Workflow for processing and analyzing chat analysis records',
      nodes: [
        {
          id: 'input_node',
          type: 'input',
          position: { x: 100, y: 100 },
          data: {
            label: 'Analysis Input',
            component: 'TextInput',
            parameters: {
              value: JSON.stringify(analysisResults),
              multiline: true
            }
          }
        },
        {
          id: 'llm_node',
          type: 'llm',
          position: { x: 300, y: 100 },
          data: {
            label: 'Analysis LLM',
            component: 'ChatOpenAI',
            parameters: {
              model_name: 'gpt-4',
              temperature: 0.7,
              max_tokens: 1000
            }
          }
        },
        {
          id: 'prompt_node',
          type: 'prompt',
          position: { x: 200, y: 200 },
          data: {
            label: 'Analysis Prompt',
            component: 'PromptTemplate',
            parameters: {
              template: `Analyze the following chat analysis records and provide insights:
              
              {analysis_data}
              
              Please provide:
              1. Key themes and patterns
              2. Quality assessment
              3. Recommendations for improvement
              4. Areas for further exploration`
            }
          }
        },
        {
          id: 'output_node',
          type: 'output',
          position: { x: 500, y: 100 },
          data: {
            label: 'Analysis Output',
            component: 'TextOutput',
            parameters: {}
          }
        }
      ],
      edges: [
        {
          id: 'input_to_prompt',
          source: 'input_node',
          target: 'prompt_node'
        },
        {
          id: 'prompt_to_llm',
          source: 'prompt_node',
          target: 'llm_node'
        },
        {
          id: 'llm_to_output',
          source: 'llm_node',
          target: 'output_node'
        }
      ],
      inputs: {
        analysis_data: analysisResults
      },
      outputs: {}
    };
  }

  /**
   * Create a workflow for cluster analysis
   */
  createClusterWorkflow(cluster: AnalysisCluster, analysisResults: AnalysisResult[]): LangFlowWorkflow {
    const clusterAnalyses = analysisResults.filter(result => 
      cluster.nodeIds.includes(result.id)
    );

    return {
      id: `cluster_workflow_${cluster.id}`,
      name: `Cluster Analysis: ${cluster.name}`,
      description: `Workflow for analyzing cluster: ${cluster.name}`,
      nodes: [
        {
          id: 'cluster_input',
          type: 'input',
          position: { x: 50, y: 100 },
          data: {
            label: 'Cluster Data',
            component: 'TextInput',
            parameters: {
              value: JSON.stringify({
                cluster: cluster,
                analyses: clusterAnalyses
              })
            }
          }
        },
        {
          id: 'cluster_prompt',
          type: 'prompt',
          position: { x: 200, y: 100 },
          data: {
            label: 'Cluster Analysis Prompt',
            component: 'PromptTemplate',
            parameters: {
              template: `Analyze this cluster of related chat analysis records:
              
              Cluster: {cluster_name}
              Description: {cluster_description}
              
              Analysis Records:
              {cluster_data}
              
              Provide:
              1. Cluster summary and themes
              2. Relationships between analyses
              3. Key insights and patterns
              4. Recommendations for next steps`
            }
          }
        },
        {
          id: 'cluster_llm',
          type: 'llm',
          position: { x: 400, y: 100 },
          data: {
            label: 'Cluster LLM',
            component: 'ChatOpenAI',
            parameters: {
              model_name: 'gpt-4',
              temperature: 0.5
            }
          }
        },
        {
          id: 'cluster_output',
          type: 'output',
          position: { x: 600, y: 100 },
          data: {
            label: 'Cluster Insights',
            component: 'TextOutput',
            parameters: {}
          }
        }
      ],
      edges: [
        {
          id: 'cluster_input_to_prompt',
          source: 'cluster_input',
          target: 'cluster_prompt'
        },
        {
          id: 'cluster_prompt_to_llm',
          source: 'cluster_prompt',
          target: 'cluster_llm'
        },
        {
          id: 'cluster_llm_to_output',
          source: 'cluster_llm',
          target: 'cluster_output'
        }
      ],
      inputs: {
        cluster_name: cluster.name,
        cluster_description: cluster.description,
        cluster_data: clusterAnalyses
      },
      outputs: {}
    };
  }

  /**
   * Create a workflow for simulation execution
   */
  createSimulationWorkflow(simulation: ChatSimulation): LangFlowWorkflow {
    const nodes: LangFlowNode[] = [
      {
        id: 'sim_input',
        type: 'input',
        position: { x: 50, y: 100 },
        data: {
          label: 'Simulation Input',
          component: 'TextInput',
          parameters: {
            value: JSON.stringify(simulation.prompts)
          }
        }
      }
    ];

    const edges: LangFlowEdge[] = [];

    // Create nodes for each prompt
    simulation.prompts.forEach((prompt, index) => {
      const promptNodeId = `prompt_${index}`;
      const llmNodeId = `llm_${index}`;
      const outputNodeId = `output_${index}`;

      nodes.push(
        {
          id: promptNodeId,
          type: 'prompt',
          position: { x: 200 + (index * 300), y: 100 },
          data: {
            label: `Prompt ${index + 1}`,
            component: 'PromptTemplate',
            parameters: {
              template: prompt.content
            }
          }
        },
        {
          id: llmNodeId,
          type: 'llm',
          position: { x: 200 + (index * 300), y: 200 },
          data: {
            label: `LLM ${index + 1}`,
            component: 'ChatOpenAI',
            parameters: {
              model_name: 'gpt-4',
              temperature: 0.7
            }
          }
        },
        {
          id: outputNodeId,
          type: 'output',
          position: { x: 200 + (index * 300), y: 300 },
          data: {
            label: `Output ${index + 1}`,
            component: 'TextOutput',
            parameters: {}
          }
        }
      );

      edges.push(
        {
          id: `input_to_prompt_${index}`,
          source: 'sim_input',
          target: promptNodeId
        },
        {
          id: `prompt_to_llm_${index}`,
          source: promptNodeId,
          target: llmNodeId
        },
        {
          id: `llm_to_output_${index}`,
          source: llmNodeId,
          target: outputNodeId
        }
      );
    });

    return {
      id: `simulation_workflow_${simulation.id}`,
      name: `Simulation: ${simulation.name}`,
      description: simulation.description,
      nodes,
      edges,
      inputs: {
        prompts: simulation.prompts
      },
      outputs: {}
    };
  }

  /**
   * Execute a workflow via LangFlow API
   */
  async executeWorkflow(workflow: LangFlowWorkflow): Promise<LangFlowExecutionResult> {
    const startTime = Date.now();

    try {
      const response = await fetch(`${this.baseUrl}/api/v1/run/${workflow.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
        },
        body: JSON.stringify({
          input_value: workflow.inputs,
          output_type: 'chat',
          input_type: 'chat'
        })
      });

      if (!response.ok) {
        throw new Error(`LangFlow API error: ${response.statusText}`);
      }

      const result = await response.json();
      const executionTime = Date.now() - startTime;

      return {
        success: true,
        output: result.outputs,
        executionTime,
        nodeResults: result.node_results || {}
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        output: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime,
        nodeResults: {}
      };
    }
  }

  /**
   * Save workflow to LangFlow
   */
  async saveWorkflow(workflow: LangFlowWorkflow): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/flows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
        },
        body: JSON.stringify({
          name: workflow.name,
          description: workflow.description,
          data: {
            nodes: workflow.nodes,
            edges: workflow.edges
          }
        })
      });

      return response.ok;
    } catch (error) {
      console.error('Error saving workflow to LangFlow:', error);
      return false;
    }
  }

  /**
   * Load workflow from LangFlow
   */
  async loadWorkflow(workflowId: string): Promise<LangFlowWorkflow | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/flows/${workflowId}`, {
        headers: {
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
        }
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      
      return {
        id: data.id,
        name: data.name,
        description: data.description,
        nodes: data.data.nodes,
        edges: data.data.edges,
        inputs: {},
        outputs: {}
      };
    } catch (error) {
      console.error('Error loading workflow from LangFlow:', error);
      return null;
    }
  }

  /**
   * Get available LangFlow components
   */
  async getAvailableComponents(): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/components`, {
        headers: {
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
        }
      });

      if (!response.ok) {
        return [];
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching LangFlow components:', error);
      return [];
    }
  }

  /**
   * Check LangFlow server status
   */
  async checkStatus(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Update configuration
   */
  updateConfig(baseUrl: string, apiKey?: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }
}
