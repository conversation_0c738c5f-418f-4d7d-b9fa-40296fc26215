/**
 * Design System Components
 * 
 * Centralized export of all design system components.
 * These components use the unified design tokens and variant system.
 */

// Core components (only export what exists)
export { Button, ButtonGroup, IconButton, FAB } from './Button';
export {
  Card,
  CardHeader,
  CardContent,
  CardFooter,
  StatsCard,
  FeatureCard,
  TestimonialCard
} from './Card';

// Re-export existing UI components with design system enhancements
export { Button as DSButton } from './Button';
export { Card as DSCard } from './Card';

// Types
export type {
  ButtonProps,
  CardProps,
} from './Button';

export type {
  CardProps as DSCardProps,
  CardHeaderProps,
} from './Card';

// Note: Additional components will be added as they are implemented
// This ensures the build doesn't fail on missing components
