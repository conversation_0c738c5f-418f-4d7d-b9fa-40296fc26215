import React, { useState, useCallback } from 'react';
import { SimpleNode, ConnectionData } from './types';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Slider } from '../ui/slider';
import { Badge } from '../ui/badge';
import { X, Link, Save, Trash2 } from 'lucide-react';

interface NodeConnectionManagerProps {
  sourceNode: SimpleNode;
  targetNode: SimpleNode;
  position: { x: number; y: number };
  onSave: (connectionData: ConnectionData) => void;
  onCancel: () => void;
  existingConnection?: ConnectionData;
}

export const NodeConnectionManager: React.FC<NodeConnectionManagerProps> = ({
  sourceNode,
  targetNode,
  position,
  onSave,
  onCancel,
  existingConnection
}) => {
  const [label, setLabel] = useState(existingConnection?.label || '');
  const [strength, setStrength] = useState(existingConnection?.strength || 1.0);
  const [tags, setTags] = useState<string[]>(existingConnection?.tags || []);
  const [newTag, setNewTag] = useState('');

  const handleAddTag = useCallback(() => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags(prev => [...prev, newTag.trim()]);
      setNewTag('');
    }
  }, [newTag, tags]);

  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  }, []);

  const handleSave = useCallback(() => {
    const connectionData: ConnectionData = {
      id: existingConnection?.id || `conn_${sourceNode.id}_${targetNode.id}_${Date.now()}`,
      source: sourceNode.id,
      target: targetNode.id,
      strength,
      label: label.trim() || undefined,
      tags: tags.length > 0 ? tags : undefined
    };

    onSave(connectionData);
  }, [sourceNode.id, targetNode.id, strength, label, tags, existingConnection?.id, onSave]);

  const generateSuggestedLabels = useCallback(() => {
    const suggestions: string[] = [];
    
    // Generate suggestions based on node types and analysis records
    if (sourceNode.data.analysisRecord && targetNode.data.analysisRecord) {
      const sourceRecord = sourceNode.data.analysisRecord;
      const targetRecord = targetNode.data.analysisRecord;
      
      // Same analysis type
      if (sourceRecord.analysisType === targetRecord.analysisType) {
        suggestions.push(`Similar ${sourceRecord.analysisType} analysis`);
      }
      
      // Same style
      if (sourceRecord.style === targetRecord.style) {
        suggestions.push(`Same ${sourceRecord.style} style`);
      }
      
      // Temporal relationship
      const sourceDate = new Date(sourceRecord.timestamp);
      const targetDate = new Date(targetRecord.timestamp);
      if (sourceDate < targetDate) {
        suggestions.push('Chronological sequence');
      }
      
      // Character relationship
      if (sourceRecord.characterPersona && targetRecord.characterPersona) {
        if (sourceRecord.characterPersona.id === targetRecord.characterPersona.id) {
          suggestions.push('Same character persona');
        } else {
          suggestions.push('Character comparison');
        }
      }
    }
    
    // Generic suggestions
    suggestions.push('Related analysis', 'Follow-up', 'Comparison', 'Refinement', 'Alternative approach');
    
    return suggestions.slice(0, 5); // Limit to 5 suggestions
  }, [sourceNode, targetNode]);

  const modalStyle: React.CSSProperties = {
    position: 'absolute',
    left: Math.min(position.x, window.innerWidth - 400),
    top: Math.min(position.y, window.innerHeight - 500),
    zIndex: 25,
    width: '380px',
    backgroundColor: 'rgba(20, 20, 40, 0.98)',
    backdropFilter: 'blur(15px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '12px',
    boxShadow: '0 12px 48px rgba(0, 0, 0, 0.6)',
  };

  return (
    <Card style={modalStyle}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <CardTitle className="text-white text-base flex items-center">
            <Link className="w-5 h-5 mr-2" />
            {existingConnection ? 'Edit Connection' : 'Create Connection'}
          </CardTitle>
          <button 
            onClick={onCancel}
            className="text-gray-400 hover:text-white text-lg leading-none"
            style={{ background: 'none', border: 'none', cursor: 'pointer' }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {/* Connection Preview */}
        <div className="mt-3 p-3 bg-white/5 rounded-lg">
          <div className="text-xs text-gray-300 space-y-1">
            <div className="flex items-center">
              <span className="text-blue-300">From:</span>
              <span className="ml-2 truncate">{sourceNode.data.label || sourceNode.id}</span>
            </div>
            <div className="flex items-center">
              <span className="text-green-300">To:</span>
              <span className="ml-2 truncate">{targetNode.data.label || targetNode.id}</span>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Connection Label */}
        <div>
          <Label htmlFor="connection-label" className="text-white text-sm">
            Connection Label
          </Label>
          <Input
            id="connection-label"
            value={label}
            onChange={(e) => setLabel(e.target.value)}
            placeholder="Describe the relationship..."
            className="mt-1 bg-white/10 border-white/20 text-white placeholder-gray-400"
          />
          
          {/* Suggested Labels */}
          <div className="mt-2">
            <div className="text-xs text-gray-400 mb-1">Suggestions:</div>
            <div className="flex flex-wrap gap-1">
              {generateSuggestedLabels().map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => setLabel(suggestion)}
                  className="text-xs px-2 py-1 bg-white/10 hover:bg-white/20 rounded text-gray-300 hover:text-white transition-colors"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Connection Strength */}
        <div>
          <Label className="text-white text-sm">
            Connection Strength: {strength.toFixed(1)}
          </Label>
          <div className="mt-2">
            <Slider
              value={[strength]}
              onValueChange={(value) => setStrength(value[0])}
              min={0.1}
              max={2.0}
              step={0.1}
              className="w-full"
            />
          </div>
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>Weak (0.1)</span>
            <span>Strong (2.0)</span>
          </div>
        </div>

        {/* Tags */}
        <div>
          <Label className="text-white text-sm">Tags</Label>
          <div className="flex gap-2 mt-1">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add tag..."
              className="flex-1 bg-white/10 border-white/20 text-white placeholder-gray-400"
              onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
            />
            <Button
              onClick={handleAddTag}
              size="sm"
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10"
            >
              Add
            </Button>
          </div>
          
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {tags.map((tag, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs bg-white/10 text-white hover:bg-white/20 cursor-pointer"
                  onClick={() => handleRemoveTag(tag)}
                >
                  {tag} <X className="w-3 h-3 ml-1" />
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button
            onClick={handleSave}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save className="w-4 h-4 mr-2" />
            {existingConnection ? 'Update' : 'Create'} Connection
          </Button>
          
          {existingConnection && (
            <Button
              onClick={() => {
                if (confirm('Are you sure you want to delete this connection?')) {
                  // TODO: Implement delete functionality
                  onCancel();
                }
              }}
              variant="destructive"
              size="sm"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
