
/**
 * Settings Store - Refactored with Standardized Pattern
 *
 * Manages application settings with consistent error handling,
 * loading states, and validation.
 */

import { ConversationSettings } from "@/types/conversation";
import { createStandardStore, BaseStore } from "./base/createStandardStore";
import { useErrorStore, createValidationError } from "./useErrorStore";

// Settings-specific state
interface SettingsData {
  settings: ConversationSettings;
}

// Settings-specific actions
interface SettingsActions {
  updateSettings: (updates: Partial<ConversationSettings>) => Promise<void>;
  clearApiKey: () => Promise<void>;
  resetSettings: () => void;
  validateSettings: () => boolean;
  exportSettings: () => string;
  importSettings: (settingsJson: string) => Promise<boolean>;
}

// Combined settings store interface
interface SettingsStore extends BaseStore, SettingsData, SettingsActions {}

// Default settings
const defaultSettings: ConversationSettings = {
  style: "professional",
  openRouterApiKey: "",
  selectedModel: "gpt-4.1-2025-04-14",
  numberOfAnswers: 3,
  analysisType: "multiple",
  selectedEmotions: ['Joy', 'Trust', 'Fear', 'Surprise', 'Sadness']
};

// Settings validation
const validateConversationSettings = (settings: Partial<ConversationSettings>): string[] => {
  const errors: string[] = [];

  if (settings.numberOfAnswers && (settings.numberOfAnswers < 1 || settings.numberOfAnswers > 10)) {
    errors.push("Number of answers must be between 1 and 10");
  }

  if (settings.selectedEmotions && settings.selectedEmotions.length === 0) {
    errors.push("At least one emotion must be selected");
  }

  if (settings.openRouterApiKey && settings.openRouterApiKey.length > 0 && settings.openRouterApiKey.length < 10) {
    errors.push("API key appears to be invalid (too short)");
  }

  return errors;
};

// Create the settings store using the standardized pattern
export const useSettingsStore = createStandardStore<SettingsStore>(
  {
    name: "conversation_planner_settings",
    version: "2.0.0",
    persist: true,
    persistConfig: {
      partialize: (state) => ({
        settings: state.settings,
        version: state.version,
      }),
    },
    initialState: {
      settings: defaultSettings,
    },
  },
  (set, get, api, errorHandler, loadingHandler) => ({
    updateSettings: async (updates: Partial<ConversationSettings>) => {
      return loadingHandler.withLoading(async () => {
        // Validate the updates
        const validationErrors = validateConversationSettings(updates);
        if (validationErrors.length > 0) {
          throw new Error(`Settings validation failed: ${validationErrors.join(', ')}`);
        }

        // Apply the updates
        set((state) => {
          state.settings = { ...state.settings, ...updates } as ConversationSettings;
          state.lastUpdated = Date.now();
        });

        // Simulate async operation (e.g., syncing with server)
        await new Promise(resolve => setTimeout(resolve, 100));
      }, 'Update Settings');
    },

    clearApiKey: async () => {
      return loadingHandler.withLoading(async () => {
        set((state) => {
          state.settings.openRouterApiKey = "";
          state.lastUpdated = Date.now();
        });

        // Simulate async operation
        await new Promise(resolve => setTimeout(resolve, 50));
      }, 'Clear API Key');
    },

    resetSettings: () => {
      set((state) => {
        state.settings = { ...defaultSettings };
        state.lastUpdated = Date.now();
        state.error = null;
      });
    },

    validateSettings: () => {
      const { settings } = get();
      const errors = validateConversationSettings(settings);

      if (errors.length > 0) {
        errorHandler.handleError(
          new Error(errors.join(', ')),
          'Settings Validation'
        );
        return false;
      }

      return true;
    },

    exportSettings: () => {
      try {
        const { settings } = get();
        return JSON.stringify(settings, null, 2);
      } catch (error) {
        errorHandler.handleError(error, 'Export Settings');
        return '';
      }
    },

    importSettings: async (settingsJson: string) => {
      return loadingHandler.withLoading(async () => {
        try {
          const importedSettings = JSON.parse(settingsJson) as ConversationSettings;

          // Validate imported settings
          const validationErrors = validateConversationSettings(importedSettings);
          if (validationErrors.length > 0) {
            throw new Error(`Invalid settings: ${validationErrors.join(', ')}`);
          }

          // Apply imported settings
          set((state) => {
            state.settings = { ...defaultSettings, ...importedSettings };
            state.lastUpdated = Date.now();
          });

          return true;
        } catch (error) {
          throw new Error(`Failed to import settings: ${error instanceof Error ? error.message : 'Invalid JSON'}`);
        }
      }, 'Import Settings') !== null;
    },
  })
);
