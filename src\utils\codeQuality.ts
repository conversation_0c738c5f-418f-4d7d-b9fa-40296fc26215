/**
 * Code Quality Utilities
 * 
 * Utilities for maintaining code quality, consistency, and best practices
 * across the application. Includes validation, formatting, and analysis tools.
 */

// Code quality metrics
export interface CodeQualityMetrics {
  complexity: number;
  maintainability: number;
  testCoverage: number;
  duplicateCode: number;
  codeSmells: string[];
  technicalDebt: number;
}

// Component analysis
export interface ComponentAnalysis {
  name: string;
  linesOfCode: number;
  complexity: number;
  dependencies: string[];
  props: PropAnalysis[];
  hooks: string[];
  issues: CodeIssue[];
}

export interface PropAnalysis {
  name: string;
  type: string;
  required: boolean;
  defaultValue?: any;
  description?: string;
}

export interface CodeIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  line?: number;
  column?: number;
  rule?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Naming conventions
export const NamingConventions = {
  // Component names should be PascalCase
  isValidComponentName: (name: string): boolean => {
    return /^[A-Z][a-zA-Z0-9]*$/.test(name);
  },

  // Hook names should start with 'use' and be camelCase
  isValidHookName: (name: string): boolean => {
    return /^use[A-Z][a-zA-Z0-9]*$/.test(name);
  },

  // Variable names should be camelCase
  isValidVariableName: (name: string): boolean => {
    return /^[a-z][a-zA-Z0-9]*$/.test(name);
  },

  // Constant names should be SCREAMING_SNAKE_CASE
  isValidConstantName: (name: string): boolean => {
    return /^[A-Z][A-Z0-9_]*$/.test(name);
  },

  // File names should be kebab-case or PascalCase for components
  isValidFileName: (name: string, isComponent: boolean = false): boolean => {
    if (isComponent) {
      return /^[A-Z][a-zA-Z0-9]*\.(tsx?|jsx?)$/.test(name);
    }
    return /^[a-z][a-z0-9-]*\.(ts|js|tsx|jsx|css|scss|json)$/.test(name);
  },

  // CSS class names should be kebab-case
  isValidCSSClassName: (name: string): boolean => {
    return /^[a-z][a-z0-9-]*$/.test(name);
  },
};

// Code complexity analysis
export class ComplexityAnalyzer {
  static calculateCyclomaticComplexity(code: string): number {
    // Simplified complexity calculation
    const complexityKeywords = [
      'if', 'else', 'while', 'for', 'switch', 'case', 'catch',
      '&&', '||', '?', ':', 'forEach', 'map', 'filter', 'reduce'
    ];

    let complexity = 1; // Base complexity

    complexityKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = code.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    });

    return complexity;
  }

  static calculateCognitiveComplexity(code: string): number {
    // Simplified cognitive complexity calculation
    let complexity = 0;
    let nestingLevel = 0;

    const lines = code.split('\n');
    
    lines.forEach(line => {
      const trimmed = line.trim();
      
      // Increase nesting for blocks
      if (trimmed.includes('{')) {
        nestingLevel++;
      }
      
      // Decrease nesting for closing blocks
      if (trimmed.includes('}')) {
        nestingLevel = Math.max(0, nestingLevel - 1);
      }
      
      // Add complexity for control structures
      if (/\b(if|while|for|switch)\b/.test(trimmed)) {
        complexity += 1 + nestingLevel;
      }
      
      // Add complexity for logical operators
      const logicalOps = (trimmed.match(/&&|\|\|/g) || []).length;
      complexity += logicalOps;
    });

    return complexity;
  }

  static analyzeMaintainability(code: string): number {
    const linesOfCode = code.split('\n').filter(line => line.trim()).length;
    const cyclomaticComplexity = this.calculateCyclomaticComplexity(code);
    const cognitiveComplexity = this.calculateCognitiveComplexity(code);
    
    // Simplified maintainability index calculation
    const maintainabilityIndex = Math.max(0, 
      100 - (cyclomaticComplexity * 2) - (cognitiveComplexity * 1.5) - (linesOfCode * 0.1)
    );
    
    return Math.round(maintainabilityIndex);
  }
}

// Code smell detection
export class CodeSmellDetector {
  static detectSmells(code: string): string[] {
    const smells: string[] = [];

    // Long method detection
    const linesOfCode = code.split('\n').filter(line => line.trim()).length;
    if (linesOfCode > 50) {
      smells.push('Long method: Consider breaking down into smaller functions');
    }

    // Too many parameters
    const functionMatches = code.match(/function\s+\w+\s*\([^)]*\)/g) || [];
    functionMatches.forEach(func => {
      const paramCount = (func.match(/,/g) || []).length + 1;
      if (paramCount > 5) {
        smells.push('Too many parameters: Consider using an options object');
      }
    });

    // Duplicate code detection (simplified)
    const lines = code.split('\n').map(line => line.trim()).filter(line => line);
    const duplicateLines = lines.filter((line, index) => 
      lines.indexOf(line) !== index && line.length > 10
    );
    if (duplicateLines.length > 0) {
      smells.push('Duplicate code detected: Consider extracting common functionality');
    }

    // Magic numbers
    const magicNumbers = code.match(/\b\d{2,}\b/g) || [];
    if (magicNumbers.length > 0) {
      smells.push('Magic numbers detected: Consider using named constants');
    }

    // Deep nesting
    const maxNesting = this.calculateMaxNesting(code);
    if (maxNesting > 4) {
      smells.push('Deep nesting detected: Consider extracting methods or using early returns');
    }

    // Large class/component
    const classMatches = code.match(/class\s+\w+|function\s+\w+|const\s+\w+\s*=\s*\(/g) || [];
    if (classMatches.length > 10) {
      smells.push('Large component: Consider breaking down into smaller components');
    }

    return smells;
  }

  private static calculateMaxNesting(code: string): number {
    let maxNesting = 0;
    let currentNesting = 0;

    for (const char of code) {
      if (char === '{') {
        currentNesting++;
        maxNesting = Math.max(maxNesting, currentNesting);
      } else if (char === '}') {
        currentNesting = Math.max(0, currentNesting - 1);
      }
    }

    return maxNesting;
  }
}

// Best practices checker
export class BestPracticesChecker {
  static checkReactBestPractices(code: string): CodeIssue[] {
    const issues: CodeIssue[] = [];

    // Check for missing key prop in lists
    if (code.includes('.map(') && !code.includes('key=')) {
      issues.push({
        type: 'warning',
        message: 'Missing key prop in list rendering',
        rule: 'react/missing-key',
        severity: 'medium',
      });
    }

    // Check for inline functions in JSX
    if (/onClick=\{.*=>\s*/.test(code)) {
      issues.push({
        type: 'warning',
        message: 'Avoid inline functions in JSX for better performance',
        rule: 'react/jsx-no-bind',
        severity: 'low',
      });
    }

    // Check for missing useCallback/useMemo
    if (code.includes('useEffect') && !code.includes('useCallback')) {
      const effectDeps = code.match(/useEffect\([^,]+,\s*\[[^\]]*\]/g);
      if (effectDeps && effectDeps.some(dep => dep.includes('function'))) {
        issues.push({
          type: 'info',
          message: 'Consider using useCallback for function dependencies in useEffect',
          rule: 'react-hooks/exhaustive-deps',
          severity: 'low',
        });
      }
    }

    // Check for direct state mutation
    if (/setState?\([^)]*\.[^)]*=/.test(code)) {
      issues.push({
        type: 'error',
        message: 'Direct state mutation detected',
        rule: 'react/no-direct-mutation-state',
        severity: 'high',
      });
    }

    return issues;
  }

  static checkTypeScriptBestPractices(code: string): CodeIssue[] {
    const issues: CodeIssue[] = [];

    // Check for any types
    if (code.includes(': any')) {
      issues.push({
        type: 'warning',
        message: 'Avoid using "any" type, use specific types instead',
        rule: 'typescript/no-explicit-any',
        severity: 'medium',
      });
    }

    // Check for non-null assertions
    if (code.includes('!')) {
      issues.push({
        type: 'warning',
        message: 'Non-null assertion operator used, ensure null safety',
        rule: 'typescript/no-non-null-assertion',
        severity: 'medium',
      });
    }

    // Check for missing return types
    const functionDeclarations = code.match(/function\s+\w+\s*\([^)]*\)\s*{/g) || [];
    if (functionDeclarations.length > 0 && !code.includes('):')) {
      issues.push({
        type: 'info',
        message: 'Consider adding explicit return types to functions',
        rule: 'typescript/explicit-function-return-type',
        severity: 'low',
      });
    }

    return issues;
  }

  static checkAccessibilityBestPractices(code: string): CodeIssue[] {
    const issues: CodeIssue[] = [];

    // Check for missing alt text on images
    if (code.includes('<img') && !code.includes('alt=')) {
      issues.push({
        type: 'error',
        message: 'Images must have alt text for accessibility',
        rule: 'jsx-a11y/alt-text',
        severity: 'high',
      });
    }

    // Check for missing labels on form inputs
    if (code.includes('<input') && !code.includes('aria-label') && !code.includes('id=')) {
      issues.push({
        type: 'warning',
        message: 'Form inputs should have associated labels',
        rule: 'jsx-a11y/label-has-associated-control',
        severity: 'medium',
      });
    }

    // Check for missing ARIA attributes on interactive elements
    if (code.includes('onClick') && !code.includes('role=') && !code.includes('<button')) {
      issues.push({
        type: 'warning',
        message: 'Interactive elements should have appropriate ARIA roles',
        rule: 'jsx-a11y/click-events-have-key-events',
        severity: 'medium',
      });
    }

    return issues;
  }
}

// Performance best practices
export class PerformanceChecker {
  static checkPerformanceIssues(code: string): CodeIssue[] {
    const issues: CodeIssue[] = [];

    // Check for missing React.memo
    if (code.includes('export default function') && !code.includes('React.memo')) {
      issues.push({
        type: 'info',
        message: 'Consider using React.memo for performance optimization',
        rule: 'performance/react-memo',
        severity: 'low',
      });
    }

    // Check for expensive operations in render
    if (code.includes('JSON.parse') || code.includes('JSON.stringify')) {
      issues.push({
        type: 'warning',
        message: 'Expensive operations in render, consider memoization',
        rule: 'performance/expensive-render',
        severity: 'medium',
      });
    }

    // Check for large bundle imports
    if (code.includes('import * as')) {
      issues.push({
        type: 'warning',
        message: 'Wildcard imports can increase bundle size',
        rule: 'performance/bundle-size',
        severity: 'low',
      });
    }

    return issues;
  }
}

// Code formatter utilities
export class CodeFormatter {
  static formatImports(code: string): string {
    // Sort and group imports
    const lines = code.split('\n');
    const imports: string[] = [];
    const otherLines: string[] = [];
    
    lines.forEach(line => {
      if (line.trim().startsWith('import')) {
        imports.push(line);
      } else {
        otherLines.push(line);
      }
    });

    // Sort imports: React first, then libraries, then local imports
    imports.sort((a, b) => {
      const aIsReact = a.includes('react');
      const bIsReact = b.includes('react');
      const aIsLocal = a.includes('./') || a.includes('../');
      const bIsLocal = b.includes('./') || b.includes('../');

      if (aIsReact && !bIsReact) return -1;
      if (!aIsReact && bIsReact) return 1;
      if (aIsLocal && !bIsLocal) return 1;
      if (!aIsLocal && bIsLocal) return -1;
      
      return a.localeCompare(b);
    });

    return [...imports, '', ...otherLines].join('\n');
  }

  static addMissingTypes(code: string): string {
    // Add basic type annotations where missing
    return code
      .replace(/function\s+(\w+)\s*\(/g, 'function $1(')
      .replace(/const\s+(\w+)\s*=\s*\(/g, 'const $1 = (');
  }
}

// Documentation checker
export class DocumentationChecker {
  static checkDocumentation(code: string): CodeIssue[] {
    const issues: CodeIssue[] = [];

    // Check for missing JSDoc comments on exported functions
    const exportedFunctions = code.match(/export\s+(function|const)\s+\w+/g) || [];
    if (exportedFunctions.length > 0 && !code.includes('/**')) {
      issues.push({
        type: 'info',
        message: 'Exported functions should have JSDoc documentation',
        rule: 'documentation/jsdoc',
        severity: 'low',
      });
    }

    // Check for missing prop types documentation
    if (code.includes('interface') && code.includes('Props') && !code.includes('/**')) {
      issues.push({
        type: 'info',
        message: 'Component props should be documented',
        rule: 'documentation/prop-types',
        severity: 'low',
      });
    }

    return issues;
  }
}

// Main code quality analyzer
export class CodeQualityAnalyzer {
  static analyzeCode(code: string, filename: string): ComponentAnalysis {
    const complexity = ComplexityAnalyzer.calculateCyclomaticComplexity(code);
    const linesOfCode = code.split('\n').filter(line => line.trim()).length;
    
    const issues: CodeIssue[] = [
      ...BestPracticesChecker.checkReactBestPractices(code),
      ...BestPracticesChecker.checkTypeScriptBestPractices(code),
      ...BestPracticesChecker.checkAccessibilityBestPractices(code),
      ...PerformanceChecker.checkPerformanceIssues(code),
      ...DocumentationChecker.checkDocumentation(code),
    ];

    const codeSmells = CodeSmellDetector.detectSmells(code);
    codeSmells.forEach(smell => {
      issues.push({
        type: 'warning',
        message: smell,
        rule: 'code-smell',
        severity: 'medium',
      });
    });

    return {
      name: filename,
      linesOfCode,
      complexity,
      dependencies: this.extractDependencies(code),
      props: this.extractProps(code),
      hooks: this.extractHooks(code),
      issues,
    };
  }

  private static extractDependencies(code: string): string[] {
    const imports = code.match(/import.*from\s+['"]([^'"]+)['"]/g) || [];
    return imports.map(imp => {
      const match = imp.match(/from\s+['"]([^'"]+)['"]/);
      return match ? match[1] : '';
    }).filter(Boolean);
  }

  private static extractProps(code: string): PropAnalysis[] {
    const interfaceMatch = code.match(/interface\s+\w*Props\s*{([^}]+)}/);
    if (!interfaceMatch) return [];

    const propsText = interfaceMatch[1];
    const propLines = propsText.split('\n').filter(line => line.trim());
    
    return propLines.map(line => {
      const trimmed = line.trim();
      const optional = trimmed.includes('?');
      const nameMatch = trimmed.match(/(\w+)\??:/);
      const typeMatch = trimmed.match(/:\s*([^;]+)/);
      
      return {
        name: nameMatch ? nameMatch[1] : '',
        type: typeMatch ? typeMatch[1].trim() : 'unknown',
        required: !optional,
      };
    }).filter(prop => prop.name);
  }

  private static extractHooks(code: string): string[] {
    const hookMatches = code.match(/use\w+\(/g) || [];
    return [...new Set(hookMatches.map(hook => hook.replace('(', '')))];
  }
}
