
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Key } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { ApiKeyInput } from "./ApiKeyInput";
import { ModelSelector } from "./ModelSelector";
import { ApiKeyActions } from "./ApiKeyActions";
import { useSettingsStore } from "@/stores/useSettingsStore";
import { useUIStore } from "@/stores/useUIStore";

export const ApiKeyManager: React.FC = () => {
  const { settings, updateSettings, clearApiKey } = useSettingsStore();
  const { showApiManager, setShowApiManager } = useUIStore();
  const [tempKey, setTempKey] = useState(settings.openRouterApiKey);
  const { toast } = useToast();

  React.useEffect(() => {
    setTempKey(settings.openRouterApiKey);
  }, [settings.openRouterApiKey]);

  const handleSave = () => {
    updateSettings({ openRouterApiKey: tempKey });
    toast({
      title: "API Key Saved",
      description: "Your OpenRouter API key has been saved successfully.",
    });
  };

  const handleDelete = () => {
    setTempKey("");
    clearApiKey();
  };
  return (
    <div className="mb-8">
      <Button 
        variant="outline" 
        size="lg"
        onClick={() => setShowApiManager(!showApiManager)} 
        className="w-full sm:w-auto mb-6 border-2 border-border/50 hover:border-primary/50 bg-background/50 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200"
      >
        <Key className="mr-2 h-5 w-5" />
        API Key Management
        {settings.openRouterApiKey && (
          <span className="ml-3 rounded-full bg-success/20 px-3 py-1 text-xs font-medium text-success border border-success/30">
            ✓ Connected
          </span>
        )}
      </Button>

      {showApiManager && (
        <Card className="shadow-xl border-0 bg-card/50 backdrop-blur-sm overflow-hidden animate-in slide-in-from-top-4 duration-300">
          <CardHeader className="bg-gradient-to-r from-primary/10 via-primary/5 to-purple-600/5 border-b border-border/50">
            <CardTitle className="text-xl font-semibold text-foreground flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Key className="h-5 w-5 text-primary" />
              </div>
              OpenRouter API Configuration
            </CardTitle>
            <CardDescription className="text-base text-muted-foreground leading-relaxed">
              Manage your OpenRouter API key and select your preferred AI model (including free options).
              <br />
              <span className="inline-flex items-center gap-2 mt-2">
                Get your key from{" "}
                <a 
                  href="https://openrouter.ai" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="inline-flex items-center gap-1 text-primary hover:text-primary-hover underline font-medium transition-colors"
                >
                  OpenRouter.ai
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
                  </svg>
                </a>
              </span>
              <br />
              <span className="text-sm text-muted-foreground/80 mt-2 block p-3 bg-muted/30 rounded-lg border border-border/30">
                🔒 Your API key is stored locally and persists until manually cleared.
              </span>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 p-8">
            <div className="p-4 bg-background/50 rounded-xl border border-border/50">
              <ApiKeyInput apiKey={tempKey} onChange={setTempKey} />
            </div>

            <div className="p-4 bg-background/50 rounded-xl border border-border/50">
              <ModelSelector
                selectedModel={settings.selectedModel}
                onModelChange={(model) => updateSettings({ selectedModel: model })}
                apiKey={settings.openRouterApiKey}
              />
            </div>

            <div className="pt-4 border-t border-border/50">
              <ApiKeyActions onSave={handleSave} onDelete={handleDelete} />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
